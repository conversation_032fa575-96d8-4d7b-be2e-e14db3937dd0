#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试upload_id集成功能
验证bid_doc_link_key和contract_link_key是否正确设置为upload_id
"""

from file_upload_service import upload_document_file
from utils.log_cfg import log


def test_upload_id_return():
    """测试upload_id的返回"""
    print("=" * 60)
    print("测试1: upload_id返回功能")
    print("=" * 60)
    
    try:
        # 创建测试文件内容
        test_content = b"Test file content for upload_id testing"
        source_id = "TEST_UPLOAD_ID_001"
        file_type = "招标文件"
        file_ext = ".txt"
        
        print(f"测试参数:")
        print(f"  文件内容大小: {len(test_content)} 字节")
        print(f"  源文档ID: {source_id}")
        print(f"  文件类型: {file_type}")
        print(f"  文件扩展名: {file_ext}")
        
        print("\n开始上传...")
        
        # 执行上传
        success, upload_id = upload_document_file(test_content, source_id, file_type, file_ext)
        
        if success:
            print("✓ 文件上传成功！")
            print(f"  上传ID: {upload_id}")
            print(f"  上传ID类型: {type(upload_id)}")
            print(f"  上传ID长度: {len(upload_id) if upload_id else 0}")
            
            # 验证upload_id不为空
            assert upload_id is not None and upload_id != "", "upload_id不应为空"
            print("✓ upload_id验证通过")
            
            return True, upload_id
        else:
            print("✗ 文件上传失败")
            return False, ""
            
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        return False, ""


def test_contract_upload_id():
    """测试合同文件的upload_id"""
    print("\n" + "=" * 60)
    print("测试2: 合同文件upload_id")
    print("=" * 60)
    
    try:
        # 创建合同文件内容
        contract_content = b"Test contract file content for upload_id testing"
        source_id = "TEST_CONTRACT_ID_001"
        file_type = "合同文件"
        file_ext = ".pdf"
        
        print(f"测试参数:")
        print(f"  文件内容大小: {len(contract_content)} 字节")
        print(f"  源文档ID: {source_id}")
        print(f"  文件类型: {file_type}")
        print(f"  文件扩展名: {file_ext}")
        
        print("\n开始上传...")
        
        # 执行上传
        success, upload_id = upload_document_file(contract_content, source_id, file_type, file_ext)
        
        if success:
            print("✓ 合同文件上传成功！")
            print(f"  上传ID: {upload_id}")
            print(f"  上传ID类型: {type(upload_id)}")
            print(f"  上传ID长度: {len(upload_id) if upload_id else 0}")
            
            # 验证upload_id不为空
            assert upload_id is not None and upload_id != "", "upload_id不应为空"
            print("✓ 合同文件upload_id验证通过")
            
            return True, upload_id
        else:
            print("✗ 合同文件上传失败")
            return False, ""
            
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        return False, ""


def test_upload_id_format():
    """测试upload_id的格式"""
    print("\n" + "=" * 60)
    print("测试3: upload_id格式验证")
    print("=" * 60)
    
    try:
        # 上传多个文件，检查upload_id格式
        test_cases = [
            ("招标文件", ".txt", b"Tender document content"),
            ("合同文件", ".docx", b"Contract document content"),
            ("招标文件", ".pdf", b"PDF tender document content"),
        ]
        
        upload_ids = []
        
        for i, (file_type, file_ext, content) in enumerate(test_cases):
            source_id = f"TEST_FORMAT_{i+1:03d}"
            
            print(f"\n测试用例 {i+1}: {file_type}{file_ext}")
            success, upload_id = upload_document_file(content, source_id, file_type, file_ext)
            
            if success:
                print(f"  ✓ 上传成功，upload_id: {upload_id}")
                upload_ids.append(upload_id)
                
                # 验证upload_id格式
                assert isinstance(upload_id, str), "upload_id应该是字符串类型"
                assert len(upload_id) > 0, "upload_id不应为空字符串"
                print(f"  ✓ upload_id格式验证通过")
            else:
                print(f"  ✗ 上传失败")
                return False
        
        # 验证upload_id的唯一性
        unique_ids = set(upload_ids)
        if len(unique_ids) == len(upload_ids):
            print(f"\n✓ 所有upload_id都是唯一的 ({len(upload_ids)}个)")
        else:
            print(f"\n⚠️  发现重复的upload_id: {len(upload_ids)}个ID中有{len(unique_ids)}个唯一")
        
        print(f"\nupload_id示例:")
        for i, upload_id in enumerate(upload_ids[:3]):  # 只显示前3个
            print(f"  {i+1}. {upload_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ upload_id格式测试失败: {e}")
        return False


def test_update_data_integration():
    """测试update_data中的字段设置逻辑"""
    print("\n" + "=" * 60)
    print("测试4: update_data字段设置逻辑")
    print("=" * 60)
    
    try:
        # 模拟item字典，包含upload_id
        test_item_tender = {
            "filename": "test_tender.pdf",
            "file_ext": ".pdf",
            "upload_id": "test_tender_upload_id_123"
        }
        
        test_item_contract = {
            "filename": "test_contract.docx", 
            "file_ext": ".docx",
            "upload_id": "test_contract_upload_id_456"
        }
        
        save_path = "/default/save/path"
        
        # 测试招标文件的bid_doc_link_key设置
        bid_doc_link_key = test_item_tender.get("upload_id", save_path)
        print(f"招标文件 bid_doc_link_key: {bid_doc_link_key}")
        assert bid_doc_link_key == "test_tender_upload_id_123", "bid_doc_link_key应该等于upload_id"
        print("✓ bid_doc_link_key设置正确")
        
        # 测试合同文件的contract_link_key设置
        contract_link_key = test_item_contract.get("upload_id", save_path)
        print(f"合同文件 contract_link_key: {contract_link_key}")
        assert contract_link_key == "test_contract_upload_id_456", "contract_link_key应该等于upload_id"
        print("✓ contract_link_key设置正确")
        
        # 测试没有upload_id时的回退逻辑
        test_item_no_upload = {
            "filename": "test_no_upload.pdf",
            "file_ext": ".pdf"
            # 没有upload_id字段
        }
        
        fallback_key = test_item_no_upload.get("upload_id", save_path)
        print(f"无upload_id时的回退值: {fallback_key}")
        assert fallback_key == save_path, "应该回退到save_path"
        print("✓ 回退逻辑正确")
        
        return True
        
    except Exception as e:
        print(f"✗ update_data字段设置测试失败: {e}")
        return False


def main():
    """运行所有upload_id集成测试"""
    print("开始upload_id集成测试...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("upload_id返回功能", test_upload_id_return()[0]))
    test_results.append(("合同文件upload_id", test_contract_upload_id()[0]))
    test_results.append(("upload_id格式验证", test_upload_id_format()))
    test_results.append(("update_data字段设置", test_update_data_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("upload_id集成测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有upload_id集成测试通过！")
        print("\n功能确认:")
        print("1. ✓ upload_document_file正确返回(success, upload_id)")
        print("2. ✓ upload_id格式正确且唯一")
        print("3. ✓ bid_doc_link_key = upload_id (招标文件)")
        print("4. ✓ contract_link_key = upload_id (合同文件)")
        print("5. ✓ 回退机制正常工作")
    else:
        print("⚠️  部分upload_id集成测试失败，请检查实现。")
    
    return passed == total


if __name__ == "__main__":
    main()
