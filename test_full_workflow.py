#!/usr/bin/env python3
"""
完整工作流程集成测试

测试新的两阶段附件处理工作流程：
1. Phase 1: 下载并上传所有附件，填充appendix_info
2. Phase 2: 分析文件类型，应用早期退出逻辑，更新特定字段
"""

import os
import sys
import json
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import DocumentAnalyzer, STANDARD_FIELDS

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def create_test_document():
    """创建测试文档"""
    return {
        "_id": "test_doc_full_workflow",
        "_source": {
            "title": "完整工作流程测试文档",
            "create_time": "2024-01-01 10:00:00",
            "category": "004",  # 中标公告
            "url": "http://test.com/doc/full_workflow",
            "content": "这是一个测试文档的内容...",
            "appendix": [
                {
                    "text": "项目招标文件.pdf",
                    "url": "http://test.com/download/tender_doc.pdf",
                },
                {
                    "text": "中标合同文件.docx",
                    "url": "http://test.com/download/contract_doc.docx",
                },
                {
                    "text": "技术规格书.pdf",
                    "url": "http://test.com/download/tech_spec.pdf",
                },
                {
                    "text": "投标须知.doc",
                    "url": "http://test.com/download/bidding_notice.doc",
                },
            ],
        },
    }


def mock_download_file(url):
    """模拟文件下载"""
    content_map = {
        "tender_doc.pdf": "招标文件内容：项目名称、技术要求、投标须知等...".encode(
            "utf-8"
        ),
        "contract_doc.docx": "合同文件内容：中标单位、合同金额、履约要求等...".encode(
            "utf-8"
        ),
        "tech_spec.pdf": "技术规格书内容：详细技术参数和配置要求...".encode("utf-8"),
        "bidding_notice.doc": "投标须知内容：投标流程、注意事项等...".encode("utf-8"),
    }

    for key, content in content_map.items():
        if key in url:
            return content
    return None


def mock_upload_attachment_file(file_content, source_id, original_filename, file_ext):
    """模拟文件上传"""
    upload_id = (
        f"upload_{hash(original_filename + source_id)}_{abs(hash(file_content))}"
    )
    return True, upload_id


def mock_upload_document_file(file_content, source_id, file_type, file_ext):
    """模拟招标/合同文件上传"""
    upload_id = f"{file_type}_{source_id}_{abs(hash(file_content))}"
    return True, upload_id


def mock_detect_file_type(filename, preview):
    """模拟文件类型检测"""
    filename_lower = filename.lower()
    if "招标" in filename or "tender" in filename_lower:
        return "招标文件"
    elif "合同" in filename or "contract" in filename_lower:
        return "合同文件"
    else:
        return "其他"


def mock_analyze_content(content, title):
    """模拟内容分析"""
    return {
        "bid_name": "测试标段",
        "prj_name": "测试项目",
        "object_name": "测试设备",
        "bidder_name": "测试中标单位",
        "bidder_price": 1000000.0,
    }


def mock_es_operations():
    """模拟ES操作"""
    mock_es = Mock()

    # 模拟搜索结果 - 返回测试文档
    test_doc = create_test_document()
    mock_es.search.return_value = {"hits": {"total": {"value": 1}, "hits": [test_doc]}}

    # 模拟插入操作
    mock_es.index.return_value = {"_id": "inserted_doc_id"}

    # 模拟其他ES操作
    mock_es.count.return_value = {"count": 1}

    return mock_es


def test_full_workflow():
    """测试完整的工作流程"""
    log.info("=" * 80)
    log.info("开始完整工作流程测试")
    log.info("=" * 80)

    # 创建模拟的ES客户端
    mock_es = mock_es_operations()

    # 创建DocumentAnalyzer实例
    analyzer = DocumentAnalyzer(
        es_client=mock_es,
        es_index_links="test_links_index",
        es_index_analysis="test_analysis_index",
        model_apikey="test_api_key",
        model_name="test_model",
        model_url="http://test.com/api",
        prompt_spec="test_prompt",
        enable_file_upload=True,
    )

    # 应用所有必要的mock
    with patch("analyse_appendix.download_file", side_effect=mock_download_file), patch(
        "analyse_appendix.upload_attachment_file",
        side_effect=mock_upload_attachment_file,
    ), patch(
        "analyse_appendix.upload_document_file", side_effect=mock_upload_document_file
    ), patch(
        "analyse_appendix.detect_file_type", side_effect=mock_detect_file_type
    ), patch(
        "analyse_appendix.get_file_extension_from_url"
    ) as mock_get_ext, patch(
        "analyse_appendix.get_file_info_from_content"
    ) as mock_get_info, patch(
        "analyse_appendix.parse_pdf"
    ) as mock_parse_pdf, patch(
        "analyse_appendix.parse_docx"
    ) as mock_parse_docx, patch(
        "analyse_appendix.parse_doc"
    ) as mock_parse_doc, patch(
        "analyse_appendix.extract_preview_text"
    ) as mock_extract_preview, patch.object(
        analyzer, "analyze_content", side_effect=mock_analyze_content
    ):

        # 设置mock返回值
        def mock_ext(url):
            if ".pdf" in url:
                return ".pdf"
            elif ".docx" in url:
                return ".docx"
            elif ".doc" in url:
                return ".doc"
            return ""

        mock_get_ext.side_effect = mock_ext

        mock_get_info.return_value = None
        mock_parse_pdf.return_value = "解析的PDF内容"
        mock_parse_docx.return_value = "解析的DOCX内容"
        mock_parse_doc.return_value = "解析的DOC内容"
        mock_extract_preview.return_value = "文件预览内容"

        # 执行处理
        log.info("开始执行文档处理...")
        try:
            analyzer.process_one_record()
            log.info("✓ 文档处理完成")
        except Exception as e:
            log.error(f"文档处理失败: {e}")
            import traceback

            traceback.print_exc()
            return False

    # 验证ES插入操作
    assert mock_es.index.called, "应该调用ES插入操作"

    # 获取插入的文档
    insert_calls = mock_es.index.call_args_list
    log.info(f"ES插入调用次数: {len(insert_calls)}")

    for i, call in enumerate(insert_calls):
        doc = call[1]["body"]  # 获取插入的文档内容

        log.info(f"\n--- 插入的文档 {i+1} ---")

        # 验证appendix_info字段
        assert "appendix_info" in doc, f"文档{i+1}缺少appendix_info字段"
        appendix_info = doc["appendix_info"]

        log.info(f"appendix_info包含 {len(appendix_info)} 个附件:")
        for j, attachment in enumerate(appendix_info):
            log.info(
                f"  附件{j+1}: {attachment['text']} - {attachment['file_ext']} - upload_id: {attachment.get('file_link_key', 'None')}"
            )

        # 验证所有附件都被处理
        assert len(appendix_info) == 4, f"期望4个附件，实际{len(appendix_info)}个"

        # 验证招标文件字段
        if doc.get("bid_doc_name"):
            log.info(f"✓ 招标文件字段已设置:")
            log.info(f"  bid_doc_name: {doc['bid_doc_name']}")
            log.info(f"  bid_doc_ext: {doc['bid_doc_ext']}")
            log.info(f"  bid_doc_link_out: {doc['bid_doc_link_out']}")
            log.info(f"  bid_doc_link_key: {doc['bid_doc_link_key']}")

        # 验证合同文件字段
        if doc.get("contract_name"):
            log.info(f"✓ 合同文件字段已设置:")
            log.info(f"  contract_name: {doc['contract_name']}")
            log.info(f"  contract_ext: {doc['contract_ext']}")
            log.info(f"  contract_link_out: {doc['contract_link_out']}")
            log.info(f"  contract_link_key: {doc['contract_link_key']}")

        # 验证标准字段
        for field in STANDARD_FIELDS:
            assert field in doc, f"文档{i+1}缺少标准字段: {field}"

    log.info("=" * 80)
    log.info("🎉 完整工作流程测试通过！")
    log.info("=" * 80)

    return True


def test_workflow_summary():
    """测试工作流程总结"""
    log.info("\n" + "=" * 80)
    log.info("新工作流程总结")
    log.info("=" * 80)

    log.info("Phase 1: 下载和上传所有附件")
    log.info("  ✓ 下载所有附件文件")
    log.info("  ✓ 上传所有附件到文件服务器")
    log.info("  ✓ 填充appendix_info字段（包含所有附件信息）")

    log.info("\nPhase 2: 文件类型分析和字段更新")
    log.info("  ✓ 分析文件类型（招标文件/合同文件/其他）")
    log.info("  ✓ 应用早期退出逻辑（找到一个招标文件后跳过其他招标文件）")
    log.info("  ✓ 更新特定字段（bid_doc_*, contract_*）")
    log.info("  ✓ 使用原始文件名（来自source_appendix.text）")

    log.info("\n关键改进:")
    log.info("  ✓ 所有附件都被上传（不仅限于招标/合同文件）")
    log.info("  ✓ appendix_info包含所有附件的完整信息")
    log.info("  ✓ 早期退出逻辑仅应用于分析阶段，不影响上传阶段")
    log.info("  ✓ 上传失败不会中断主要处理流程")


if __name__ == "__main__":
    try:
        success = test_full_workflow()
        test_workflow_summary()
        sys.exit(0 if success else 1)
    except Exception as e:
        log.error(f"测试失败: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
