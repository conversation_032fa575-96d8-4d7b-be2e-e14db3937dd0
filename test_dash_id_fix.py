#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试以-开头的文档ID的修复
"""

import subprocess
import sys
import os


def test_dash_id_commands():
    """测试以-开头的文档ID的各种命令格式"""
    print("=" * 80)
    print("测试以-开头的文档ID的命令格式")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "添加以-开头的ID（等号格式）",
            "cmd": ['python', 'analyse_appendix.py', 'blacklist', '--action', 'add', '--id=-1test_dash_001', '--title', '测试文档1', '--reason', '测试等号格式'],
            "should_succeed": True
        },
        {
            "name": "添加以-开头的ID（空格格式，应该失败）",
            "cmd": ['python', 'analyse_appendix.py', 'blacklist', '--action', 'add', '--id', '-1test_dash_002', '--title', '测试文档2'],
            "should_succeed": False
        },
        {
            "name": "添加普通ID（空格格式）",
            "cmd": ['python', 'analyse_appendix.py', 'blacklist', '--action', 'add', '--id', 'normal_test_003', '--title', '普通文档'],
            "should_succeed": True
        },
        {
            "name": "移除以-开头的ID（等号格式）",
            "cmd": ['python', 'analyse_appendix.py', 'blacklist', '--action', 'remove', '--id=-1test_dash_001'],
            "should_succeed": True
        },
        {
            "name": "移除普通ID",
            "cmd": ['python', 'analyse_appendix.py', 'blacklist', '--action', 'remove', '--id', 'normal_test_003'],
            "should_succeed": True
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   命令: {' '.join(test_case['cmd'])}")
        
        try:
            result = subprocess.run(
                test_case['cmd'], 
                capture_output=True, 
                text=True, 
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            success = result.returncode == 0
            
            if test_case['should_succeed']:
                if success:
                    print(f"   ✓ 测试通过：命令执行成功")
                    if result.stdout:
                        # 只显示关键输出
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if '✓' in line or '已将文档' in line or '已从黑名单中移除' in line:
                                print(f"     {line}")
                    results.append(True)
                else:
                    print(f"   ✗ 测试失败：命令应该成功但失败了")
                    if result.stderr:
                        print(f"     错误: {result.stderr.strip()}")
                    results.append(False)
            else:
                if not success:
                    print(f"   ✓ 测试通过：命令正确失败")
                    # 检查是否有有用的错误提示
                    if "提示：如果文档ID以-开头" in result.stdout:
                        print(f"     ✓ 包含有用的错误提示")
                    results.append(True)
                else:
                    print(f"   ✗ 测试失败：命令应该失败但成功了")
                    results.append(False)
                    
        except subprocess.TimeoutExpired:
            print(f"   ✗ 测试失败：命令超时")
            results.append(False)
        except Exception as e:
            print(f"   ✗ 测试失败：异常 - {e}")
            results.append(False)
    
    return results


def test_help_message():
    """测试帮助信息是否包含正确的提示"""
    print("\n" + "=" * 80)
    print("测试帮助信息")
    print("=" * 80)
    
    try:
        result = subprocess.run(
            ['python', 'analyse_appendix.py', 'blacklist', '--help'],
            capture_output=True,
            text=True,
            timeout=15,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            help_text = result.stdout
            if "对于以-开头的ID，请使用" in help_text:
                print("✓ 帮助信息包含正确的提示")
                return True
            else:
                print("✗ 帮助信息缺少关于-开头ID的提示")
                return False
        else:
            print("✗ 获取帮助信息失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试帮助信息失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("测试以-开头的文档ID修复")
    print("=" * 80)
    
    try:
        # 测试命令格式
        command_results = test_dash_id_commands()
        
        # 测试帮助信息
        help_result = test_help_message()
        
        # 汇总结果
        all_results = command_results + [help_result]
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "添加以-开头的ID（等号格式）",
            "添加以-开头的ID（空格格式，应该失败）",
            "添加普通ID（空格格式）",
            "移除以-开头的ID（等号格式）",
            "移除普通ID",
            "帮助信息测试"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, all_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(all_results)
        total_count = len(all_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！以-开头的文档ID问题已修复！")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
        # 显示使用建议
        print("\n" + "=" * 80)
        print("使用建议:")
        print("=" * 80)
        print("对于以-开头的文档ID，请使用以下格式：")
        print('  python analyse_appendix.py blacklist --action add --id="-1kMqpcBsUtJ06NfAuOo"')
        print('  python analyse_appendix.py blacklist --action remove --id="-1kMqpcBsUtJ06NfAuOo"')
        print("\n对于普通文档ID，可以使用任意格式：")
        print('  python analyse_appendix.py blacklist --action add --id normal_doc')
        print('  python analyse_appendix.py blacklist --action add --id=normal_doc')
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
