#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PDF解析功能
"""

import fitz
from analyse_appendix import parse_pdf, extract_preview_text


def test_fitz_functionality():
    """
    测试fitz库的基本功能
    """
    try:
        print("测试fitz库功能...")

        # 测试创建空文档
        doc = fitz.Document()
        print(f"✓ fitz.Document() 工作正常")
        doc.close()

        # 测试版本信息
        print(f"✓ PyMuPDF版本: {fitz.version}")

        return True

    except Exception as e:
        print(f"✗ fitz库测试失败: {e}")
        return False


def test_pdf_parsing_with_sample():
    """
    测试PDF解析功能（使用示例PDF）
    """
    try:
        print("\n测试PDF解析功能...")

        # 创建一个简单的测试PDF
        doc = fitz.Document()
        page = doc.new_page()

        # 添加一些文本
        text = "这是一个测试PDF文档\n包含中文内容\n用于测试解析功能"
        point = fitz.Point(50, 50)
        page.insert_text(point, text, fontsize=12)

        # 获取PDF字节内容
        pdf_bytes = doc.tobytes()
        doc.close()

        print(f"✓ 创建测试PDF成功，大小: {len(pdf_bytes)} 字节")

        # 测试parse_pdf函数
        extracted_text = parse_pdf(pdf_bytes)
        print(f"✓ 提取的文本: {extracted_text.strip()}")

        # 测试extract_preview_text函数
        preview_text = extract_preview_text(".pdf", pdf_bytes)
        print(f"✓ 预览文本: {preview_text.strip()}")

        return True

    except Exception as e:
        print(f"✗ PDF解析测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("PDF解析功能测试")
    print("=" * 60)

    # 测试fitz基本功能
    fitz_ok = test_fitz_functionality()

    if fitz_ok:
        # 测试PDF解析
        pdf_ok = test_pdf_parsing_with_sample()

        if pdf_ok:
            print("\n🎉 所有测试通过！PDF解析功能正常")
        else:
            print("\n❌ PDF解析测试失败")
    else:
        print("\n❌ fitz库测试失败")

    print("=" * 60)


if __name__ == "__main__":
    main()
