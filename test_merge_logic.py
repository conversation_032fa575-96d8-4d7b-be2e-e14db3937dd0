#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试融合逻辑
"""

import json
from analyse_appendix import batch_merge_analysis, merge_analysis

def test_merge_logic():
    """
    测试融合逻辑
    """
    print("测试融合逻辑")
    print("=" * 80)
    
    # 模拟主体解析结果（多个标的物）
    main_results = [
        {
            "object_name": "医疗设备A",
            "object_price": None,
            "bidder_name": None,
            "source_id": "test123",
            "prj_name": "测试项目",
            "bid_doc_name": None,
            "contract_name": None
        },
        {
            "object_name": "医疗设备B", 
            "object_price": None,
            "bidder_name": None,
            "source_id": "test123",
            "prj_name": "测试项目",
            "bid_doc_name": None,
            "contract_name": None
        },
        {
            "object_name": "医疗设备C",
            "object_price": None,
            "bidder_name": None,
            "source_id": "test123", 
            "prj_name": "测试项目",
            "bid_doc_name": None,
            "contract_name": None
        }
    ]
    
    # 模拟招标文件解析结果（只有一个）
    tender_results = [
        {
            "object_price": 100000.0,
            "bid_doc_name": "招标文件.pdf",
            "bid_doc_ext": "pdf",
            "bid_doc_link_out": "http://example.com/tender.pdf",
            "bid_doc_link_in": "/path/to/tender.pdf",
            "source_id": "test123"
        }
    ]
    
    # 模拟合同文件解析结果（只有一个）
    contract_results = [
        {
            "bidder_name": "某某公司",
            "bidder_price": 95000.0,
            "contract_name": "合同文件.pdf",
            "contract_ext": "pdf", 
            "contract_link_out": "http://example.com/contract.pdf",
            "contract_link_in": "/path/to/contract.pdf",
            "source_id": "test123"
        }
    ]
    
    print("输入数据:")
    print(f"主体解析结果数量: {len(main_results)}")
    print(f"招标文件解析结果数量: {len(tender_results)}")
    print(f"合同文件解析结果数量: {len(contract_results)}")
    print()
    
    print("主体解析结果:")
    for i, result in enumerate(main_results):
        print(f"  主体结果 {i+1}: object_name={result['object_name']}, object_price={result['object_price']}")
    print()
    
    print("招标文件解析结果:")
    for i, result in enumerate(tender_results):
        print(f"  招标文件结果 {i+1}: bid_doc_name={result['bid_doc_name']}, object_price={result['object_price']}")
    print()
    
    print("合同文件解析结果:")
    for i, result in enumerate(contract_results):
        print(f"  合同文件结果 {i+1}: contract_name={result['contract_name']}, bidder_name={result['bidder_name']}")
    print()
    
    # 执行融合
    print("执行融合...")
    final_results = batch_merge_analysis(main_results, tender_results, contract_results)
    
    print("=" * 80)
    print("融合结果:")
    print("=" * 80)
    
    for i, result in enumerate(final_results):
        print(f"融合结果 {i+1}:")
        print(f"  object_name: {result.get('object_name')}")
        print(f"  object_price: {result.get('object_price')} (来源: 招标文件)")
        print(f"  bidder_name: {result.get('bidder_name')} (来源: 合同文件)")
        print(f"  bid_doc_name: {result.get('bid_doc_name')} (来源: 招标文件)")
        print(f"  contract_name: {result.get('contract_name')} (来源: 合同文件)")
        print(f"  source_id: {result.get('source_id')}")
        print("-" * 40)
    
    print(f"总共生成 {len(final_results)} 个融合结果")
    
    # 验证每个结果都包含了招标文件和合同文件的信息
    print("\n验证结果:")
    all_have_tender_info = all(result.get('bid_doc_name') for result in final_results)
    all_have_contract_info = all(result.get('contract_name') for result in final_results)
    all_have_price_from_tender = all(result.get('object_price') == 100000.0 for result in final_results)
    all_have_bidder_from_contract = all(result.get('bidder_name') == '某某公司' for result in final_results)
    
    print(f"✓ 所有结果都包含招标文件信息: {all_have_tender_info}")
    print(f"✓ 所有结果都包含合同文件信息: {all_have_contract_info}")
    print(f"✓ 所有结果都从招标文件获取了价格: {all_have_price_from_tender}")
    print(f"✓ 所有结果都从合同文件获取了投标人: {all_have_bidder_from_contract}")
    
    if all([all_have_tender_info, all_have_contract_info, all_have_price_from_tender, all_have_bidder_from_contract]):
        print("\n🎉 融合逻辑测试通过！")
    else:
        print("\n❌ 融合逻辑测试失败！")

if __name__ == "__main__":
    test_merge_logic()
