#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传功能集成测试
测试analyse_appendix.py中的文件上传功能集成
"""

import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from analyse_appendix import DocumentAnalyzer, detect_file_type
from file_upload_service import upload_document_file
from utils.log_cfg import log


def test_detect_file_type():
    """测试文件类型检测功能"""
    print("=" * 60)
    print("测试1: 文件类型检测功能")
    print("=" * 60)

    test_cases = [
        ("招标文件.pdf", "这是一个招标文件的内容", "招标文件"),
        ("采购文件.docx", "采购文件内容", "招标文件"),
        ("合同.pdf", "这是合同文件的内容", "合同文件"),
        ("服务协议.docx", "服务协议内容", "合同文件"),
        ("其他文件.pdf", "这是其他类型的文件", "其他"),
    ]

    for filename, content, expected_type in test_cases:
        result = detect_file_type(filename, content)
        status = "✓" if result == expected_type else "✗"
        print(f"{status} {filename}: {result} (期望: {expected_type})")

    print("文件类型检测测试完成")
    return True


def test_document_analyzer_initialization():
    """测试DocumentAnalyzer的初始化，包括文件上传功能开关"""
    print("\n" + "=" * 60)
    print("测试2: DocumentAnalyzer初始化")
    print("=" * 60)

    try:
        # 模拟ES客户端
        mock_es = Mock()

        # 测试启用文件上传
        analyzer_with_upload = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt",
            enable_file_upload=True,
        )

        assert analyzer_with_upload.enable_file_upload == True
        print("✓ 启用文件上传的初始化成功")

        # 测试禁用文件上传
        analyzer_without_upload = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt",
            enable_file_upload=False,
        )

        assert analyzer_without_upload.enable_file_upload == False
        print("✓ 禁用文件上传的初始化成功")

        return True

    except Exception as e:
        print(f"✗ DocumentAnalyzer初始化测试失败: {e}")
        return False


def test_file_upload_integration():
    """测试文件上传功能的集成"""
    print("\n" + "=" * 60)
    print("测试3: 文件上传功能集成")
    print("=" * 60)

    try:
        # 创建测试文件内容
        test_content = b"Test bidding document content"

        # 测试上传功能
        with patch(
            "file_upload_service.file_upload_service.upload_file_with_retry"
        ) as mock_upload:
            mock_upload.return_value = (True, "test_upload_id_123")

            success, upload_id = upload_document_file(
                file_content=test_content,
                source_id="TEST_001",
                file_type="招标文件",
                file_ext=".txt",
            )

            assert success == True
            assert upload_id == "test_upload_id_123"
            mock_upload.assert_called_once()
            print("✓ 文件上传功能集成测试成功")

            # 验证调用参数
            call_args = mock_upload.call_args
            assert call_args[1]["object_name"] == "招标文件_TEST_001.txt"
            assert call_args[1]["content_type"] == "text/plain"
            print("✓ 上传参数验证成功")

        return True

    except Exception as e:
        print(f"✗ 文件上传集成测试失败: {e}")
        return False


def test_upload_control_logic():
    """测试上传控制逻辑"""
    print("\n" + "=" * 60)
    print("测试4: 上传控制逻辑")
    print("=" * 60)

    try:
        # 模拟文档数据
        mock_doc = {
            "_id": "test_doc_001",
            "_source": {
                "title": "测试文档",
                "appendix": [
                    {"text": "招标文件.pdf", "url": "http://example.com/tender.pdf"}
                ],
            },
        }

        # 模拟文件项
        mock_item = {
            "filename": "招标文件.pdf",
            "content": "招标文件内容",
            "file_ext": ".pdf",
            "file_bytes": b"test file content",
        }

        # 测试启用上传的情况
        with patch("file_upload_service.upload_document_file") as mock_upload:
            mock_upload.return_value = (True, "test_upload_id_123")

            # 模拟启用上传的分析器
            mock_analyzer = Mock()
            mock_analyzer.enable_file_upload = True

            # 模拟上传逻辑
            file_type = "招标文件"
            if (
                file_type in ["招标文件", "合同文件"]
                and mock_analyzer.enable_file_upload
            ):
                source_id = mock_doc["_id"]
                file_ext = mock_item.get("file_ext", "")
                file_bytes = mock_item.get("file_bytes", b"")

                if file_bytes and file_ext:
                    # 导入并调用模拟的函数
                    from file_upload_service import upload_document_file

                    upload_success, upload_id = upload_document_file(
                        file_content=file_bytes,
                        source_id=source_id,
                        file_type=file_type,
                        file_ext=file_ext,
                    )

                    assert upload_success == True
                    mock_upload.assert_called_once()
                    print("✓ 启用上传时的控制逻辑正确")

        # 测试禁用上传的情况
        mock_analyzer.enable_file_upload = False

        with patch("file_upload_service.upload_document_file") as mock_upload:
            # 模拟禁用上传的逻辑
            if (
                file_type in ["招标文件", "合同文件"]
                and mock_analyzer.enable_file_upload
            ):
                # 这个分支不应该执行
                upload_document_file(
                    file_content=mock_item.get("file_bytes", b""),
                    source_id=mock_doc["_id"],
                    file_type=file_type,
                    file_ext=mock_item.get("file_ext", ""),
                )

            # 验证没有调用上传函数
            mock_upload.assert_not_called()
            print("✓ 禁用上传时的控制逻辑正确")

        return True

    except Exception as e:
        print(f"✗ 上传控制逻辑测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试5: 错误处理")
    print("=" * 60)

    try:
        # 测试上传失败的情况
        with patch(
            "file_upload_service.file_upload_service.upload_file_with_retry"
        ) as mock_upload:
            mock_upload.return_value = (False, "")

            success, upload_id = upload_document_file(
                file_content=b"test content",
                source_id="TEST_002",
                file_type="合同文件",
                file_ext=".pdf",
            )

            assert success == False
            assert upload_id == ""
            print("✓ 上传失败处理正确")

        # 测试异常处理
        with patch(
            "file_upload_service.file_upload_service.upload_file_with_retry"
        ) as mock_upload:
            mock_upload.side_effect = Exception("网络错误")

            success, upload_id = upload_document_file(
                file_content=b"test content",
                source_id="TEST_003",
                file_type="招标文件",
                file_ext=".docx",
            )

            assert success == False
            assert upload_id == ""
            print("✓ 异常处理正确")

        return True

    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def main():
    """运行所有集成测试"""
    print("开始文件上传功能集成测试...")

    test_results = []

    # 运行各项测试
    test_results.append(("文件类型检测", test_detect_file_type()))
    test_results.append(
        ("DocumentAnalyzer初始化", test_document_analyzer_initialization())
    )
    test_results.append(("文件上传功能集成", test_file_upload_integration()))
    test_results.append(("上传控制逻辑", test_upload_control_logic()))
    test_results.append(("错误处理", test_error_handling()))

    # 输出测试结果
    print("\n" + "=" * 60)
    print("集成测试结果汇总")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 所有集成测试通过！文件上传功能已成功集成。")
        print("\n集成要点:")
        print("1. ✓ 文件类型检测正常工作")
        print("2. ✓ DocumentAnalyzer支持文件上传开关")
        print("3. ✓ 文件上传功能正确集成")
        print("4. ✓ 上传控制逻辑按预期工作")
        print("5. ✓ 错误处理机制完善")
    else:
        print("⚠️  部分集成测试失败，请检查代码集成。")

    return passed == total


if __name__ == "__main__":
    main()
