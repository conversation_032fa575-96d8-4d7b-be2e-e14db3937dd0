#!/usr/bin/env python3
"""
修复markersweb_attachment_analysis_v3索引中的标的物总价字段数据不一致问题

该脚本的功能：
1. 查询markersweb_attachment_analysis_v3索引中需要修复的文档
2. 修复逻辑：当文档同时满足以下条件时，重新计算并更新object_total_price字段
   - object_price字段存在且不为空（不是null、""、0）
   - object_amount字段存在且不为空（不是null、""、0）
   - 计算公式：object_total_price = object_price × object_amount
"""

import argparse
import sys
from typing import List, Dict, Optional, Union
from dotenv import load_dotenv
import os
from decimal import Decimal, InvalidOperation

from es_deal import init_es_client, search_documents
from utils.log_cfg import log


def is_valid_numeric_value(value: Union[str, int, float, None]) -> bool:
    """
    检查值是否为有效的数值（不为null、""、0）

    Args:
        value: 要检查的值

    Returns:
        bool: 如果值有效则返回True，否则返回False
    """
    if value is None:
        return False

    if isinstance(value, str):
        if value.strip() == "":
            return False
        try:
            num_value = float(value)
            return num_value != 0
        except (ValueError, TypeError):
            return False

    if isinstance(value, (int, float)):
        return value != 0

    return False


def convert_to_numeric(value: Union[str, int, float, None]) -> Optional[float]:
    """
    将值转换为数值类型

    Args:
        value: 要转换的值

    Returns:
        float: 转换后的数值，如果转换失败则返回None
    """
    if value is None:
        return None

    try:
        if isinstance(value, str):
            value = value.strip()
            if value == "":
                return None
        return float(value)
    except (ValueError, TypeError):
        return None


def calculate_total_price(
    price: Union[str, int, float], amount: Union[str, int, float]
) -> Optional[float]:
    """
    计算总价

    Args:
        price: 单价
        amount: 数量

    Returns:
        float: 计算后的总价，如果计算失败则返回None
    """
    try:
        price_num = convert_to_numeric(price)
        amount_num = convert_to_numeric(amount)

        if price_num is None or amount_num is None:
            return None

        # 使用Decimal进行精确计算，避免浮点数精度问题
        price_decimal = Decimal(str(price_num))
        amount_decimal = Decimal(str(amount_num))
        total_decimal = price_decimal * amount_decimal

        return float(total_decimal)

    except (InvalidOperation, ValueError, TypeError) as e:
        log.error(f"计算总价失败: price={price}, amount={amount}, error={e}")
        return None


def get_documents_need_fix(
    es_client, index_name: str, batch_size: int = 100
) -> List[Dict]:
    """
    获取需要修复的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小

    Returns:
        需要修复的文档列表
    """
    try:
        # 查询同时具有object_price和object_amount字段的文档
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "object_price"}},
                        {"exists": {"field": "object_amount"}},
                    ]
                }
            },
            "size": batch_size,
            "_source": ["object_price", "object_amount", "object_total_price"],
        }

        result = search_documents(es_client, index_name, query=query)

        if result and result.get("hits", {}).get("hits"):
            documents = []
            for hit in result["hits"]["hits"]:
                source = hit["_source"]
                doc_info = {
                    "doc_id": hit["_id"],
                    "object_price": source.get("object_price"),
                    "object_amount": source.get("object_amount"),
                    "object_total_price": source.get("object_total_price"),
                }
                documents.append(doc_info)
            return documents
        else:
            return []

    except Exception as e:
        log.error(f"查询需要修复的文档失败: {e}")
        return []


def get_total_documents_count(es_client, index_name: str) -> int:
    """
    获取同时具有object_price和object_amount字段的文档总数

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称

    Returns:
        文档总数
    """
    try:
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "object_price"}},
                        {"exists": {"field": "object_amount"}},
                    ]
                }
            }
        }

        result = es_client.count(index=index_name, body=query)
        return result.get("count", 0)

    except Exception as e:
        log.error(f"获取文档总数失败: {e}")
        return 0


def bulk_update_total_price(es_client, index_name: str, updates: List[Dict]):
    """
    批量更新文档的object_total_price字段

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        updates: 更新操作列表，格式为[{"doc_id": "xxx", "object_total_price": xxx}, ...]
    """
    if not updates:
        return {"success": 0, "failed": 0}

    try:
        # 构建批量更新请求
        body = []
        for update in updates:
            # 更新操作头
            body.append({"update": {"_index": index_name, "_id": update["doc_id"]}})
            # 更新内容
            body.append({"doc": {"object_total_price": update["object_total_price"]}})

        # 执行批量更新
        response = es_client.bulk(body=body)

        # 检查结果
        success_count = 0
        error_count = 0

        for item in response["items"]:
            if "update" in item:
                if item["update"].get("status") in [200, 201]:
                    success_count += 1
                else:
                    error_count += 1
                    log.error(f"更新失败: {item['update']}")

        log.info(f"批量更新完成 - 成功: {success_count}, 失败: {error_count}")
        return {"success": success_count, "failed": error_count}

    except Exception as e:
        log.error(f"批量更新失败: {e}")
        return {"success": 0, "failed": len(updates)}


def process_documents_fix(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理需要修复的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info(f"开始处理标的物总价字段修复...")
        log.info(f"索引: {index_name}")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 先获取总数统计
        total_docs = get_total_documents_count(es_client, index_name)
        log.info(
            f"索引中同时具有object_price和object_amount字段的文档总数: {total_docs}"
        )

        if total_docs == 0:
            log.info("没有找到需要检查的文档")
            return

        # 获取需要修复的文档
        documents = get_documents_need_fix(es_client, index_name, batch_size)

        if not documents:
            log.info("没有找到需要检查的文档")
            return

        log.info(f"本批次检查 {len(documents)} 个文档（总共 {total_docs} 个）")

        # 分析需要修复的文档
        updates = []
        skip_count = 0
        invalid_data_count = 0
        already_correct_count = 0

        for i, doc in enumerate(documents, 1):
            doc_id = doc["doc_id"]
            price = doc["object_price"]
            amount = doc["object_amount"]
            current_total = doc["object_total_price"]

            # 检查price和amount是否有效
            if not is_valid_numeric_value(price) or not is_valid_numeric_value(amount):
                skip_count += 1
                log.debug(
                    f"[{i}/{len(documents)}] 跳过文档 {doc_id}: price={price}, amount={amount} (无效数据)"
                )
                continue

            # 计算新的总价
            calculated_total = calculate_total_price(price, amount)

            if calculated_total is None:
                invalid_data_count += 1
                log.warning(
                    f"[{i}/{len(documents)}] 无法计算总价 {doc_id}: price={price}, amount={amount}"
                )
                continue

            # 检查是否需要更新（允许小的浮点数误差）
            if current_total is not None:
                current_total_num = convert_to_numeric(current_total)
                if (
                    current_total_num is not None
                    and abs(calculated_total - current_total_num) < 0.01
                ):
                    already_correct_count += 1
                    log.debug(
                        f"[{i}/{len(documents)}] 文档 {doc_id} 总价已正确: {current_total}"
                    )
                    continue

            updates.append({"doc_id": doc_id, "object_total_price": calculated_total})

            log.info(
                f"[{i}/{len(documents)}] 准备修复文档 {doc_id}: "
                f"price={price} × amount={amount} = {calculated_total} "
                f"(当前值: {current_total})"
            )

        # 输出统计信息
        log.info("=" * 50)
        log.info("处理统计:")
        log.info(f"  本批次文档总数: {len(documents)}")
        log.info(f"  需要修复的文档: {len(updates)}")
        log.info(f"  已经正确的文档: {already_correct_count}")
        log.info(f"  数据无效跳过的文档: {skip_count}")
        log.info(f"  计算失败的文档: {invalid_data_count}")
        log.info("=" * 50)

        if updates and not dry_run:
            # 执行批量更新
            result = bulk_update_total_price(es_client, index_name, updates)
            log.info(f"修复完成 - 成功: {result['success']}, 失败: {result['failed']}")
        elif dry_run:
            log.info("试运行模式，未执行实际更新")
        else:
            log.info("没有需要修复的文档")

    except Exception as e:
        log.error(f"处理过程中发生错误: {e}")


def process_all_documents_fix(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理所有需要修复的文档（分批处理）

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info("开始处理所有需要修复的文档...")

        # 获取总数
        total_docs = get_total_documents_count(es_client, index_name)
        log.info(
            f"索引中同时具有object_price和object_amount字段的文档总数: {total_docs}"
        )

        if total_docs == 0:
            log.info("没有找到需要检查的文档")
            return

        processed_count = 0
        batch_num = 1
        total_fixed = 0
        total_already_correct = 0
        total_skipped = 0
        total_failed = 0

        # 使用scroll API处理大量数据
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "object_price"}},
                        {"exists": {"field": "object_amount"}},
                    ]
                }
            },
            "_source": ["object_price", "object_amount", "object_total_price"],
        }

        # 初始化scroll
        scroll_response = es_client.search(
            index=index_name, body=query, scroll="5m", size=batch_size
        )

        scroll_id = scroll_response.get("_scroll_id")
        hits = scroll_response["hits"]["hits"]

        while hits:
            log.info(f"\n{'='*60}")
            log.info(f"开始处理第 {batch_num} 批次")
            log.info(f"已处理: {processed_count}/{total_docs}")
            log.info(f"{'='*60}")

            # 处理当前批次
            documents = []
            for hit in hits:
                source = hit["_source"]
                doc_info = {
                    "doc_id": hit["_id"],
                    "object_price": source.get("object_price"),
                    "object_amount": source.get("object_amount"),
                    "object_total_price": source.get("object_total_price"),
                }
                documents.append(doc_info)

            # 分析和修复当前批次
            batch_stats = process_batch_documents(
                es_client, index_name, documents, dry_run
            )

            # 累计统计
            total_fixed += batch_stats["fixed"]
            total_already_correct += batch_stats["already_correct"]
            total_skipped += batch_stats["skipped"]
            total_failed += batch_stats["failed"]

            processed_count += len(hits)
            batch_num += 1

            # 获取下一批数据
            scroll_response = es_client.scroll(scroll_id=scroll_id, scroll="5m")
            hits = scroll_response["hits"]["hits"]

        # 清理scroll上下文
        if scroll_id:
            es_client.clear_scroll(scroll_id=scroll_id)

        # 输出最终统计
        log.info(f"\n{'='*60}")
        log.info("最终统计:")
        log.info(f"  处理文档总数: {processed_count}")
        log.info(f"  修复的文档: {total_fixed}")
        log.info(f"  已经正确的文档: {total_already_correct}")
        log.info(f"  跳过的文档: {total_skipped}")
        log.info(f"  失败的文档: {total_failed}")
        log.info(f"  共处理 {batch_num - 1} 个批次")
        log.info(f"{'='*60}")

    except Exception as e:
        log.error(f"处理所有文档时发生错误: {e}")


def process_batch_documents(
    es_client, index_name: str, documents: List[Dict], dry_run: bool = False
) -> Dict:
    """
    处理一个批次的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        documents: 文档列表
        dry_run: 是否为试运行模式

    Returns:
        Dict: 处理统计信息
    """
    updates = []
    skip_count = 0
    invalid_data_count = 0
    already_correct_count = 0

    for i, doc in enumerate(documents, 1):
        doc_id = doc["doc_id"]
        price = doc["object_price"]
        amount = doc["object_amount"]
        current_total = doc["object_total_price"]

        # 检查price和amount是否有效
        if not is_valid_numeric_value(price) or not is_valid_numeric_value(amount):
            skip_count += 1
            continue

        # 计算新的总价
        calculated_total = calculate_total_price(price, amount)

        if calculated_total is None:
            invalid_data_count += 1
            continue

        # 检查是否需要更新（允许小的浮点数误差）
        if current_total is not None:
            current_total_num = convert_to_numeric(current_total)
            if (
                current_total_num is not None
                and abs(calculated_total - current_total_num) < 0.01
            ):
                already_correct_count += 1
                continue

        updates.append({"doc_id": doc_id, "object_total_price": calculated_total})

        log.info(
            f"[{i}/{len(documents)}] 准备修复文档 {doc_id}: "
            f"price={price} × amount={amount} = {calculated_total} "
            f"(当前值: {current_total})"
        )

    # 执行更新
    update_result = {"success": 0, "failed": 0}
    if updates and not dry_run:
        update_result = bulk_update_total_price(es_client, index_name, updates)
    elif dry_run and updates:
        log.info(f"试运行模式: 将修复 {len(updates)} 个文档")

    return {
        "fixed": update_result["success"],
        "already_correct": already_correct_count,
        "skipped": skip_count + invalid_data_count,
        "failed": update_result["failed"],
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="修复markersweb_attachment_analysis_v3索引中的标的物总价字段数据不一致问题"
    )
    parser.add_argument(
        "--index",
        type=str,
        help="目标索引名称（默认markersweb_attachment_analysis_v3）",
    )
    parser.add_argument(
        "--batch-size", type=int, default=100, help="批处理大小（默认100）"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="试运行模式，不执行实际更新"
    )
    parser.add_argument(
        "--all", action="store_true", help="处理所有需要修复的文档（分批处理）"
    )

    args = parser.parse_args()

    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()

        # 确定索引名称
        if args.index:
            index_name = args.index
        else:
            index_name = "markersweb_attachment_analysis_v3"  # 默认索引名称

        log.info(f"目标索引: {index_name}")
        log.info(f"批处理大小: {args.batch_size}")
        log.info(f"试运行模式: {args.dry_run}")
        log.info(f"处理所有文档: {args.all}")

        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            sys.exit(1)

        # 处理文档修复
        if args.all:
            process_all_documents_fix(es, index_name, args.batch_size, args.dry_run)
        else:
            process_documents_fix(es, index_name, args.batch_size, args.dry_run)

    except KeyboardInterrupt:
        log.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
