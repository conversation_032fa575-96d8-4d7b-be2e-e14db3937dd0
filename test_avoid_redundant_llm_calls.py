#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试避免冗余LLM调用的优化
验证当已有解析结果时不会重复调用LLM API
"""

from analyse_appendix import intelligent_merge_analysis
from utils.log_cfg import log
from unittest.mock import patch


def test_avoid_redundant_llm_calls():
    """测试避免冗余LLM调用"""
    print("=" * 60)
    print("测试1: 避免冗余LLM调用")
    print("=" * 60)
    
    # 模拟主体解析结果（有一些空缺字段）
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "prj_number": "YY2025001",
            "tenderee": "某市人民医院",
            "object_name": "CT设备",
            # 以下字段为空，需要智能融合
            "fiscal_delegation_number": None,
            "prj_approval_authority": None,
            "object_oem": None,
            "bid_cancelled_flag": None,
            "bid_cancelled_reason": None,
            "bidder_contract_config_param": None,
        }
    ]
    
    # 模拟招标文件解析结果（已经解析过，但没有包含所有需要的字段）
    tender_results = [
        {
            "prj_name": "医院设备采购项目",
            "tenderee": "某市人民医院",
            "object_name": "CT设备",
            # 注意：故意不包含主体结果中缺失的字段
            # 这模拟了招标文件已经解析过但没有这些字段的情况
        }
    ]
    
    # 模拟合同文件解析结果（已经解析过，但没有包含所有需要的字段）
    contract_results = [
        {
            "bidder_name": "医疗设备有限公司",
            "bidder_price": 2500000.0,
            # 注意：故意不包含bidder_contract_config_param
        }
    ]
    
    # 模拟文档内容（提供了，但不应该被使用）
    tender_content = """
    招标公告
    项目名称：医院设备采购项目
    财政委托编号：2025(JKJ）143
    项目审批机关：新疆君凯杰工程项目管理有限公司
    """
    
    contract_content = """
    采购合同
    中标单位：医疗设备有限公司
    中标金额：2,500,000元
    合同配置参数：标准配置
    """
    
    print("测试场景：")
    print("- 主体结果有6个空缺字段")
    print("- 招标文件已解析，但不包含这些字段")
    print("- 合同文件已解析，但不包含这些字段")
    print("- 提供了文档内容和模型配置")
    print("- 期望：不调用LLM API，因为文件已经解析过")
    
    # 使用Mock来监控LLM调用
    with patch('analyse_appendix.extract_fields_from_content') as mock_extract:
        mock_extract.return_value = {}  # 返回空字典
        
        # 执行智能融合
        merged_results = intelligent_merge_analysis(
            main_list=main_results,
            tender_content=tender_content,
            contract_content=contract_content,
            tender_list=tender_results,
            contract_list=contract_results,
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url"
        )
        
        # 检查是否调用了LLM
        llm_call_count = mock_extract.call_count
        print(f"\nLLM API调用次数: {llm_call_count}")
        
        if llm_call_count == 0:
            print("✅ 成功避免了冗余的LLM调用")
            return True
        else:
            print("❌ 仍然进行了不必要的LLM调用")
            print("调用详情:")
            for i, call in enumerate(mock_extract.call_args_list):
                args, kwargs = call
                print(f"  调用 {i+1}: 提取字段 {args[1] if len(args) > 1 else 'N/A'}")
            return False


def test_llm_call_when_no_parsed_results():
    """测试当没有解析结果时应该调用LLM"""
    print("\n" + "=" * 60)
    print("测试2: 没有解析结果时应该调用LLM")
    print("=" * 60)
    
    # 模拟主体解析结果（有空缺字段）
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "fiscal_delegation_number": None,
            "prj_approval_authority": None,
        }
    ]
    
    # 模拟文档内容
    tender_content = """
    招标公告
    项目名称：医院设备采购项目
    财政委托编号：2025(JKJ）143
    项目审批机关：新疆君凯杰工程项目管理有限公司
    """
    
    print("测试场景：")
    print("- 主体结果有2个空缺字段")
    print("- 没有提供招标文件解析结果")
    print("- 提供了文档内容和模型配置")
    print("- 期望：应该调用LLM API提取字段")
    
    # 使用Mock来监控LLM调用
    with patch('analyse_appendix.extract_fields_from_content') as mock_extract:
        mock_extract.return_value = {
            "fiscal_delegation_number": "2025(JKJ）143",
            "prj_approval_authority": "新疆君凯杰工程项目管理有限公司"
        }
        
        # 执行智能融合（注意：不提供tender_list和contract_list）
        merged_results = intelligent_merge_analysis(
            main_list=main_results,
            tender_content=tender_content,
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url"
        )
        
        # 检查是否调用了LLM
        llm_call_count = mock_extract.call_count
        print(f"\nLLM API调用次数: {llm_call_count}")
        
        if llm_call_count > 0:
            print("✅ 正确调用了LLM API进行字段提取")
            
            # 检查融合结果
            merged_result = merged_results[0]
            if (merged_result.get("fiscal_delegation_number") == "2025(JKJ）143" and
                merged_result.get("prj_approval_authority") == "新疆君凯杰工程项目管理有限公司"):
                print("✅ 字段提取和融合成功")
                return True
            else:
                print("❌ 字段融合失败")
                return False
        else:
            print("❌ 应该调用LLM但没有调用")
            return False


def test_partial_parsed_results():
    """测试部分解析结果的情况"""
    print("\n" + "=" * 60)
    print("测试3: 部分解析结果的情况")
    print("=" * 60)
    
    # 模拟主体解析结果
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "fiscal_delegation_number": None,
            "prj_approval_authority": None,
            "object_oem": None,
        }
    ]
    
    # 模拟招标文件解析结果（只包含部分字段）
    tender_results = [
        {
            "prj_name": "医院设备采购项目",
            "fiscal_delegation_number": "2025(JKJ）143",
            # 注意：不包含prj_approval_authority和object_oem
        }
    ]
    
    # 模拟文档内容
    tender_content = """
    招标公告
    项目名称：医院设备采购项目
    财政委托编号：2025(JKJ）143
    项目审批机关：新疆君凯杰工程项目管理有限公司
    """
    
    print("测试场景：")
    print("- 主体结果有3个空缺字段")
    print("- 招标文件解析结果包含1个字段")
    print("- 还有2个字段缺失")
    print("- 期望：使用解析结果中的字段，跳过LLM调用")
    
    # 使用Mock来监控LLM调用
    with patch('analyse_appendix.extract_fields_from_content') as mock_extract:
        mock_extract.return_value = {}
        
        # 执行智能融合
        merged_results = intelligent_merge_analysis(
            main_list=main_results,
            tender_content=tender_content,
            tender_list=tender_results,
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url"
        )
        
        # 检查结果
        merged_result = merged_results[0]
        llm_call_count = mock_extract.call_count
        
        print(f"\nLLM API调用次数: {llm_call_count}")
        print(f"融合结果:")
        print(f"  fiscal_delegation_number: {merged_result.get('fiscal_delegation_number')}")
        print(f"  prj_approval_authority: {merged_result.get('prj_approval_authority')}")
        print(f"  object_oem: {merged_result.get('object_oem')}")
        
        # 验证：应该获取到解析结果中的字段，但不调用LLM
        if (merged_result.get('fiscal_delegation_number') == "2025(JKJ）143" and
            llm_call_count == 0):
            print("✅ 正确使用了解析结果，避免了LLM调用")
            return True
        else:
            print("❌ 逻辑不符合预期")
            return False


def main():
    """运行所有测试"""
    print("开始测试避免冗余LLM调用的优化...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("避免冗余LLM调用", test_avoid_redundant_llm_calls()))
    test_results.append(("没有解析结果时调用LLM", test_llm_call_when_no_parsed_results()))
    test_results.append(("部分解析结果的情况", test_partial_parsed_results()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("避免冗余LLM调用测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有避免冗余LLM调用测试通过！")
        print("\n优化效果:")
        print("1. ✓ 当文件已解析时，不会重复调用LLM API")
        print("2. ✓ 当没有解析结果时，正确调用LLM API")
        print("3. ✓ 部分解析结果时，只使用已有数据")
        print("4. ✓ 大幅减少不必要的API调用和成本")
    else:
        print("⚠️  部分测试失败，请检查优化实现。")
    
    return passed == total


if __name__ == "__main__":
    main()
