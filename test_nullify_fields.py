#!/usr/bin/env python3
"""
测试nullify_fields_for_announcement_001功能
"""

import sys
import os
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from es_deal import init_es_client, nullify_fields_for_announcement_001, search_documents
from utils.log_cfg import log


def test_nullify_functionality():
    """测试字段置空功能"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()
        
        # 获取索引名称
        index_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
        if not index_name:
            log.error("环境变量ES_INDEX_ANALYSIS_ALIAS未设置")
            return
        
        log.info(f"测试索引: {index_name}")
        
        # 1. 先查询公告类型为"001"的文档数量
        log.info("=" * 60)
        log.info("步骤1: 查询公告类型为'001'的文档")
        log.info("=" * 60)
        
        query_001 = {
            "query": {
                "term": {
                    "source_category": "001"
                }
            },
            "size": 0  # 只要总数，不要具体文档
        }
        
        result = search_documents(es, index_name, query=query_001)
        total_001_docs = result["hits"]["total"]["value"]
        log.info(f"找到 {total_001_docs} 个公告类型为'001'的文档")
        
        if total_001_docs == 0:
            log.info("没有找到公告类型为'001'的文档，无法进行测试")
            return
        
        # 2. 查看前几个文档的字段情况
        log.info("\n" + "=" * 60)
        log.info("步骤2: 查看前5个文档的字段情况")
        log.info("=" * 60)
        
        query_sample = {
            "query": {
                "term": {
                    "source_category": "001"
                }
            },
            "size": 5,
            "_source": [
                "_id", "source_category", "prj_name",
                "bidder_price", "bidder_name", "bidder_contact_person",
                "bid_cancelled_flag", "contract_name"
            ]
        }
        
        sample_result = search_documents(es, index_name, query=query_sample)
        
        log.info("样本文档字段情况:")
        for i, hit in enumerate(sample_result["hits"]["hits"], 1):
            doc_id = hit["_id"]
            source = hit["_source"]
            log.info(f"\n文档 {i} (ID: {doc_id}):")
            log.info(f"  - source_category: {source.get('source_category')}")
            log.info(f"  - prj_name: {source.get('prj_name', 'N/A')}")
            log.info(f"  - bidder_price: {source.get('bidder_price')}")
            log.info(f"  - bidder_name: {source.get('bidder_name')}")
            log.info(f"  - bidder_contact_person: {source.get('bidder_contact_person')}")
            log.info(f"  - bid_cancelled_flag: {source.get('bid_cancelled_flag')}")
            log.info(f"  - contract_name: {source.get('contract_name')}")
        
        # 3. 执行试运行
        log.info("\n" + "=" * 60)
        log.info("步骤3: 执行试运行（dry_run=True）")
        log.info("=" * 60)
        
        dry_run_stats = nullify_fields_for_announcement_001(
            es=es,
            index_name=index_name,
            batch_size=100,
            dry_run=True
        )
        
        log.info("\n试运行结果:")
        log.info(f"- 找到的文档总数: {dry_run_stats['total_found']}")
        log.info(f"- 需要更新的文档数: {dry_run_stats['total_updated']}")
        
        # 4. 询问是否执行实际更新
        if dry_run_stats['total_updated'] > 0:
            log.info("\n" + "=" * 60)
            log.info("步骤4: 确认是否执行实际更新")
            log.info("=" * 60)
            
            print(f"\n发现 {dry_run_stats['total_updated']} 个文档需要更新")
            print("是否要执行实际的字段置空操作？")
            print("注意：这将修改ES中的实际数据！")
            
            confirm = input("输入 'yes' 确认执行实际更新: ")
            
            if confirm.lower() == 'yes':
                log.info("执行实际更新...")
                
                actual_stats = nullify_fields_for_announcement_001(
                    es=es,
                    index_name=index_name,
                    batch_size=100,
                    dry_run=False
                )
                
                log.info("\n实际更新结果:")
                log.info(f"- 找到的文档总数: {actual_stats['total_found']}")
                log.info(f"- 更新的文档总数: {actual_stats['total_updated']}")
                log.info(f"- 错误的文档总数: {actual_stats['total_errors']}")
                
                # 5. 验证更新结果
                log.info("\n" + "=" * 60)
                log.info("步骤5: 验证更新结果")
                log.info("=" * 60)
                
                # 重新查询前几个文档
                verification_result = search_documents(es, index_name, query=query_sample)
                
                log.info("更新后的文档字段情况:")
                for i, hit in enumerate(verification_result["hits"]["hits"], 1):
                    doc_id = hit["_id"]
                    source = hit["_source"]
                    log.info(f"\n文档 {i} (ID: {doc_id}):")
                    log.info(f"  - source_category: {source.get('source_category')}")
                    log.info(f"  - prj_name: {source.get('prj_name', 'N/A')}")
                    log.info(f"  - bidder_price: {source.get('bidder_price')} (应为null)")
                    log.info(f"  - bidder_name: {source.get('bidder_name')} (应为null)")
                    log.info(f"  - bidder_contact_person: {source.get('bidder_contact_person')} (应为null)")
                    log.info(f"  - bid_cancelled_flag: {source.get('bid_cancelled_flag')} (应为null)")
                    log.info(f"  - contract_name: {source.get('contract_name')} (应为null)")
                
                log.info("\n✅ 测试完成！")
            else:
                log.info("用户取消了实际更新操作")
        else:
            log.info("没有文档需要更新，测试完成")
        
    except Exception as e:
        log.error(f"测试失败: {e}")
        raise


def main():
    """主函数"""
    print("🧪 开始测试nullify_fields_for_announcement_001功能")
    print("=" * 60)
    
    try:
        test_nullify_functionality()
    except KeyboardInterrupt:
        log.info("\n测试被用户中断")
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
