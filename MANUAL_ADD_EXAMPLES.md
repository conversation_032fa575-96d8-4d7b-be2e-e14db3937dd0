# 手动添加黑名单功能使用示例

## 功能说明

手动添加功能允许您主动将已知有问题的文档ID添加到黑名单中，避免程序尝试处理这些文档。

## 使用场景

1. **已知问题文档**：某些文档格式特殊，已知会导致解析失败
2. **临时跳过**：暂时不想处理某些文档
3. **批量管理**：根据业务需求批量添加特定类型的文档
4. **预防性添加**：在发现问题模式后，预防性地添加类似文档

## 命令格式

```bash
python analyse_appendix.py blacklist --action add --id 文档ID [可选参数]
```

### 必需参数
- `--id`：文档ID（必需）

### 可选参数
- `--title`：文档标题
- `--url`：文档URL
- `--reason`：添加原因

## 使用示例

### 1. 最简单的添加（只提供ID）
```bash
python analyse_appendix.py blacklist --action add --id -1kMqpcBsUtJ06NfAuOo
```

输出：
```
✓ 已将文档 -1kMqpcBsUtJ06NfAuOo 添加到黑名单
```

### 2. 完整信息添加
```bash
python analyse_appendix.py blacklist --action add \
  --id -1kMqpcBsUtJ06NfAuOo \
  --title "北京大学人民医院诊疗能力提升项目公开招标公告" \
  --url "http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm" \
  --reason "文档格式异常，无法正常解析"
```

输出：
```
✓ 已将文档 -1kMqpcBsUtJ06NfAuOo 添加到黑名单
  标题: 北京大学人民医院诊疗能力提升项目公开招标公告
  URL: http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm
  原因: 文档格式异常，无法正常解析
```

### 3. 添加已知问题文档
```bash
python analyse_appendix.py blacklist --action add \
  --id -1o4r5cBsUtJ06NfkR31 \
  --title "巴楚县中医医院中草药饮片采购项目中标(成交)结果公告" \
  --reason "LLM解析超时，已知问题文档"
```

### 4. 批量添加（使用脚本）
创建一个批处理脚本 `batch_add.bat`：
```batch
@echo off
python analyse_appendix.py blacklist --action add --id doc_001 --reason "测试文档1"
python analyse_appendix.py blacklist --action add --id doc_002 --reason "测试文档2"
python analyse_appendix.py blacklist --action add --id doc_003 --reason "测试文档3"
echo 批量添加完成
```

或者创建PowerShell脚本 `batch_add.ps1`：
```powershell
$documents = @(
    @{id="doc_001"; title="测试文档1"; reason="格式问题"},
    @{id="doc_002"; title="测试文档2"; reason="编码问题"},
    @{id="doc_003"; title="测试文档3"; reason="内容异常"}
)

foreach ($doc in $documents) {
    python analyse_appendix.py blacklist --action add --id $doc.id --title $doc.title --reason $doc.reason
    Write-Host "已添加文档: $($doc.id)"
}
```

## 重复添加处理

如果尝试添加已存在的文档，系统会提示并询问是否更新：

```bash
python analyse_appendix.py blacklist --action add --id existing_doc --title "新标题"
```

输出：
```
警告：文档 existing_doc 已在黑名单中
是否要更新该文档的信息？(y/N): y
✓ 已将文档 existing_doc 添加到黑名单
  标题: 新标题
```

## 验证添加结果

添加后可以通过以下命令验证：

### 查看黑名单列表
```bash
python analyse_appendix.py blacklist --action list --limit 10
```

### 查看统计信息
```bash
python analyse_appendix.py blacklist --action stats
```

## 错误处理

### 缺少必需参数
```bash
python analyse_appendix.py blacklist --action add
```

输出：
```
错误：add操作需要指定--id参数
```

### 无效的操作类型
```bash
python analyse_appendix.py blacklist --action invalid
```

输出：
```
error: argument --action: invalid choice: 'invalid' (choose from 'list', 'stats', 'clear', 'remove', 'add')
```

## 最佳实践

### 1. 记录详细信息
建议在添加时提供详细的标题和原因，便于后续管理：
```bash
python analyse_appendix.py blacklist --action add \
  --id problematic_doc \
  --title "具体的文档标题" \
  --reason "具体的问题描述"
```

### 2. 定期审查
定期检查黑名单，确认是否还需要保留某些文档：
```bash
python analyse_appendix.py blacklist --action list
```

### 3. 备份黑名单
在进行大量操作前，备份黑名单数据库：
```bash
copy blacklist.db blacklist_backup.db
```

### 4. 测试验证
添加重要文档前，先用测试文档验证命令：
```bash
python analyse_appendix.py blacklist --action add --id test_doc --reason "测试"
python analyse_appendix.py blacklist --action remove --id test_doc
```

## 常见用例

### 用例1：处理格式异常文档
```bash
# 发现某个文档格式异常，手动添加到黑名单
python analyse_appendix.py blacklist --action add \
  --id format_error_doc \
  --reason "PDF格式损坏，无法解析"
```

### 用例2：跳过特定类型文档
```bash
# 暂时跳过某类公告
python analyse_appendix.py blacklist --action add \
  --id announcement_type_x \
  --reason "暂时跳过此类公告"
```

### 用例3：预防性添加
```bash
# 根据已知问题模式，预防性添加
python analyse_appendix.py blacklist --action add \
  --id similar_problematic_doc \
  --reason "与已知问题文档类似，预防性添加"
```

## 移除文档

如果需要重新处理某个文档，可以从黑名单中移除：
```bash
python analyse_appendix.py blacklist --action remove --id 文档ID
```

## 总结

手动添加功能提供了灵活的黑名单管理方式，让您可以：
- 主动管理问题文档
- 预防性地避免已知问题
- 根据业务需求灵活调整处理范围
- 提高系统处理效率

通过合理使用这个功能，可以显著提高文档处理系统的稳定性和效率。
