#!/usr/bin/env python3
"""
测试 analyse_appendix.py 中的条件字段填充功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import validate_and_normalize_fields, STANDARD_FIELDS, ANNOUNCEMENT_001_FIELDS

def test_appendix_announcement_001():
    """测试 analyse_appendix.py 中招标公告(001)的字段处理"""
    print("=" * 80)
    print("测试 analyse_appendix.py 中招标公告(001)的字段处理")
    print("=" * 80)
    
    # 模拟一个包含各种字段的文档
    test_doc = {
        "announcement_type": "001",
        "source_category": "001",
        "prj_name": "测试招标项目",
        "tenderee": "测试招标人",
        "object_name": "测试设备",
        "object_brand": "测试品牌",
        "bidder_price": 100000.0,  # 这个字段在001类型中应该被设置为None
        "bidder_name": "测试中标单位",  # 这个字段在001类型中应该被设置为None
        "bidder_contact_person": "联系人",  # 这个字段在001类型中应该被设置为None
        "bidder_contract_config_param": "合同参数",  # 这个字段在001类型中应该被设置为None
        "bid_cancelled_flag": "1",  # 这个字段在001类型中应该被设置为None
        "bid_cancelled_reason": "废标原因",  # 这个字段在001类型中应该被设置为None
        "contract_name": "合同文件名",  # 这个字段在001类型中应该被设置为None
        "contract_ext": ".pdf",  # 这个字段在001类型中应该被设置为None
        "source_id": "test_001",
        "insert_time": "2025-01-01 12:00:00"
    }
    
    result = validate_and_normalize_fields(test_doc, announcement_type="001")
    
    print(f"输入字段数: {len(test_doc)}")
    print(f"输出字段数: {len(result)}")
    print(f"标准字段总数: {len(STANDARD_FIELDS)}")
    print(f"001类型允许字段数: {len(ANNOUNCEMENT_001_FIELDS)}")
    
    # 检查允许的字段是否保留了值
    allowed_fields_with_values = []
    for field in ANNOUNCEMENT_001_FIELDS:
        if result.get(field) is not None:
            allowed_fields_with_values.append(field)
    
    print(f"\n允许字段中有值的字段数: {len(allowed_fields_with_values)}")
    print("有值的允许字段:", allowed_fields_with_values)
    
    # 检查不允许的字段是否被设置为None
    disallowed_fields = set(STANDARD_FIELDS) - set(ANNOUNCEMENT_001_FIELDS)
    disallowed_with_values = []
    for field in disallowed_fields:
        if result.get(field) is not None:
            disallowed_with_values.append(field)
    
    print(f"\n不允许字段中仍有值的字段数: {len(disallowed_with_values)}")
    if disallowed_with_values:
        print("ERROR: 以下不允许的字段仍有值:", disallowed_with_values)
    else:
        print("✓ 所有不允许的字段都已正确设置为None")
    
    # 验证特定字段
    print(f"\n字段验证:")
    print(f"prj_name (应保留): {result.get('prj_name')}")
    print(f"object_name (应保留): {result.get('object_name')}")
    print(f"bidder_price (应为None): {result.get('bidder_price')}")
    print(f"bidder_name (应为None): {result.get('bidder_name')}")
    print(f"contract_name (应为None): {result.get('contract_name')}")
    print(f"bid_cancelled_flag (应为None): {result.get('bid_cancelled_flag')}")
    
    return result

def test_appendix_announcement_004():
    """测试 analyse_appendix.py 中中标公告(004)的字段处理"""
    print("\n" + "=" * 80)
    print("测试 analyse_appendix.py 中中标公告(004)的字段处理")
    print("=" * 80)
    
    # 模拟一个包含各种字段的文档
    test_doc = {
        "announcement_type": "004",
        "source_category": "004",
        "prj_name": "测试招标项目",
        "tenderee": "测试招标人",
        "object_name": "测试设备",
        "object_brand": "测试品牌",
        "bidder_price": 100000.0,  # 这个字段在004类型中应该保留
        "bidder_name": "测试中标单位",  # 这个字段在004类型中应该保留
        "contract_name": "合同文件名",  # 这个字段在004类型中应该保留
        "source_id": "test_004",
        "insert_time": "2025-01-01 12:00:00"
    }
    
    result = validate_and_normalize_fields(test_doc, announcement_type="004")
    
    print(f"输入字段数: {len(test_doc)}")
    print(f"输出字段数: {len(result)}")
    print(f"标准字段总数: {len(STANDARD_FIELDS)}")
    
    # 检查有值的字段
    fields_with_values = []
    for field, value in result.items():
        if value is not None:
            fields_with_values.append(field)
    
    print(f"\n有值的字段数: {len(fields_with_values)}")
    print("有值的字段:", fields_with_values)
    
    # 验证特定字段
    print(f"\n字段验证:")
    print(f"prj_name (应保留): {result.get('prj_name')}")
    print(f"object_name (应保留): {result.get('object_name')}")
    print(f"bidder_price (应保留): {result.get('bidder_price')}")
    print(f"bidder_name (应保留): {result.get('bidder_name')}")
    print(f"contract_name (应保留): {result.get('contract_name')}")
    
    return result

def main():
    """主测试函数"""
    print("开始测试 analyse_appendix.py 中的条件字段填充功能\n")
    
    # 测试两种公告类型
    result_001 = test_appendix_announcement_001()
    result_004 = test_appendix_announcement_004()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    # 验证001类型的特殊处理
    disallowed_fields = set(STANDARD_FIELDS) - set(ANNOUNCEMENT_001_FIELDS)
    error_count = 0
    
    for field in disallowed_fields:
        if result_001.get(field) is not None:
            error_count += 1
    
    if error_count == 0:
        print("✓ analyse_appendix.py 招标公告(001)字段过滤测试通过")
    else:
        print(f"✗ analyse_appendix.py 招标公告(001)字段过滤测试失败，{error_count}个字段未正确设置为None")
    
    # 验证004类型保留所有字段
    if len(result_004) == len(STANDARD_FIELDS):
        print("✓ analyse_appendix.py 中标公告(004)字段完整性测试通过")
    else:
        print("✗ analyse_appendix.py 中标公告(004)字段完整性测试失败")
    
    print(f"\n字段统计:")
    print(f"- 标准字段总数: {len(STANDARD_FIELDS)}")
    print(f"- 001类型允许字段数: {len(ANNOUNCEMENT_001_FIELDS)}")
    print(f"- 001类型禁用字段数: {len(disallowed_fields)}")
    
    print("\n✓ analyse_appendix.py 条件字段填充逻辑同步完成！")

if __name__ == "__main__":
    main()
