#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试.doc文件解析和文件类型检测修复
"""

import os
import sys
import subprocess
import tempfile

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关函数
try:
    from analyse_appendix import parse_doc, detect_file_type, TENDER_KEYWORDS, CONTRACT_KEYWORDS
    print("✓ 成功导入相关函数")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_antiword_fix():
    """测试antiword命令修复"""
    print("=" * 80)
    print("测试antiword命令修复")
    print("=" * 80)
    
    # 创建一个简单的测试内容
    test_content = b"Test document content for antiword parsing."
    
    try:
        result = parse_doc(test_content)
        print(f"✓ parse_doc执行成功，返回内容长度: {len(result)}")
        if result:
            print(f"  内容预览: {result[:100]}...")
        return True
    except Exception as e:
        print(f"✗ parse_doc执行失败: {e}")
        return False


def test_file_type_detection():
    """测试文件类型检测改进"""
    print("\n" + "=" * 80)
    print("测试文件类型检测改进")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "原问题文件",
            "appendix_text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜",
            "preview_text": "",  # 模拟解析失败的情况
            "expected": "招标文件"
        },
        {
            "name": "明确的招标文件",
            "appendix_text": "招标文件_设备采购",
            "preview_text": "本次采购的技术规范书",
            "expected": "招标文件"
        },
        {
            "name": "合同文件",
            "appendix_text": "采购合同_医疗设备",
            "preview_text": "甲乙双方签署的服务协议",
            "expected": "合同文件"
        },
        {
            "name": "基于文件名的招标文件",
            "appendix_text": "医疗器械采购需求清单",
            "preview_text": "",  # 内容解析失败
            "expected": "招标文件"
        },
        {
            "name": "基于文件名的合同文件",
            "appendix_text": "中标通知书及合同",
            "preview_text": "",  # 内容解析失败
            "expected": "合同文件"
        },
        {
            "name": "其他文件",
            "appendix_text": "用户手册说明书",
            "preview_text": "产品使用说明",
            "expected": "其他"
        }
    ]
    
    results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {case['name']}")
        print(f"   文件名: {case['appendix_text']}")
        print(f"   内容: {case['preview_text'] or '(空)'}")
        
        try:
            result = detect_file_type(case['appendix_text'], case['preview_text'])
            expected = case['expected']
            
            if result == expected:
                print(f"   ✓ 检测正确: {result}")
                results.append(True)
            else:
                print(f"   ✗ 检测错误: 期望 {expected}, 实际 {result}")
                results.append(False)
                
        except Exception as e:
            print(f"   ✗ 检测失败: {e}")
            results.append(False)
    
    return results


def test_keyword_coverage():
    """测试关键词覆盖范围"""
    print("\n" + "=" * 80)
    print("测试关键词覆盖范围")
    print("=" * 80)
    
    print(f"招标文件关键词数量: {len(TENDER_KEYWORDS)}")
    print("招标文件关键词:")
    for i, kw in enumerate(TENDER_KEYWORDS, 1):
        print(f"  {i:2d}. {kw}")
    
    print(f"\n合同文件关键词数量: {len(CONTRACT_KEYWORDS)}")
    print("合同文件关键词:")
    for i, kw in enumerate(CONTRACT_KEYWORDS, 1):
        print(f"  {i:2d}. {kw}")
    
    # 检查是否包含关键的关键词
    required_tender_keywords = ["公开文件", "招标文件", "采购文件", "设备", "医疗"]
    missing_keywords = [kw for kw in required_tender_keywords if kw not in TENDER_KEYWORDS]
    
    if not missing_keywords:
        print("\n✓ 所有必需的招标关键词都已包含")
        return True
    else:
        print(f"\n✗ 缺少必需的招标关键词: {missing_keywords}")
        return False


def test_antiword_availability():
    """测试antiword可用性"""
    print("\n" + "=" * 80)
    print("测试antiword可用性")
    print("=" * 80)
    
    antiword_commands = [
        ["antiword", "--help"],
        ["antiword", "-h"],
        ["antiword"],
    ]
    
    for cmd in antiword_commands:
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode in [0, 1]:  # 0=成功, 1=参数错误但程序存在
                print(f"✓ antiword可用，命令: {' '.join(cmd)}")
                print(f"  退出码: {result.returncode}")
                if result.stdout:
                    print(f"  输出: {result.stdout[:100]}...")
                return True
                
        except subprocess.TimeoutExpired:
            print(f"⚠ antiword命令超时: {' '.join(cmd)}")
        except FileNotFoundError:
            print(f"✗ antiword命令未找到: {' '.join(cmd)}")
        except Exception as e:
            print(f"✗ antiword测试失败: {' '.join(cmd)} - {e}")
    
    print("✗ antiword不可用")
    return False


def main():
    """运行所有测试"""
    print("测试.doc文件解析和文件类型检测修复")
    print("=" * 80)
    
    test_results = []
    
    try:
        # 测试antiword修复
        test_results.append(test_antiword_fix())
        
        # 测试文件类型检测
        detection_results = test_file_type_detection()
        test_results.append(all(detection_results))
        
        # 测试关键词覆盖
        test_results.append(test_keyword_coverage())
        
        # 测试antiword可用性
        test_results.append(test_antiword_availability())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "antiword命令修复",
            "文件类型检测改进",
            "关键词覆盖范围",
            "antiword可用性"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count >= 3:  # antiword可用性不是必需的
            print("\n🎉 主要修复都成功了！")
            print("\n关键改进:")
            print("1. ✅ 修复了antiword命令参数问题")
            print("2. ✅ 增强了文件类型检测逻辑")
            print("3. ✅ 添加了更多招标文件关键词")
            print("4. ✅ 支持基于文件名的智能判断")
        else:
            print("\n⚠ 部分测试失败，但基本功能应该可用")
            
        # 显示使用建议
        print("\n" + "=" * 80)
        print("使用建议:")
        print("=" * 80)
        print("1. 现在程序能更好地识别招标文件，包括'公开文件'等")
        print("2. 当文件内容解析失败时，会基于文件名进行智能判断")
        print("3. antiword命令现在使用多种参数格式，兼容性更好")
        print("4. 如果需要更好的.doc支持，建议安装: pip install docx2txt")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
