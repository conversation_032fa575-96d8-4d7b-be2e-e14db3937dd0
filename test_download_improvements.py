#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载功能改进
验证新的请求头和错误处理是否能解决403 Forbidden问题
"""

import requests
from analyse_appendix import download_file
from utils.log_cfg import log


def test_original_download_method():
    """测试原始下载方法"""
    print("=" * 60)
    print("测试1: 原始下载方法")
    print("=" * 60)
    
    test_url = "http://download.ccgp.gov.cn/oss/download?uuid=177f87be-ccfe-4ed4-8056-748c84"
    
    try:
        print(f"测试URL: {test_url}")
        print("使用原始方法（无请求头）...")
        
        response = requests.get(test_url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 原始方法成功")
            return True
        else:
            print(f"❌ 原始方法失败: {response.status_code} {response.reason}")
            return False
            
    except Exception as e:
        print(f"❌ 原始方法异常: {e}")
        return False


def test_browser_headers():
    """测试浏览器请求头"""
    print("\n" + "=" * 60)
    print("测试2: 浏览器请求头")
    print("=" * 60)
    
    test_url = "http://download.ccgp.gov.cn/oss/download?uuid=177f87be-ccfe-4ed4-8056-748c84"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Referer": "http://www.ccgp.gov.cn/",
    }
    
    try:
        print(f"测试URL: {test_url}")
        print("使用浏览器请求头...")
        
        response = requests.get(test_url, headers=headers, timeout=10, allow_redirects=True)
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.content)} 字节")
        
        if response.status_code == 200:
            print("✅ 浏览器请求头成功")
            return True
        else:
            print(f"❌ 浏览器请求头失败: {response.status_code} {response.reason}")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器请求头异常: {e}")
        return False


def test_simplified_headers():
    """测试简化请求头"""
    print("\n" + "=" * 60)
    print("测试3: 简化请求头")
    print("=" * 60)
    
    test_url = "http://download.ccgp.gov.cn/oss/download?uuid=177f87be-ccfe-4ed4-8056-748c84"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "*/*",
        "Referer": "http://www.ccgp.gov.cn/",
    }
    
    try:
        print(f"测试URL: {test_url}")
        print("使用简化请求头...")
        
        response = requests.get(test_url, headers=headers, timeout=10, allow_redirects=True)
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.content)} 字节")
        
        if response.status_code == 200:
            print("✅ 简化请求头成功")
            return True
        else:
            print(f"❌ 简化请求头失败: {response.status_code} {response.reason}")
            return False
            
    except Exception as e:
        print(f"❌ 简化请求头异常: {e}")
        return False


def test_improved_download_function():
    """测试改进后的下载函数"""
    print("\n" + "=" * 60)
    print("测试4: 改进后的下载函数")
    print("=" * 60)
    
    test_url = "http://download.ccgp.gov.cn/oss/download?uuid=177f87be-ccfe-4ed4-8056-748c84"
    
    try:
        print(f"测试URL: {test_url}")
        print("使用改进后的download_file函数...")
        
        content = download_file(test_url, max_retries=3)
        
        if content:
            print(f"✅ 改进后的下载函数成功")
            print(f"下载内容大小: {len(content)} 字节")
            
            # 检查内容类型
            if content.startswith(b'%PDF'):
                print("  文件类型: PDF")
            elif content.startswith(b'PK'):
                print("  文件类型: ZIP/Office文档")
            elif content.startswith(b'<html'):
                print("  文件类型: HTML（可能是错误页面）")
            else:
                print(f"  文件类型: 未知（前16字节: {content[:16]}）")
            
            return True
        else:
            print("❌ 改进后的下载函数失败")
            return False
            
    except Exception as e:
        print(f"❌ 改进后的下载函数异常: {e}")
        return False


def test_alternative_urls():
    """测试其他可能的URL格式"""
    print("\n" + "=" * 60)
    print("测试5: 其他URL格式")
    print("=" * 60)
    
    # 尝试一些可能的URL变体
    base_uuid = "177f87be-ccfe-4ed4-8056-748c84"
    alternative_urls = [
        f"http://download.ccgp.gov.cn/oss/download/{base_uuid}",
        f"https://download.ccgp.gov.cn/oss/download?uuid={base_uuid}",
        f"http://www.ccgp.gov.cn/oss/download?uuid={base_uuid}",
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "http://www.ccgp.gov.cn/",
    }
    
    for i, url in enumerate(alternative_urls):
        try:
            print(f"\n尝试URL {i+1}: {url}")
            response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ URL {i+1} 成功")
                print(f"  内容长度: {len(response.content)} 字节")
                return True
            else:
                print(f"  ❌ URL {i+1} 失败: {response.reason}")
                
        except Exception as e:
            print(f"  ❌ URL {i+1} 异常: {e}")
    
    print("所有备用URL都失败")
    return False


def test_url_analysis():
    """分析URL结构"""
    print("\n" + "=" * 60)
    print("测试6: URL结构分析")
    print("=" * 60)
    
    test_url = "http://download.ccgp.gov.cn/oss/download?uuid=177f87be-ccfe-4ed4-8056-748c84"
    
    print(f"原始URL: {test_url}")
    
    # 分析URL组成部分
    from urllib.parse import urlparse, parse_qs
    
    parsed = urlparse(test_url)
    print(f"协议: {parsed.scheme}")
    print(f"域名: {parsed.netloc}")
    print(f"路径: {parsed.path}")
    print(f"查询参数: {parse_qs(parsed.query)}")
    
    # 检查UUID格式
    uuid = parse_qs(parsed.query).get('uuid', [''])[0]
    print(f"UUID: {uuid}")
    print(f"UUID长度: {len(uuid)}")
    
    if len(uuid) == 36 and uuid.count('-') == 4:
        print("✅ UUID格式正确")
    else:
        print("❌ UUID格式可能有问题")
    
    return True


def main():
    """运行所有下载测试"""
    print("开始测试下载功能改进...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("原始下载方法", test_original_download_method()))
    test_results.append(("浏览器请求头", test_browser_headers()))
    test_results.append(("简化请求头", test_simplified_headers()))
    test_results.append(("改进后的下载函数", test_improved_download_function()))
    test_results.append(("其他URL格式", test_alternative_urls()))
    test_results.append(("URL结构分析", test_url_analysis()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("下载功能测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed >= 1:
        print("🎉 至少有一种方法可以成功下载！")
        print("\n建议:")
        print("1. 如果浏览器请求头成功，说明需要完整的请求头")
        print("2. 如果简化请求头成功，说明只需要基本的User-Agent")
        print("3. 如果其他URL格式成功，说明需要调整URL格式")
        print("4. 如果都失败，可能需要特殊的认证或该文件已不可访问")
    else:
        print("⚠️  所有下载方法都失败了")
        print("\n可能的原因:")
        print("1. 文件已被删除或移动")
        print("2. 需要特殊的认证或权限")
        print("3. 服务器配置了严格的访问控制")
        print("4. UUID已过期或无效")
    
    return passed >= 1


if __name__ == "__main__":
    main()
