#!/usr/bin/env python3
"""
调试相似度计算过程
"""

import os
import sys
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_similarity_calculation(text1, text2):
    """调试相似度计算的每一步"""
    print(f"调试相似度计算: '{text1}' vs '{text2}'")
    print("=" * 60)
    
    # 1. 文本标准化
    def normalize_text(text):
        text = str(text).strip().lower()
        remove_words = ["关于", "项目", "采购", "招标", "公告", "中标", "合同", "服务项目"]
        for word in remove_words:
            text = text.replace(word, "")
        return text.strip()
    
    text1_norm = normalize_text(text1)
    text2_norm = normalize_text(text2)
    
    print(f"1. 文本标准化:")
    print(f"   原文本1: '{text1}'")
    print(f"   标准化1: '{text1_norm}'")
    print(f"   原文本2: '{text2}'")
    print(f"   标准化2: '{text2_norm}'")
    print()
    
    # 2. 包含关系检查
    containment_similarity = 0.0
    if text1_norm in text2_norm or text2_norm in text1_norm:
        shorter_len = min(len(text1_norm), len(text2_norm))
        longer_len = max(len(text1_norm), len(text2_norm))
        base_containment = shorter_len / longer_len if longer_len > 0 else 0.0
        
        if shorter_len >= 4:
            containment_similarity = 0.9
        elif shorter_len >= 3:
            containment_similarity = 0.8
        else:
            containment_similarity = base_containment
    
    print(f"2. 包含关系检查:")
    print(f"   '{text1_norm}' in '{text2_norm}': {text1_norm in text2_norm}")
    print(f"   '{text2_norm}' in '{text1_norm}': {text2_norm in text1_norm}")
    print(f"   包含关系相似度: {containment_similarity}")
    print()
    
    # 3. 核心词汇匹配
    def extract_core_words(text):
        words = re.findall(r"[\u4e00-\u9fff]+", text)
        core_words = []
        for word in words:
            if len(word) >= 2:
                core_words.append(word)
        return set(core_words)
    
    core_words1 = extract_core_words(text1_norm)
    core_words2 = extract_core_words(text2_norm)
    
    core_word_similarity = 0.0
    if core_words1 and core_words2:
        intersection = len(core_words1 & core_words2)
        union = len(core_words1 | core_words2)
        core_word_similarity = intersection / union if union > 0 else 0.0
        
        if intersection > 0:
            long_word_match = any(len(word) >= 4 for word in (core_words1 & core_words2))
            if long_word_match:
                core_word_similarity = min(core_word_similarity + 0.3, 1.0)
            elif intersection >= 2:
                core_word_similarity = min(core_word_similarity + 0.2, 1.0)
    
    print(f"3. 核心词汇匹配:")
    print(f"   核心词汇1: {core_words1}")
    print(f"   核心词汇2: {core_words2}")
    print(f"   交集: {core_words1 & core_words2}")
    print(f"   并集: {core_words1 | core_words2}")
    print(f"   基础相似度: {len(core_words1 & core_words2)}/{len(core_words1 | core_words2)} = {len(core_words1 & core_words2)/len(core_words1 | core_words2) if core_words1 | core_words2 else 0}")
    print(f"   最终核心词汇相似度: {core_word_similarity}")
    print()
    
    # 4. 语义相关性检查
    def find_word_combinations(text):
        combinations = []
        words = re.findall(r"[\u4e00-\u9fff]{2,}", text)
        for i in range(len(words) - 1):
            combo = words[i] + words[i + 1]
            if len(combo) >= 4:
                combinations.append(combo)
        return set(combinations)
    
    combos1 = find_word_combinations(text1_norm)
    combos2 = find_word_combinations(text2_norm)
    
    semantic_similarity = 0.0
    if core_word_similarity > 0 and combos1 and combos2:
        combo_intersection = len(combos1 & combos2)
        if combo_intersection > 0:
            semantic_similarity = 0.3
    
    print(f"4. 语义相关性检查:")
    print(f"   词汇组合1: {combos1}")
    print(f"   词汇组合2: {combos2}")
    print(f"   组合交集: {combos1 & combos2}")
    print(f"   语义相关性: {semantic_similarity}")
    print()
    
    # 5. 编辑距离和Jaccard（简化计算）
    edit_similarity = 0.3  # 假设值
    jaccard_similarity = 0.4  # 假设值
    
    # 6. 动态权重分配
    if core_word_similarity >= 0.6:
        combined_similarity = (
            containment_similarity * 0.3
            + core_word_similarity * 0.5
            + edit_similarity * 0.15
            + jaccard_similarity * 0.05
        )
        weight_type = "高匹配权重"
    else:
        combined_similarity = (
            containment_similarity * 0.5
            + core_word_similarity * 0.3
            + edit_similarity * 0.15
            + jaccard_similarity * 0.05
        )
        weight_type = "低匹配权重"
    
    # 应用语义加分
    final_similarity = min(combined_similarity + semantic_similarity, 1.0)
    
    print(f"5. 最终计算:")
    print(f"   权重类型: {weight_type}")
    print(f"   基础相似度: {combined_similarity}")
    print(f"   语义加分: +{semantic_similarity}")
    print(f"   最终相似度: {final_similarity}")
    print()
    
    return final_similarity

if __name__ == "__main__":
    # 调试目标案例
    result = debug_similarity_calculation("清洁服务外包", "物业清洁服务项目")
    print(f"调试结果: {result}")
