#!/usr/bin/env python3
"""
演示改进后的字段处理逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_noappendix import validate_and_normalize_fields

def demo_improved_logic():
    """演示改进后的字段处理逻辑"""
    print("=" * 80)
    print("演示改进后的字段处理逻辑")
    print("=" * 80)
    
    # 模拟一个包含多余字段的招标公告文档
    test_doc = {
        "announcement_type": "001",
        "prj_name": "测试招标项目",
        "tenderee": "测试招标人",
        "object_name": "测试设备",
        "object_brand": "测试品牌",
        # 以下字段在001类型中不应该有值，但存在于输入中
        "bidder_price": 100000.0,
        "bidder_name": "测试中标单位",
        "bidder_contact_person": "联系人",
        "bidder_contact_phone_number": "13800138000",
        "bidder_contract_config_param": "合同配置参数",
        "bid_cancelled_flag": "1",
        "bid_cancelled_reason": "废标原因",
        "source_id": "test_001",
        "insert_time": "2025-01-01 12:00:00"
    }
    
    print("输入文档包含的字段:")
    for field, value in test_doc.items():
        print(f"  {field}: {value}")
    
    print(f"\n输入字段总数: {len(test_doc)}")
    
    # 处理文档
    result = validate_and_normalize_fields(test_doc, announcement_type="001")
    
    print(f"\n输出字段总数: {len(result)}")
    
    # 检查不应该有值的字段
    disallowed_fields = [
        "bidder_price", "bidder_name", "bidder_contact_person", 
        "bidder_contact_phone_number", "bidder_contract_config_param",
        "bid_cancelled_flag", "bid_cancelled_reason"
    ]
    
    print("\n不应该有值的字段处理结果:")
    for field in disallowed_fields:
        value = result.get(field)
        status = "✓ 正确设置为None" if value is None else f"✗ 错误保留了值: {value}"
        print(f"  {field}: {status}")
    
    # 检查应该保留值的字段
    allowed_fields = [
        "announcement_type", "prj_name", "tenderee", 
        "object_name", "object_brand", "source_id", "insert_time"
    ]
    
    print("\n应该保留值的字段处理结果:")
    for field in allowed_fields:
        value = result.get(field)
        status = f"✓ 正确保留值: {value}" if value is not None else "✗ 错误设置为None"
        print(f"  {field}: {status}")
    
    print("\n" + "=" * 80)
    print("关键改进点:")
    print("1. 不再删除'多余字段'，而是将它们设置为None")
    print("2. 始终输出包含所有59个标准字段的完整文档")
    print("3. 根据公告类型(001/004/010)应用不同的字段值策略")
    print("4. 保持ES文档结构的一致性")
    print("=" * 80)

if __name__ == "__main__":
    demo_improved_logic()
