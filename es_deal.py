from typing import List, Optional
from elasticsearch import Elasticsearch
import warnings

warnings.filterwarnings("ignore")
import os
from dotenv import load_dotenv
from utils.log_cfg import log


def init_es_client() -> Elasticsearch:
    """初始化并返回Elasticsearch客户端。

    从.env文件加载ES配置并初始化客户端。必须确保.env文件中包含以下环境变量：
    - ES_HOST: ES服务器地址，格式为 host:port
    - ES_USER: ES用户名
    - ES_PASSWORD: ES密码

    Returns:
        Elasticsearch: ES客户端实例，已完成认证和连接测试。

    Raises:
        Exception: 当初始化失败时抛出异常，可能的原因包括：
            - .env文件不存在或格式错误
            - 环境变量缺失
            - ES连接失败
            - 认证失败

    Example:
        # .env文件示例
        ES_HOST=localhost:9200
        ES_USER=elastic
        ES_PASSWORD=password

        # 使用示例
        es = init_es_client()
        if es.ping():
            log.info("ES连接成功")
    """
    try:
        # 加载.env文件
        load_dotenv()

        # 从环境变量获取配置
        es_host = os.getenv("ES_HOST")
        es_user = os.getenv("ES_USER")
        es_password = os.getenv("ES_PASSWORD")

        es = Elasticsearch(
            hosts=[es_host], basic_auth=(es_user, es_password), verify_certs=False
        )
        log.info(f"ES客户端初始化成功，连接到: {es_host}")
        return es
    except Exception as e:
        raise Exception(f"初始化ES客户端失败: {e}")


def create_index(es: Elasticsearch, index_name: str) -> None:
    """创建索引，如果索引已存在则忽略。

    Args:
        es (Elasticsearch): ES客户端实例，必须具有创建索引的权限。
        index_name (str): 索引名称，默认为'markersweb_attachment_analysis'。
            索引名必须全小写，不能包含\, /, *, ?, ", <, >, |, 空格, 逗号等特殊字符。

    Raises:
        Exception: 当创建操作失败时抛出异常，可能的原因包括：
            - 索引名称格式不正确
            - 权限不足
            - ES集群状态异常
            - 磁盘空间不足

    Example:
        # 创建默认索引
        create_index(es)

        # 创建自定义索引
        create_index(es, "custom_index_2025")

        # 错误的索引名称示例
        # create_index(es, "My-Index")  # 包含大写字母和连字符
        # create_index(es, "test index")  # 包含空格
    """
    try:
        log.info(f"开始创建索引: {index_name}")
        if not es.indices.exists(index=index_name):
            es.indices.create(index=index_name)
            log.info(f"索引 {index_name} 创建成功")
        else:
            log.info(f"索引 {index_name} 已存在，跳过创建")
    except Exception as e:
        raise Exception(f"创建索引失败: {e}")


def define_mapping(es: Elasticsearch, index_name: str) -> None:
    """为索引定义映射。

    定义招投标文档解析结果的字段映射，包括：
    - 基本信息字段（项目名称、编号等）
    - 时间相关字段（发布时间、截止时间等）
    - 地理信息字段（省份、城市、区县等）
    - 标的物信息字段（名称、品牌、型号等）
    - 投标人信息字段（名称、联系人等）
    所有字段都包含类型定义和中文描述。

    Args:
        es (Elasticsearch): ES客户端实例，必须具有管理索引的权限。
        index_name (str): 索引名称，默认为'markersweb_attachment_analysis'。
            如果索引已存在且已有数据，更新mapping可能会导致数据不一致。

    Raises:
        Exception: 当定义映射失败时抛出异常，可能的原因包括：
            - 索引已存在且无法修改
            - mapping格式错误
            - 权限不足
            - ES集群状态异常

    Example:
        # 为新索引定义映射
        define_mapping(es, "tender_docs_2025")

        # 注意：对已有数据的索引修改mapping需要谨慎
        # 建议创建新索引并使用reindex迁移数据
    """
    try:
        log.info(f"开始为索引 {index_name} 定义映射")
        mapping = {
            "mappings": {
                "_meta": {"description": "招投标文档解析结果索引"},
                "properties": {
                    "bid_name": {"type": "text", "meta": {"description": "标段名称"}},
                    "bid_number": {
                        "type": "keyword",
                        "meta": {"description": "标段编号"},
                    },
                    "bid_budget": {
                        "type": "double",
                        "meta": {"description": "标段预算金额"},
                    },
                    "fiscal_delegation_number": {
                        "type": "keyword",
                        "meta": {"description": "财政委托编号"},
                    },
                    "prj_addr": {"type": "text", "meta": {"description": "项目地址"}},
                    "prj_name": {"type": "text", "meta": {"description": "项目名称"}},
                    "prj_number": {
                        "type": "keyword",
                        "meta": {"description": "项目编号"},
                    },
                    "prj_type": {
                        "type": "keyword",
                        "meta": {"description": "项目类型(工程,货物,服务)"},
                    },
                    "release_time": {
                        "type": "date",
                        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis",
                        "meta": {"description": "发布时间"},
                    },
                    "prj_approval_authority": {
                        "type": "text",
                        "meta": {"description": "项目审批单位"},
                    },
                    "superintendent_office": {
                        "type": "text",
                        "meta": {"description": "监管部门"},
                    },
                    "superintendent_office_code": {
                        "type": "keyword",
                        "meta": {"description": "监管部门编号"},
                    },
                    "tenderee": {"type": "text", "meta": {"description": "招标人"}},
                    "bid_submission_deadline": {
                        "type": "date",
                        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis",
                        "meta": {"description": "投标截止时间"},
                    },
                    "trade_platform": {
                        "type": "text",
                        "meta": {"description": "交易平台"},
                    },
                    "procurement_method": {
                        "type": "keyword",
                        "meta": {"description": "采购方式"},
                    },
                    "prj_sub_type": {
                        "type": "keyword",
                        "meta": {
                            "description": "项目细分类型(设备,维保,耗材,试剂,手术器械,其他)"
                        },
                    },
                    "province": {"type": "keyword", "meta": {"description": "省份"}},
                    "city": {"type": "keyword", "meta": {"description": "城市"}},
                    "county": {"type": "keyword", "meta": {"description": "区县"}},
                    "announcement_type": {
                        "type": "keyword",
                        "meta": {
                            "description": "公告类型(001-招标公告,002-中标候选人公示,003-更正/澄清公告,004-结果公告,010-中标通知书,999-其他)"
                        },
                    },
                    "object_name": {
                        "type": "text",
                        "meta": {"description": "标的物名称"},
                    },
                    "object_brand": {
                        "type": "keyword",
                        "meta": {"description": "标的物品牌"},
                    },
                    "object_model": {
                        "type": "keyword",
                        "meta": {"description": "标的物型号"},
                    },
                    "object_supplier": {
                        "type": "text",
                        "meta": {"description": "标的物供应商"},
                    },
                    "object_produce_area": {
                        "type": "text",
                        "meta": {"description": "标的物产地"},
                    },
                    "object_conf": {
                        "type": "text",
                        "meta": {"description": "标的物配置参数"},
                    },
                    "object_oem": {
                        "type": "text",
                        "meta": {"description": "标的物OEM厂商"},
                    },
                    "object_amount": {
                        "type": "integer",
                        "meta": {"description": "标的物数量"},
                    },
                    "object_unit": {
                        "type": "keyword",
                        "meta": {"description": "标的物单位"},
                    },
                    "object_price": {
                        "type": "double",
                        "meta": {"description": "标的物单价"},
                    },
                    "object_total_price": {
                        "type": "double",
                        "meta": {"description": "标的物总价"},
                    },
                    "object_maintenance_period": {
                        "type": "keyword",
                        "meta": {"description": "标的物维保期限"},
                    },
                    "object_price_source": {
                        "type": "keyword",
                        "meta": {"description": "标的物价格来源"},
                    },
                    "object_quality": {
                        "type": "keyword",
                        "meta": {"description": "标的物质量层次(1-国产,2-进口)"},
                    },
                    "bidder_price": {
                        "type": "double",
                        "meta": {"description": "中标金额"},
                    },
                    "bidder_name": {
                        "type": "text",
                        "meta": {"description": "中标单位名称"},
                    },
                    "bidder_contact_person": {
                        "type": "text",
                        "meta": {"description": "中标单位联系人"},
                    },
                    "bidder_contact_phone_number": {
                        "type": "keyword",
                        "meta": {"description": "中标单位联系人电话"},
                    },
                    "bidder_contract_config_param": {
                        "type": "text",
                        "meta": {"description": "中标合同配置参数"},
                    },
                    "agent": {"type": "text", "meta": {"description": "代理机构"}},
                    "service_fee": {
                        "type": "double",
                        "meta": {"description": "代理服务收费金额"},
                    },
                    "source_id": {
                        "type": "keyword",
                        "meta": {"description": "源公告数据id"},
                    },
                    "source_create_time": {
                        "type": "date",
                        "format": "yyyy-MM-dd",
                        "meta": {"description": "源公告数据创建时间"},
                    },
                    "source_category": {
                        "type": "keyword",
                        "meta": {"description": "源公告类型"},
                    },
                    "source_url": {
                        "type": "text",
                        "meta": {"description": "源公告HTTP链接"},
                    },
                    "source_appendix": {
                        "type": "nested",
                        "properties": {
                            "text": {"type": "text"},
                            "url": {"type": "keyword"},
                        },
                        "meta": {"description": "源公告附件下载链接"},
                    },
                    "insert_time": {
                        "type": "date",
                        "format": "yyyy-MM-dd HH:mm:ss",
                        "meta": {"description": "数据插入时间"},
                    },
                },
            }
        }
        es.indices.create(
            index=index_name, body=mapping, ignore=400
        )  # ignore=400忽略索引已存在错误
        log.info(f"索引 {index_name} 映射定义成功")
    except Exception as e:
        raise Exception(f"定义映射失败: {e}")


def add_field_to_mapping(
    es: Elasticsearch, index_name: str, field_name: str, field_mapping: dict
) -> None:
    """向指定索引的mapping中添加新字段。

    Args:
        es (Elasticsearch): ES客户端实例。
        index_name (str): 索引名。
        field_name (str): 新字段名。
        field_mapping (dict): 字段的mapping定义。

    Raises:
        Exception: 添加失败时抛出异常。

    Example:
        add_field_to_mapping(es, 'my_index', 'new_field', {'type': 'text'})
    """
    try:
        log.info(f"开始向索引 {index_name} 添加字段: {field_name}")
        es.indices.put_mapping(
            index=index_name, body={"properties": {field_name: field_mapping}}
        )
        log.info(f"字段 {field_name} 添加成功")
    except Exception as e:
        raise Exception(f"添加字段失败: {e}")


def update_field_in_mapping(
    es: Elasticsearch, index_name: str, field_name: str, field_mapping: dict
) -> None:
    """更新指定索引的mapping中某字段的定义（只能改部分属性，不能改type）。

    Args:
        es (Elasticsearch): ES客户端实例。
        index_name (str): 索引名。
        field_name (str): 字段名。
        field_mapping (dict): 新的字段mapping定义。

    Raises:
        Exception: 更新失败时抛出异常。

    Note:
        ES不允许直接修改字段type，如需更改type需新建索引并reindex。

    Example:
        update_field_in_mapping(es, 'my_index', 'my_field', {'type': 'text', 'fielddata': True})
    """
    try:
        log.info(f"开始更新索引 {index_name} 中字段 {field_name} 的映射")
        es.indices.put_mapping(
            index=index_name, body={"properties": {field_name: field_mapping}}
        )
        log.info(f"字段 {field_name} 映射更新成功")
    except Exception as e:
        raise Exception(f"更新字段失败: {e}")


def insert_document(
    es: Elasticsearch,
    index_name: str,
    doc_id: str | None = None,
    document: dict | None = None,
) -> None:
    """插入文档到指定索引。

    Args:
        es (Elasticsearch): ES客户端实例。
        index_name (str): 索引名称，默认为'markersweb_attachment_analysis'。
        doc_id (str | None): 文档ID，如果不指定则由ES自动生成。
        document (dict | None): 要插入的文档内容，必须是一个有效的JSON对象。

    Raises:
        Exception: 当插入操作失败时抛出异常，可能的原因包括：
            - 文档格式不正确
            - 索引不存在
            - ES连接问题

    Example:
        doc = {
            "title": "测试文档",
            "content": "这是一个测试文档",
            "timestamp": "2025-06-13T14:30:00"
        }
        insert_document(es, doc_id="doc1", document=doc)
    """
    try:
        log.info(f"开始向索引 {index_name} 插入文档，文档ID: {doc_id}")
        es.index(index=index_name, id=doc_id, document=document)
        log.info(f"文档插入成功，文档ID: {doc_id}")
    except Exception as e:
        raise Exception(f"插入文档失败: {e}")


# def validate_json_data(data: dict) -> bool:
#     """验证JSON数据的完整性

#     Args:
#         data: JSON数据

#     Returns:
#         是否验证通过
#     """
#     required_fields = {
#         "bid_name",
#         "prj_name",
#         "prj_number",
#         "prj_type",
#         "release_time",
#         "tenderee",
#         "trade_platform",
#     }

#     # 检查必需字段
#     if not all(field in data for field in required_fields):
#         return False

#     # 检查字段值类型
#     if not isinstance(data.get("release_time"), str):
#         return False

#     return True


# def insert_document(
#     es: Elasticsearch, index_name: str, doc_id: str, document: dict
# ) -> None:
#     """插入文档到ES

#     Args:
#         es: ES客户端
#         index_name: 索引名
#         doc_id: 文档ID
#         document: 文档数据
#     """
#     try:
#         # 验证数据
#         if not validate_json_data(document):
#             raise ValueError("数据验证失败")

#         # 插入数据
#         es.index(index=index_name, id=doc_id, document=document)
#         log.info(f"文档插入成功: {doc_id}")
#     except Exception as e:
#         log.error(f"文档插入失败: {e}")
#         raise


def update_document(
    es: Elasticsearch,
    index_name: str,
    doc_id: str | None = None,
    updated_doc: dict | None = None,
) -> None:
    """更新指定ID的文档。

    Args:
        es (Elasticsearch): ES客户端实例。
        index_name (str): 索引名称，默认为'markersweb_attachment_analysis'。
        doc_id (str | None): 要更新的文档ID，必须指定。
        updated_doc (dict | None): 更新的文档内容，必须是一个有效的JSON对象。
            只更新指定的字段，未指定的字段保持不变。

    Raises:
        Exception: 当更新操作失败时抛出异常，可能的原因包括：
            - 文档不存在
            - 文档格式不正确
            - ES连接问题

    Example:
        # 更新文档的特定字段
        update_document(
            es,
            doc_id="doc1",
            updated_doc={
                "status": "已审核",
                "update_time": "2025-06-13T14:30:00"
            }
        )
    """
    try:
        log.info(f"开始更新索引 {index_name} 中的文档，文档ID: {doc_id}")
        es.update(index=index_name, id=doc_id, body={"doc": updated_doc})
        log.info(f"文档更新成功，文档ID: {doc_id}")
    except Exception as e:
        raise Exception(f"更新文档失败: {e}")


def delete_document(
    es: Elasticsearch,
    index_name: str,
    doc_id: str | None = None,
) -> None:
    """删除指定ID的文档。

    Args:
        es (Elasticsearch): ES客户端实例。
        index_name (str): 索引名称，默认为'markersweb_attachment_analysis'。
        doc_id (str | None): 要删除的文档ID，必须指定。

    Raises:
        Exception: 当删除操作失败时抛出异常，可能的原因包括：
            - 文档不存在
            - 索引不存在
            - ES连接问题

    Example:
        # 删除指定ID的文档
        delete_document(es, doc_id="doc1")

        # 删除特定索引中的文档
        delete_document(es, index_name="custom_index", doc_id="doc1")
    """
    try:
        log.info(f"开始删除索引 {index_name} 中的文档，文档ID: {doc_id}")
        es.delete(index=index_name, id=doc_id)
        log.info(f"文档删除成功，文档ID: {doc_id}")
    except Exception as e:
        raise Exception(f"删除文档失败: {e}")


def search_documents(
    es: Elasticsearch,
    index_name: str,
    query: dict | None = None,
) -> dict:
    """在指定索引中搜索文档。

    Args:
        es (Elasticsearch): ES客户端实例。
        index_name (str): 索引名称，默认为'markersweb_attachment_analysis'。
        query (dict | None): 搜索查询条件，必须是一个有效的ES查询DSL。
            如果为None，则返回所有文档。

    Returns:
        dict: 搜索结果，包含以下主要字段：
            - hits.total: 匹配的文档总数
            - hits.hits: 匹配的文档列表
            - took: 搜索耗时（毫秒）
            - _shards: 分片信息

    Raises:
        Exception: 当搜索操作失败时抛出异常，可能的原因包括：
            - 查询语法错误
            - 索引不存在
            - ES连接问题

    Example:
        # 简单的全文搜索
        query = {
            "query": {
                "match": {
                    "content": "搜索关键词"
                }
            }
        }

        # 复杂的组合查询
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"match": {"title": "标题关键词"}},
                        {"range": {"timestamp": {"gte": "2025-01-01"}}}
                    ]
                }
            },
            "sort": [{"timestamp": "desc"}],
            "size": 10
        }

        result = search_documents(es, query=query)
        total_hits = result['hits']['total']['value']
        documents = result['hits']['hits']
    """
    try:
        # log.info(f"开始在索引 {index_name} 中搜索文档，查询条件: {query}")
        log.info(f"开始在索引 {index_name} 中搜索文档")
        result = es.search(index=index_name, body=query)
        log.info(
            f"搜索完成，在索引 {index_name} 中找到 {result.get('hits', {}).get('total', {}).get('value', 0)} 条结果"
        )
        return result
    except Exception as e:
        raise Exception(f"搜索文档失败: {e}")


def remove_field_by_reindex(
    es: Elasticsearch, old_index: str, new_index: str, remove_fields: list
) -> None:
    """
    通过reindex方式删除ES索引的指定字段（7.x通用做法）

    Args:
        es: ES客户端
        old_index: 旧索引名
        new_index: 新索引名
        remove_fields: 要删除的字段名列表

    Raises:
        Exception: 失败时抛出
    """
    try:
        log.info(f"准备重建索引 {old_index}，去除字段: {remove_fields}")

        # 1. 获取原mapping和settings
        mapping = es.indices.get_mapping(index=old_index)[old_index]["mappings"]
        settings = es.indices.get_settings(index=old_index)[old_index]["settings"][
            "index"
        ]

        # 调试：打印原始设置
        log.info(f"原始索引设置: {list(settings.keys())}")
        for k, v in settings.items():
            log.debug(f"设置 {k}: {v}")

        # 2. 移除要删除的字段
        for field in remove_fields:
            mapping["properties"].pop(field, None)

        # 3. 创建新索引
        # 过滤掉不应该复制的内部设置
        filtered_settings = {}
        exclude_settings = [
            "version",
            "creation_date",
            "uuid",
            "provided_name",
            "routing_partition_size",
            "store",
            "index",
        ]

        for k, v in settings.items():
            # 跳过以这些前缀开头的设置
            if any(k.startswith(prefix) for prefix in exclude_settings):
                continue
            # 只保留基本的设置
            if k in [
                "number_of_shards",
                "number_of_replicas",
                "refresh_interval",
                "max_result_window",
            ]:
                filtered_settings[k] = v

        # 调试：打印过滤后的设置
        log.info(f"过滤后的设置: {filtered_settings}")

        body = {
            "settings": filtered_settings,
            "mappings": mapping,
        }
        es.indices.create(index=new_index, body=body)
        log.info(f"新索引 {new_index} 创建成功")

        # 4. reindex数据，排除字段
        reindex_body = {
            "source": {"index": old_index, "_source": {"excludes": remove_fields}},
            "dest": {"index": new_index},
        }
        es.reindex(body=reindex_body, wait_for_completion=True, request_timeout=3600)
        log.info(f"数据reindex到 {new_index} 完成")

        # 5. 校验后可选：删除旧索引，重命名新索引
        # es.indices.delete(index=old_index)
        # es.indices.put_alias(index=new_index, name=old_index)
        log.info("请手动校验新索引数据无误后再删除旧索引！")

    except Exception as e:
        raise Exception(f"通过reindex删除字段失败: {e}")


def rebuild_index_with_new_field_types(
    es: Elasticsearch, old_index: str, new_index: str, field_type_map: dict
) -> None:
    """
    通过reindex方式修改字段类型（ES 7.x标准做法）

    Args:
        es: ES客户端
        old_index: 旧索引名
        new_index: 新索引名
        field_type_map: 需要修改的字段及新类型，如 {"object_price": {"type": "text"}, ...}

    Raises:
        Exception: 失败时抛出
    """
    try:
        log.info(f"准备重建索引 {old_index}，修改字段类型: {field_type_map}")

        # 1. 获取原mapping和settings
        mapping = es.indices.get_mapping(index=old_index)[old_index]["mappings"]
        settings = es.indices.get_settings(index=old_index)[old_index]["settings"][
            "index"
        ]

        # 2. 修改mapping
        for field, new_type in field_type_map.items():
            if "properties" in mapping and field in mapping["properties"]:
                mapping["properties"].pop(field)
            mapping["properties"][field] = new_type

        # 3. 创建新索引
        body = {
            "settings": {
                k: v for k, v in settings.items() if not k.startswith("version")
            },
            "mappings": mapping,
        }
        es.indices.create(index=new_index, body=body)
        log.info(f"新索引 {new_index} 创建成功")

        # 4. reindex数据（如需类型转换可加脚本）
        reindex_body = {
            "source": {
                "index": old_index,
            },
            "dest": {"index": new_index},
            "script": {
                "lang": "painless",
                "source": """
                    if (ctx._source.object_price != null) {
                        ctx._source.object_price = ctx._source.object_price.toString();
                    }
                    if (ctx._source.object_amount != null) {
                        ctx._source.object_amount = ctx._source.object_amount.toString();
                    }
                """,
            },
        }
        es.reindex(body=reindex_body, wait_for_completion=True, request_timeout=3600)
        log.info(f"数据reindex到 {new_index} 完成")

        log.info("请手动校验新索引数据无误后再删除旧索引！")

    except Exception as e:
        raise Exception(f"通过reindex修改字段类型失败: {e}")


def add_alias(es: Elasticsearch, index_name: str, alias_name: str) -> None:
    """为指定索引添加别名

    Args:
        es (Elasticsearch): ES客户端实例
        index_name (str): 索引名称
        alias_name (str): 别名名称

    Raises:
        Exception: 当添加别名失败时抛出异常
    """
    try:
        log.info(f"开始为索引 {index_name} 添加别名 {alias_name}")
        es.indices.put_alias(index=index_name, name=alias_name)
        log.info(f"成功为索引 {index_name} 添加别名 {alias_name}")
    except Exception as e:
        raise Exception(f"添加别名失败: {e}")


def add_alias_with_filter(
    es: Elasticsearch,
    index_names: List[str],
    alias_name: str,
    filter_body: Optional[dict] = None,
) -> None:
    """为多个索引添加带有可选过滤器的别名

    Args:
        es (Elasticsearch): ES客户端实例
        index_names (List[str]): 索引名称列表
        alias_name (str): 别名名称
        filter_body (dict, optional): 过滤器条件，默认为None

    Raises:
        Exception: 当添加别名失败时抛出异常
    """
    try:
        log.info(f"开始为索引 {index_names} 添加别名 {alias_name}")

        # 构建别名操作体
        body = {"actions": []}

        # 为每个索引添加别名操作
        for index_name in index_names:
            action = {"add": {"index": index_name, "alias": alias_name}}

            # 如果提供了过滤器条件
            if filter_body:
                action["add"]["filter"] = filter_body

            body["actions"].append(action)

        # 执行别名操作
        es.indices.update_aliases(body=body)
        log.info(f"成功为索引 {index_names} 添加别名 {alias_name}")
    except Exception as e:
        raise Exception(f"添加别名失败: {e}")


def nullify_fields_for_announcement_001(
    es: Elasticsearch, index_name: str, batch_size: int = 1000, dry_run: bool = False
) -> dict:
    """
    筛选出公告类型为"001"的数据，并将指定字段设置为null

    Args:
        es (Elasticsearch): ES客户端实例
        index_name (str): 索引名称（通常是markersweb_attachment_analysis_alias）
        batch_size (int): 批处理大小，默认1000
        dry_run (bool): 是否为试运行模式，True时只统计不实际更新

    Returns:
        dict: 包含处理统计信息的字典

    Raises:
        Exception: 当操作失败时抛出异常
    """
    # 需要设置为null的字段列表
    fields_to_nullify = [
        "bidder_price",  # 中标金额
        "bidder_name",  # 中标单位名称
        "bidder_contact_person",  # 中标单位联系人
        "bidder_contact_phone_number",  # 中标单位联系人电话
        "bidder_contract_config_param",  # 中标合同配置参数
        "bid_cancelled_flag",  # 标段是否废标标记
        "bid_cancelled_reason",  # 标段废标原因
        "contract_name",  # 合同文件名称
        "contract_ext",  # 合同文件扩展名
        "contract_link_out",  # 合同文件外部链接
        "contract_link_key",  # 合同文件上传ID
    ]

    try:
        log.info(f"开始处理索引 {index_name} 中公告类型为'001'的数据")
        log.info(f"需要设置为null的字段: {fields_to_nullify}")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 统计信息
        stats = {
            "total_found": 0,
            "total_processed": 0,
            "total_updated": 0,
            "total_errors": 0,
            "fields_nullified": fields_to_nullify,
        }

        # 构建查询条件：筛选source_category等于"001"的文档
        query = {
            "query": {"term": {"source_category": "001"}},
            "size": batch_size,
            "_source": ["_id"] + fields_to_nullify,  # 只返回需要的字段
        }

        # 使用scroll API处理大量数据
        scroll_response = es.search(
            index=index_name, body=query, scroll="5m"  # 保持scroll上下文5分钟
        )

        scroll_id = scroll_response.get("_scroll_id")
        total_hits = scroll_response["hits"]["total"]["value"]
        stats["total_found"] = total_hits

        log.info(f"找到 {total_hits} 个公告类型为'001'的文档")

        if total_hits == 0:
            log.info("没有找到需要处理的文档")
            return stats

        if dry_run:
            log.info("试运行模式：只统计，不实际更新数据")
            # 在试运行模式下，统计有多少文档需要更新
            while True:
                hits = scroll_response["hits"]["hits"]
                if not hits:
                    break

                for hit in hits:
                    doc_id = hit["_id"]
                    source = hit.get("_source", {})

                    # 检查是否有字段需要设置为null
                    needs_update = False
                    for field in fields_to_nullify:
                        if source.get(field) is not None:
                            needs_update = True
                            break

                    if needs_update:
                        stats["total_updated"] += 1

                    stats["total_processed"] += 1

                # 获取下一批数据
                scroll_response = es.scroll(scroll_id=scroll_id, scroll="5m")

            log.info(
                f"试运行统计: 总共找到 {stats['total_found']} 个文档，需要更新 {stats['total_updated']} 个文档"
            )

        else:
            # 实际更新模式
            log.info("开始实际更新数据...")

            while True:
                hits = scroll_response["hits"]["hits"]
                if not hits:
                    break

                # 准备批量更新操作
                bulk_body = []

                for hit in hits:
                    doc_id = hit["_id"]
                    source = hit.get("_source", {})

                    # 构建更新文档
                    update_doc = {}
                    needs_update = False

                    for field in fields_to_nullify:
                        if source.get(field) is not None:
                            update_doc[field] = None
                            needs_update = True

                    if needs_update:
                        # 添加到批量操作
                        bulk_body.append(
                            {"update": {"_index": index_name, "_id": doc_id}}
                        )
                        bulk_body.append({"doc": update_doc})
                        stats["total_updated"] += 1

                    stats["total_processed"] += 1

                # 执行批量更新
                if bulk_body:
                    try:
                        bulk_response = es.bulk(body=bulk_body, refresh=True)

                        # 检查批量操作结果
                        if bulk_response.get("errors"):
                            for item in bulk_response["items"]:
                                if "update" in item and "error" in item["update"]:
                                    log.error(
                                        f"更新文档失败: {item['update']['error']}"
                                    )
                                    stats["total_errors"] += 1

                        log.info(f"批量更新完成，本批处理 {len(bulk_body)//2} 个文档")

                    except Exception as e:
                        log.error(f"批量更新失败: {e}")
                        stats["total_errors"] += len(bulk_body) // 2

                # 获取下一批数据
                scroll_response = es.scroll(scroll_id=scroll_id, scroll="5m")

        # 清理scroll上下文
        if scroll_id:
            es.clear_scroll(scroll_id=scroll_id)

        # 输出最终统计
        log.info("=" * 60)
        log.info("处理完成统计:")
        log.info(f"- 找到的文档总数: {stats['total_found']}")
        log.info(f"- 处理的文档总数: {stats['total_processed']}")
        log.info(f"- 更新的文档总数: {stats['total_updated']}")
        log.info(f"- 错误的文档总数: {stats['total_errors']}")
        log.info(f"- 设置为null的字段: {len(fields_to_nullify)} 个")
        log.info("=" * 60)

        return stats

    except Exception as e:
        # 确保清理scroll上下文
        if "scroll_id" in locals() and scroll_id:
            try:
                es.clear_scroll(scroll_id=scroll_id)
            except:
                pass
        raise Exception(f"处理公告类型001数据失败: {e}")


def main() -> None:
    """主函数，用于演示ES操作的基本用法。

    包含以下操作示例：
    1. 初始化ES客户端
    2. 创建索引
    3. 定义映射
    4. 插入测试文档
    5. 更新文档
    6. 搜索文档
    7. 删除文档
    8. 添加和使用别名
    """
    # 初始化Elasticsearch客户端
    es = init_es_client()

    # 加载.env文件
    load_dotenv()

    # 从环境变量获取配置
    es_index_analysis = os.getenv("ES_INDEX_ANALYSIS")
    es_index_analysis_alias = os.getenv("ES_INDEX_ANALYSIS_ALIAS")

    # 演示使用别名功能
    if es_index_analysis and es_index_analysis_alias:
        log.info(
            f"开始演示ES别名功能，索引: {es_index_analysis}, 别名: {es_index_analysis_alias}"
        )

        # 为索引添加别名
        add_alias(es, es_index_analysis, es_index_analysis_alias)

        # 使用别名进行搜索操作
        alias_query = {"query": {"match": {"object_name": "道路改造工程施工"}}}
        log.info(f"使用别名 {es_index_analysis_alias} 进行搜索")
        alias_result = search_documents(
            es, query=alias_query, index_name=es_index_analysis_alias
        )
        log.info(
            f"通过别名搜索到 {alias_result.get('hits', {}).get('total', {}).get('value', 0)} 条结果"
        )
    # es_index_links = os.getenv("ES_INDEX_LINKS")

    # # 创建索引
    # create_index(es, es_index_analysis)

    # # 定义映射
    # define_mapping(es, es_index_analysis)

    # # 创建测试文档
    # test_doc = {
    #     "bid_name": "某市政道路改造工程",
    #     "bid_number": "BID2025001",
    #     "bid_budget": 5000000.00,
    #     "fiscal_delegation_number": "FD2025001",
    #     "prj_addr": "某市某区某街道",
    #     "prj_name": "2025年市政道路改造项目",
    #     "prj_number": "PRJ2025001",
    #     "prj_type": "市政工程",
    #     "release_time": "2025-06-13 14:30:00",
    #     "prj_approval_authority": "某市发展和改革委员会",
    #     "superintendent_office": "某市住房和城乡建设局",
    #     "superintendent_office_code": "JSKF001",
    #     "tenderee": "某市市政工程管理处",
    #     "bid_submission_deadline": "2025-07-13 17:00:00",
    #     "trade_platform": "某市公共资源交易中心",
    #     "procurement_method": "公开招标",
    #     "prj_sub_type": "市政基础设施",
    #     "province": "江苏省",
    #     "city": "南京市",
    #     "county": "玄武区",
    #     "announcement_type": "招标公告",
    #     "object_name": "道路改造工程施工",
    #     "object_brand": "不适用",
    #     "object_model": "不适用",
    #     "object_supplier": "待定",
    #     "object_produce_area": "不适用",
    #     "object_conf": "详见招标文件技术要求",
    #     "object_oem": "不适用",
    #     "object_amount": 1,
    #     "object_unit": "项",
    #     "object_price": 5000000.00,
    #     "object_total_price": 5000000.00,
    #     "object_maintenance_period": "2年",
    #     "object_price_source": "工程量清单",
    #     "object_quality": "符合国家相关标准规范",
    #     "bidder_price": 3672192,
    #     "bidder_name": "泸州乐禾食品有限公司",
    #     "bidder_contact_person": "唐佳彤",
    #     "bidder_contact_phone_number": "18783062530",
    #     "bidder_contract_config_param": "普通病员餐（单价）：13.00元 流质老年病员餐（单价）：13.00元 软食老年病员餐（单价）：14.20元 职工自助午餐（单价）：14.50元 职工晚餐（单价）：7.30元 三无病员加餐（单价）：3.50元 工勤人员餐（单价）：12.50元",
    #     "agent": "某工程咨询有限公司",
    #     "service_fee": 50000.00,
    #     "source_id": "uJvIPpcBfqDgtia46Tva",
    #     "source_create_time": "2025-06-05",
    #     "source_category": "004",
    #     "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250604_24709578.htm",
    #     "source_appendix": [
    #         {
    #             "url": "https://download.ccgp.gov.cn/oss/download?uuid=79c1ed4d-6de7-4833-9432-1cf21d",
    #             "text": "企业类型声明函",
    #         },
    #         {
    #             "url": "https://download.ccgp.gov.cn/oss/download?uuid=ac5feaf8-df52-462c-a156-c1ac59",
    #             "text": "年度两院区手术室导管室敷料采购项目",
    #         },
    #     ],
    #     "insert_time": "2025-06-17 03:26:00",
    # }

    # # 插入测试文档
    # insert_document(es, es_index_analysis, doc_id="TEST001", document=test_doc)

    # # 更新文档
    # # 注意：这里假设我们知道文档的ID。实际使用时可能需要通过搜索等方式来确定ID
    # update_document(
    #     es,
    #     es_index_analysis,
    #     doc_id="TEST001",
    #     updated_doc={"bidder_name": "合肥市XX公司"},
    # )

    # # 搜索文档
    # query = {"query": {"match": {"object_name": "道路改造工程施工"}}}
    # search_result = search_documents(es, es_index_analysis, query=query)
    # log.info(search_result)

    # # 插入新字段
    # add_field_to_mapping(
    #     es, es_index_analysis_alias, "source_response", {"type": "text"}
    # )

    #     # 删除文档
    #     delete_document(es, doc_id="1")

    # remove_field_by_reindex(
    #     es,
    #     old_index="markersweb_attachment_analysis_v2_20250630_164112",
    #     new_index="markersweb_attachment_analysis_v3",
    #     remove_fields=["source_response"],
    # )

    # rebuild_index_with_new_field_types(
    #     es,
    #     old_index="markersweb_attachment_analysis",
    #     new_index="markersweb_attachment_analysis_v2",
    #     field_type_map={
    #         "object_price": {"type": "text"},
    #         "object_amount": {"type": "keyword"},
    #     },
    # )


if __name__ == "__main__":
    main()
