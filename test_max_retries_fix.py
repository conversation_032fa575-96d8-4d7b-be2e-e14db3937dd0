#!/usr/bin/env python3
"""
测试最大重试次数修复

验证修复内容：
1. DocumentAnalyzer的max_retries默认值从3改为2
2. 错误检查逻辑从"已重试3次"改为"已重试2次"
3. 确保重试次数一致性
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_noappendix import DocumentAnalyzer, llm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def test_max_retries_consistency():
    """测试最大重试次数的一致性"""
    log.info("=" * 80)
    log.info("测试最大重试次数一致性")
    log.info("=" * 80)
    
    # 测试1：检查DocumentAnalyzer的默认max_retries值
    log.info("测试1：检查DocumentAnalyzer的默认max_retries值")
    
    # 创建模拟的ES客户端
    mock_es = Mock()
    
    # 创建DocumentAnalyzer实例，不传入max_retries参数，使用默认值
    analyzer = DocumentAnalyzer(
        es_client=mock_es,
        es_index_links="test_links",
        es_index_analysis="test_analysis",
        model_apikey="test_key",
        model_name="test_model",
        model_url="test_url",
        prompt_spec="test_prompt"
        # 注意：这里没有传入max_retries，应该使用默认值2
    )
    
    # 验证默认值
    assert analyzer.max_retries == 2, f"期望max_retries默认值为2，实际为{analyzer.max_retries}"
    log.info(f"✓ DocumentAnalyzer默认max_retries值正确: {analyzer.max_retries}")
    
    # 测试2：检查llm函数的默认max_retries值
    log.info("\n测试2：检查llm函数的默认max_retries值")
    
    # 通过inspect模块检查函数签名
    import inspect
    sig = inspect.signature(llm)
    max_retries_param = sig.parameters['max_retries']
    default_value = max_retries_param.default
    
    assert default_value == 2, f"期望llm函数max_retries默认值为2，实际为{default_value}"
    log.info(f"✓ llm函数默认max_retries值正确: {default_value}")
    
    log.info("\n✅ 最大重试次数一致性测试通过")

def test_retry_behavior():
    """测试重试行为"""
    log.info("\n" + "=" * 80)
    log.info("测试重试行为")
    log.info("=" * 80)
    
    # 计数器，记录API调用次数
    call_count = 0
    
    def mock_openai_create(**kwargs):
        nonlocal call_count
        call_count += 1
        log.info(f"模拟API调用第 {call_count} 次")
        
        # 前2次调用失败，第3次不应该被调用（因为max_retries=2）
        if call_count <= 2:
            raise Exception("504 Gateway Timeout")
        else:
            # 这不应该被执行到
            return Mock(choices=[Mock(message=Mock(content="success"))])
    
    # 模拟OpenAI客户端
    mock_client = Mock()
    mock_client.chat.completions.create = mock_openai_create
    
    with patch('analyse_noappendix.OpenAI', return_value=mock_client):
        try:
            # 调用llm函数，使用默认的max_retries=2
            result = llm(
                messages=[{"role": "user", "content": "test"}],
                model_name="test_model",
                model_apikey="test_key",
                model_url="test_url"
                # 不传入max_retries，使用默认值2
            )
            log.error("❌ 期望抛出异常，但函数成功返回了")
            assert False, "期望抛出异常"
        except Exception as e:
            error_msg = str(e)
            log.info(f"捕获到期望的异常: {error_msg}")
            
            # 验证错误消息
            assert "已重试2次" in error_msg, f"期望错误消息包含'已重试2次'，实际: {error_msg}"
            log.info("✓ 错误消息正确包含'已重试2次'")
            
            # 验证调用次数
            assert call_count == 2, f"期望API被调用2次，实际调用{call_count}次"
            log.info(f"✓ API调用次数正确: {call_count}次")
    
    log.info("\n✅ 重试行为测试通过")

def test_custom_max_retries():
    """测试自定义max_retries值"""
    log.info("\n" + "=" * 80)
    log.info("测试自定义max_retries值")
    log.info("=" * 80)
    
    # 测试1：DocumentAnalyzer自定义max_retries
    log.info("测试1：DocumentAnalyzer自定义max_retries")
    
    mock_es = Mock()
    
    # 创建DocumentAnalyzer实例，传入自定义max_retries值
    analyzer = DocumentAnalyzer(
        es_client=mock_es,
        es_index_links="test_links",
        es_index_analysis="test_analysis",
        model_apikey="test_key",
        model_name="test_model",
        model_url="test_url",
        prompt_spec="test_prompt",
        max_retries=5  # 自定义值
    )
    
    assert analyzer.max_retries == 5, f"期望max_retries为5，实际为{analyzer.max_retries}"
    log.info(f"✓ DocumentAnalyzer自定义max_retries值正确: {analyzer.max_retries}")
    
    # 测试2：llm函数自定义max_retries
    log.info("\n测试2：llm函数自定义max_retries")
    
    call_count = 0
    
    def mock_openai_create(**kwargs):
        nonlocal call_count
        call_count += 1
        log.info(f"模拟API调用第 {call_count} 次")
        
        # 前4次调用失败，第5次不应该被调用（因为max_retries=4）
        if call_count <= 4:
            raise Exception("504 Gateway Timeout")
        else:
            return Mock(choices=[Mock(message=Mock(content="success"))])
    
    mock_client = Mock()
    mock_client.chat.completions.create = mock_openai_create
    
    with patch('analyse_noappendix.OpenAI', return_value=mock_client):
        try:
            # 调用llm函数，传入自定义max_retries=4
            result = llm(
                messages=[{"role": "user", "content": "test"}],
                model_name="test_model",
                model_apikey="test_key",
                model_url="test_url",
                max_retries=4  # 自定义值
            )
            log.error("❌ 期望抛出异常，但函数成功返回了")
            assert False, "期望抛出异常"
        except Exception as e:
            error_msg = str(e)
            log.info(f"捕获到期望的异常: {error_msg}")
            
            # 验证错误消息
            assert "已重试4次" in error_msg, f"期望错误消息包含'已重试4次'，实际: {error_msg}"
            log.info("✓ 错误消息正确包含'已重试4次'")
            
            # 验证调用次数
            assert call_count == 4, f"期望API被调用4次，实际调用{call_count}次"
            log.info(f"✓ API调用次数正确: {call_count}次")
    
    log.info("\n✅ 自定义max_retries值测试通过")

def main():
    """主测试函数"""
    log.info("开始最大重试次数修复测试")
    log.info("=" * 100)
    
    try:
        # 运行所有测试
        test_max_retries_consistency()
        test_retry_behavior()
        test_custom_max_retries()
        
        log.info("\n" + "=" * 100)
        log.info("🎉 所有最大重试次数修复测试通过！")
        log.info("=" * 100)
        
        log.info("\n修复总结:")
        log.info("✓ DocumentAnalyzer默认max_retries从3改为2")
        log.info("✓ llm函数默认max_retries保持为2")
        log.info("✓ 错误检查逻辑从'已重试3次'改为'已重试2次'")
        log.info("✓ 重试次数一致性得到保证")
        log.info("✓ 自定义max_retries值正常工作")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
