#!/usr/bin/env python3
"""
测试文件元数据字段保留功能

验证在文档融合过程中，主解析结果中的文件元数据字段（如bid_doc_name, contract_name等）
不会被附件解析结果中的null值覆盖。
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import merge_analysis, intelligent_merge_analysis


def test_file_metadata_preservation():
    """测试文件元数据字段保留功能"""

    print("=" * 80)
    print("测试文件元数据字段保留功能")
    print("=" * 80)

    # 测试用例1：主结果有文件元数据，招标文件解析结果为null
    print("\n测试用例1：主结果有招标文件元数据，招标文件解析结果为null")
    print("-" * 60)

    main_result = {
        "object_name": "医疗设备",
        "prj_name": "医院设备采购项目",
        "bid_doc_name": "招标文件.pdf",
        "bid_doc_ext": "pdf",
        "bid_doc_link_out": "http://example.com/tender.pdf",
        "bid_doc_link_key": "tender_123",
        "tenderee": None,
        "bidder_name": None,
    }

    tender_result = {
        "object_name": "医疗设备",
        "tenderee": "某市人民医院",
        "bid_doc_name": None,
        "bid_doc_ext": None,
        "bid_doc_link_out": None,
        "bid_doc_link_key": None,
    }

    contract_result = {
        "bidder_name": "医疗设备公司",
        "contract_name": None,
        "contract_ext": None,
        "contract_link_out": None,
        "contract_link_key": None,
    }

    merged = merge_analysis(main_result, tender_result, contract_result)

    print("主结果文件元数据:")
    print(f"  bid_doc_name: {main_result['bid_doc_name']}")
    print(f"  bid_doc_ext: {main_result['bid_doc_ext']}")
    print(f"  bid_doc_link_out: {main_result['bid_doc_link_out']}")
    print(f"  bid_doc_link_key: {main_result['bid_doc_link_key']}")

    print("\n招标文件解析结果文件元数据:")
    print(f"  bid_doc_name: {tender_result['bid_doc_name']}")
    print(f"  bid_doc_ext: {tender_result['bid_doc_ext']}")
    print(f"  bid_doc_link_out: {tender_result['bid_doc_link_out']}")
    print(f"  bid_doc_link_key: {tender_result['bid_doc_link_key']}")

    print("\n融合后文件元数据:")
    print(f"  bid_doc_name: {merged['bid_doc_name']}")
    print(f"  bid_doc_ext: {merged['bid_doc_ext']}")
    print(f"  bid_doc_link_out: {merged['bid_doc_link_out']}")
    print(f"  bid_doc_link_key: {merged['bid_doc_link_key']}")

    # 验证文件元数据是否被保留
    assert (
        merged["bid_doc_name"] == "招标文件.pdf"
    ), f"bid_doc_name应该保留主结果值，实际: {merged['bid_doc_name']}"
    assert (
        merged["bid_doc_ext"] == "pdf"
    ), f"bid_doc_ext应该保留主结果值，实际: {merged['bid_doc_ext']}"
    assert (
        merged["bid_doc_link_out"] == "http://example.com/tender.pdf"
    ), f"bid_doc_link_out应该保留主结果值，实际: {merged['bid_doc_link_out']}"
    assert (
        merged["bid_doc_link_key"] == "tender_123"
    ), f"bid_doc_link_key应该保留主结果值，实际: {merged['bid_doc_link_key']}"

    # 验证其他字段正常补充
    assert (
        merged["tenderee"] == "某市人民医院"
    ), f"tenderee应该从招标文件补充，实际: {merged['tenderee']}"
    assert (
        merged["bidder_name"] == "医疗设备公司"
    ), f"bidder_name应该从合同文件补充，实际: {merged['bidder_name']}"

    print("\n✅ 测试用例1通过：主结果中的招标文件元数据被正确保留")

    # 测试用例2：主结果有合同文件元数据，合同文件解析结果为null
    print("\n测试用例2：主结果有合同文件元数据，合同文件解析结果为null")
    print("-" * 60)

    main_result2 = {
        "object_name": "保安服务",
        "prj_name": "保安服务采购项目",
        "contract_name": "服务合同.pdf",
        "contract_ext": "pdf",
        "contract_link_out": "http://example.com/contract.pdf",
        "contract_link_key": "contract_456",
        "bidder_price": None,
    }

    contract_result2 = {
        "bidder_price": 100000.0,
        "contract_name": None,
        "contract_ext": None,
        "contract_link_out": None,
        "contract_link_key": None,
    }

    merged2 = merge_analysis(main_result2, None, contract_result2)

    print("主结果合同文件元数据:")
    print(f"  contract_name: {main_result2['contract_name']}")
    print(f"  contract_ext: {main_result2['contract_ext']}")
    print(f"  contract_link_out: {main_result2['contract_link_out']}")
    print(f"  contract_link_key: {main_result2['contract_link_key']}")

    print("\n合同文件解析结果文件元数据:")
    print(f"  contract_name: {contract_result2['contract_name']}")
    print(f"  contract_ext: {contract_result2['contract_ext']}")
    print(f"  contract_link_out: {contract_result2['contract_link_out']}")
    print(f"  contract_link_key: {contract_result2['contract_link_key']}")

    print("\n融合后合同文件元数据:")
    print(f"  contract_name: {merged2['contract_name']}")
    print(f"  contract_ext: {merged2['contract_ext']}")
    print(f"  contract_link_out: {merged2['contract_link_out']}")
    print(f"  contract_link_key: {merged2['contract_link_key']}")

    # 验证合同文件元数据是否被保留
    assert (
        merged2["contract_name"] == "服务合同.pdf"
    ), f"contract_name应该保留主结果值，实际: {merged2['contract_name']}"
    assert (
        merged2["contract_ext"] == "pdf"
    ), f"contract_ext应该保留主结果值，实际: {merged2['contract_ext']}"
    assert (
        merged2["contract_link_out"] == "http://example.com/contract.pdf"
    ), f"contract_link_out应该保留主结果值，实际: {merged2['contract_link_out']}"
    assert (
        merged2["contract_link_key"] == "contract_456"
    ), f"contract_link_key应该保留主结果值，实际: {merged2['contract_link_key']}"

    # 验证其他字段正常补充
    assert (
        merged2["bidder_price"] == 100000.0
    ), f"bidder_price应该从合同文件补充，实际: {merged2['bidder_price']}"

    print("\n✅ 测试用例2通过：主结果中的合同文件元数据被正确保留")

    # 测试用例3：主结果文件元数据为空，应该使用附件解析结果
    print("\n测试用例3：主结果文件元数据为空，应该使用附件解析结果")
    print("-" * 60)

    main_result3 = {
        "object_name": "清洁服务",
        "prj_name": "清洁服务采购项目",
        "bid_doc_name": None,
        "bid_doc_ext": None,
        "bid_doc_link_out": None,
        "bid_doc_link_key": None,
    }

    tender_result3 = {
        "object_name": "清洁服务",
        "bid_doc_name": "招标文件_清洁服务.pdf",
        "bid_doc_ext": "pdf",
        "bid_doc_link_out": "http://example.com/tender_clean.pdf",
        "bid_doc_link_key": "tender_789",
    }

    merged3 = merge_analysis(main_result3, tender_result3, None)

    print("主结果文件元数据:")
    print(f"  bid_doc_name: {main_result3['bid_doc_name']}")
    print(f"  bid_doc_ext: {main_result3['bid_doc_ext']}")

    print("\n招标文件解析结果文件元数据:")
    print(f"  bid_doc_name: {tender_result3['bid_doc_name']}")
    print(f"  bid_doc_ext: {tender_result3['bid_doc_ext']}")

    print("\n融合后文件元数据:")
    print(f"  bid_doc_name: {merged3['bid_doc_name']}")
    print(f"  bid_doc_ext: {merged3['bid_doc_ext']}")

    # 验证空的文件元数据被正确补充
    assert (
        merged3["bid_doc_name"] == "招标文件_清洁服务.pdf"
    ), f"bid_doc_name应该使用招标文件值，实际: {merged3['bid_doc_name']}"
    assert (
        merged3["bid_doc_ext"] == "pdf"
    ), f"bid_doc_ext应该使用招标文件值，实际: {merged3['bid_doc_ext']}"

    print("\n✅ 测试用例3通过：主结果为空时正确使用附件解析结果")

    print("\n" + "=" * 80)
    print("🎉 所有测试用例通过！文件元数据保留功能正常工作")
    print("=" * 80)


def test_intelligent_merge_preservation():
    """测试智能融合分析中的文件元数据保留"""

    print("\n" + "=" * 80)
    print("测试智能融合分析中的文件元数据保留功能")
    print("=" * 80)

    main_list = [
        {
            "object_name": "医疗设备",
            "prj_name": "医院设备采购项目",
            "bid_doc_name": "招标文件.pdf",
            "bid_doc_ext": "pdf",
            "bid_doc_link_out": "http://example.com/tender.pdf",
            "bid_doc_link_key": "tender_123",
            "contract_name": "合同文件.pdf",
            "contract_ext": "pdf",
            "contract_link_out": "http://example.com/contract.pdf",
            "contract_link_key": "contract_456",
            "tenderee": None,
            "bidder_name": None,
        }
    ]

    tender_list = [
        {
            "object_name": "医疗设备",
            "tenderee": "某市人民医院",
            "bid_doc_name": None,
            "bid_doc_ext": None,
            "bid_doc_link_out": None,
            "bid_doc_link_key": None,
        }
    ]

    contract_list = [
        {
            "object_name": "医疗设备",  # 添加object_name以便匹配
            "bidder_name": "医疗设备公司",
            "contract_name": None,
            "contract_ext": None,
            "contract_link_out": None,
            "contract_link_key": None,
        }
    ]

    # 不提供模型配置，只进行基础融合
    results = intelligent_merge_analysis(
        main_list=main_list, tender_list=tender_list, contract_list=contract_list
    )

    assert len(results) == 1, f"应该返回1个结果，实际: {len(results)}"

    result = results[0]

    print("智能融合结果文件元数据:")
    print(f"  bid_doc_name: {result['bid_doc_name']}")
    print(f"  bid_doc_ext: {result['bid_doc_ext']}")
    print(f"  bid_doc_link_out: {result['bid_doc_link_out']}")
    print(f"  bid_doc_link_key: {result['bid_doc_link_key']}")
    print(f"  contract_name: {result['contract_name']}")
    print(f"  contract_ext: {result['contract_ext']}")
    print(f"  contract_link_out: {result['contract_link_out']}")
    print(f"  contract_link_key: {result['contract_link_key']}")

    # 验证文件元数据被保留
    assert (
        result["bid_doc_name"] == "招标文件.pdf"
    ), f"bid_doc_name应该保留主结果值，实际: {result['bid_doc_name']}"
    assert (
        result["contract_name"] == "合同文件.pdf"
    ), f"contract_name应该保留主结果值，实际: {result['contract_name']}"

    # 验证其他字段正常补充
    assert (
        result["tenderee"] == "某市人民医院"
    ), f"tenderee应该从招标文件补充，实际: {result['tenderee']}"
    assert (
        result["bidder_name"] == "医疗设备公司"
    ), f"bidder_name应该从合同文件补充，实际: {result['bidder_name']}"

    print("\n✅ 智能融合分析中的文件元数据保留功能正常工作")


if __name__ == "__main__":
    try:
        test_file_metadata_preservation()
        test_intelligent_merge_preservation()
        print("\n🎉 所有测试通过！修复成功！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
