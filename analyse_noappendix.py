import os
from es_deal import init_es_client, search_documents, insert_document
from dotenv import load_dotenv
from datetime import datetime
import json
from typing import List, Dict, Optional
from openai import OpenAI
from utils.log_cfg import log
from markdownify import markdownify as md
from blacklist_manager import BlacklistManager, print_blacklist_stats

# 标准字段列表 - 所有插入ES的文档都应包含这些字段
STANDARD_FIELDS = (
    # 业务数据字段（40个）
    "bid_name",  # 标段名称
    "bid_number",  # 标段编号
    "bid_budget",  # 标段预算金额
    "fiscal_delegation_number",  # 财政委托编号
    "prj_addr",  # 招标项目地址
    "prj_name",  # 招标项目名称
    "prj_number",  # 招标项目编号
    "prj_type",  # 招标项目类型
    "release_time",  # 发布日期
    "prj_approval_authority",  # 项目审批单位
    "superintendent_office",  # 监督部门
    "superintendent_office_code",  # 监督部门编号
    "tenderee",  # 招标人
    "bid_submission_deadline",  # 投标截止时间
    "trade_platform",  # 交易平台
    "procurement_method",  # 采购方式
    "prj_sub_type",  # 项目细分类型
    "province",  # 省份
    "city",  # 城市
    "county",  # 区县
    "announcement_type",  # 公告类型
    "object_name",  # 标的物名称
    "object_brand",  # 标的物品牌
    "object_model",  # 标的物型号
    "object_supplier",  # 标的物供应商
    "object_produce_area",  # 标的物产地
    "object_conf",  # 标的物配置参数
    "object_oem",  # 标的物OEM厂家
    "object_amount",  # 标的物数量
    "object_unit",  # 标的物单位
    "object_price",  # 标的物单价
    "object_total_price",  # 标的物总价
    "object_maintenance_period",  # 标的物维保期限
    "object_price_source",  # 标的物价格来源
    "object_quality",  # 标的物质量层次
    "agent",  # 代理机构
    "service_fee",  # 代理服务收费金额
    "bidder_price",  # 中标金额
    "bidder_name",  # 中标单位名称
    "bidder_contact_person",  # 中标单位联系人
    "bidder_contact_phone_number",  # 中标单位联系人电话
    "bidder_contract_config_param",  # 中标合同配置参数
    "bid_cancelled_flag",  # 标段是否废标标记
    "bid_cancelled_reason",  # 标段废标原因
    # 源数据元数据字段（6个）
    "source_id",  # 源文档ID
    "source_title",  # 源文档标题
    "source_create_time",  # 源文档创建时间
    "source_category",  # 源文档分类
    "source_url",  # 源文档URL
    "source_appendix",  # 源文档附件信息
    # 附件相关字段（8个）
    "bid_doc_name",  # 招标文件名称
    "bid_doc_ext",  # 招标文件扩展名
    "bid_doc_link_out",  # 招标文件外部链接
    "bid_doc_link_key",  # 招标文件上传ID
    "contract_name",  # 合同文件名称
    "contract_ext",  # 合同文件扩展名
    "contract_link_out",  # 合同文件外部链接
    "contract_link_key",  # 合同文件上传ID
    # 系统字段（1个）
    "insert_time",  # 插入时间
)

# 招标公告(001)类型允许的字段列表
ANNOUNCEMENT_001_FIELDS = (
    # 业务数据字段
    "bid_name",  # 标段名称
    "bid_number",  # 标段编号
    "bid_budget",  # 标段预算金额
    "fiscal_delegation_number",  # 财政委托编号
    "prj_addr",  # 招标项目地址
    "prj_name",  # 招标项目名称
    "prj_number",  # 招标项目编号
    "prj_type",  # 招标项目类型
    "release_time",  # 发布日期
    "prj_approval_authority",  # 项目审批单位
    "superintendent_office",  # 监督部门
    "superintendent_office_code",  # 监督部门编号
    "tenderee",  # 招标人
    "bid_submission_deadline",  # 投标截止时间
    "trade_platform",  # 交易平台
    "procurement_method",  # 采购方式
    "prj_sub_type",  # 项目细分类型
    "province",  # 省份
    "city",  # 城市
    "county",  # 区县
    "announcement_type",  # 公告类型
    "object_name",  # 标的物名称
    "object_brand",  # 标的物品牌
    "object_model",  # 标的物型号
    "object_supplier",  # 标的物供应商
    "object_produce_area",  # 标的物产地
    "object_conf",  # 标的物配置参数
    "object_oem",  # 标的物OEM厂家
    "object_amount",  # 标的物数量
    "object_unit",  # 标的物单位
    "object_price",  # 标的物单价
    "object_total_price",  # 标的物总价
    "object_maintenance_period",  # 标的物维保期限
    "object_price_source",  # 标的物价格来源
    "object_quality",  # 标的物质量层次
    "agent",  # 代理机构
    "service_fee",  # 代理服务收费金额
    # 源数据元数据字段
    "source_id",  # 源文档ID
    "source_title",  # 源文档标题
    "source_create_time",  # 源文档创建时间
    "source_category",  # 源文档分类
    "source_url",  # 源文档URL
    "source_appendix",  # 源文档附件信息
    # 附件相关字段
    "bid_doc_name",  # 招标文件名称
    "bid_doc_ext",  # 招标文件扩展名
    "bid_doc_link_out",  # 招标文件外部链接
    "bid_doc_link_key",  # 招标文件上传ID
    # 系统字段
    "insert_time",  # 插入时间
)


def validate_and_normalize_fields(
    document: dict, announcement_type: str = None
) -> dict:
    """
    校验和标准化文档字段，根据公告类型应用不同的字段策略

    Args:
        document: 待插入ES的文档字典
        announcement_type: 公告类型，用于确定字段策略

    Returns:
        dict: 经过校验和标准化的文档字典
    """
    if not isinstance(document, dict):
        log.warning(f"文档不是字典类型: {type(document)}")
        return {}

    # 根据公告类型确定字段策略
    if announcement_type in ["001", "002", "003"]:
        log.info(f"公告类型: {announcement_type}，将使用限定字段策略")
    elif announcement_type in ["004", "010", "999"]:
        log.info(f"公告类型: {announcement_type}，将使用完整字段策略")

    # 获取当前文档的字段
    current_fields = set(document.keys())
    standard_fields_set = set(STANDARD_FIELDS)

    # 检查多余字段（超出标准字段的字段）
    extra_fields = current_fields - standard_fields_set
    if extra_fields:
        log.info(f"发现超出标准字段的字段，将被忽略: {sorted(extra_fields)}")

    # 检查缺失字段
    missing_fields = standard_fields_set - current_fields
    if missing_fields:
        log.info(f"发现缺失字段，将补全为None: {sorted(missing_fields)}")

    # 创建标准化的文档 - 始终包含所有标准字段
    normalized_document = {}

    # 按照标准字段顺序重新组织
    for field in STANDARD_FIELDS:
        if field in document:
            # 字段存在于输入文档中
            if announcement_type in ["001", "002", "003"]:
                # 招标公告：只有允许的字段保留值，其他设为None
                if field in ANNOUNCEMENT_001_FIELDS:
                    normalized_document[field] = document[field]
                else:
                    normalized_document[field] = None
            else:
                # 其他公告类型：保留所有字段的值
                normalized_document[field] = document[field]
        else:
            # 字段不存在于输入文档中，设为None
            normalized_document[field] = None

    # 统计信息
    total_fields = len(STANDARD_FIELDS)
    valid_fields = len([v for v in normalized_document.values() if v is not None])

    if announcement_type in ["001", "002", "003"]:
        allowed_fields_count = len(ANNOUNCEMENT_001_FIELDS)
        log.info(
            f"字段校验完成: 标准字段{total_fields}个, 有效字段{valid_fields}个, 001类型允许字段{allowed_fields_count}个"
        )
    else:
        log.info(f"字段校验完成: 标准字段{total_fields}个, 有效字段{valid_fields}个")

    return normalized_document


def clean_json_data(json_str: str) -> str:
    """清理JSON字符串中的重复字段

    Args:
        json_str: 原始JSON字符串

    Returns:
        清理后的JSON字符串
    """
    try:
        # 先尝试解析JSON
        data = json.loads(json_str)

        # 如果是字典，确保没有重复键
        if isinstance(data, dict):
            return json.dumps(data)

        # 如果是列表，处理每个字典
        elif isinstance(data, list):
            cleaned_data = []
            for item in data:
                if isinstance(item, dict):
                    # 使用dict.fromkeys去重
                    cleaned_item = dict.fromkeys(item.keys())
                    # 保留最后一个值
                    for key in item:
                        cleaned_item[key] = item[key]
                    cleaned_data.append(cleaned_item)
            return json.dumps(cleaned_data)

        return json_str
    except json.JSONDecodeError as e:
        # 如果JSON解析失败，尝试修复
        log.warning(f"JSON解析失败，开始清理: {e}")

        # 1. 替换中文标点符号和特殊字符
        cleaned = json_str.replace("，", ",")  # 中文逗号
        cleaned = cleaned.replace("：", ":")  # 中文冒号
        cleaned = cleaned.replace('"', '"')  # 中文左引号
        cleaned = cleaned.replace('"', '"')  # 中文右引号
        cleaned = cleaned.replace("'", "'")  # 中文左单引号
        cleaned = cleaned.replace("'", "'")  # 中文右单引号

        # 替换全角数字为半角数字（包括在数值中的全角数字）
        fullwidth_digits = "０１２３４５６７８９"
        halfwidth_digits = "0123456789"
        for fw, hw in zip(fullwidth_digits, halfwidth_digits):
            cleaned = cleaned.replace(fw, hw)

        # 2. 移除特殊空白字符
        import re

        # 移除全角空格和其他特殊空白字符
        cleaned = re.sub(r"[　\u3000\ufeff]+", " ", cleaned)
        # 移除行末多余的空白和特殊字符
        cleaned = re.sub(r"\s*[　\s]*$", "", cleaned, flags=re.MULTILINE)

        # 3. 智能处理重复字段（只在同一对象内去重）
        lines = cleaned.split("\n")
        cleaned_lines = []
        current_object_fields = set()
        brace_level = 0

        for line in lines:
            # 跳过空行
            if not line.strip():
                continue

            stripped_line = line.strip()

            # 跟踪大括号层级
            brace_level += stripped_line.count("{") - stripped_line.count("}")

            # 如果遇到新对象的开始，重置字段集合
            if "{" in stripped_line and brace_level <= 1:
                current_object_fields = set()

            if ":" in line:
                field = line.split(":")[0].strip().strip('"')
                if field:
                    # 只在当前对象内检查重复
                    if field not in current_object_fields:
                        current_object_fields.add(field)
                        cleaned_lines.append(line)
                    # 如果是重复字段，跳过
                else:
                    # 空字段名，保留
                    cleaned_lines.append(line)
            else:
                # 非字段行（如括号、逗号等），直接保留
                cleaned_lines.append(line)

        result = "\n".join(cleaned_lines)

        # 4. 修复常见的JSON格式问题
        # 移除对象结束前的多余逗号
        result = re.sub(r",(\s*})", r"\1", result)
        # 移除数组结束前的多余逗号
        result = re.sub(r",(\s*])", r"\1", result)
        # 修复字段名后多余的空格
        result = re.sub(r'"\s*:', r'":', result)
        # 修复缺少逗号的问题（在字符串后面跟着新行和引号的情况）
        result = re.sub(r'"\s*\n\s*"', r'",\n"', result)
        # 修复冒号前后的空格问题
        result = re.sub(r'"\s*：\s*', r'": ', result)
        # 修复冒号后缺少空格的问题
        result = re.sub(r'":([^"\s])', r'": \1', result)
        # 修复不完整的数值（如 13779. 变为 13779.0）
        result = re.sub(r"(\d+\.)(\s*[,}\]])", r"\g<1>0\2", result)
        # 移除单独成行的逗号
        result = re.sub(r"\n\s*,\s*\n", r"\n", result)
        # 修复行首的逗号
        result = re.sub(r'\n\s*,\s*"', r',\n"', result)
        # 修复字段名前的空格（如 " object_model" -> "object_model"）
        result = re.sub(r'"\s+([^"]*?)"\s*:', r'"\1":', result)
        # 修复字段名后的空格（如 "object_model " -> "object_model"）
        result = re.sub(r'"([^"]*?)\s+"\s*:', r'"\1":', result)

        # 5. 处理JSON截断问题
        # 清理不完整的最后部分
        lines = result.split("\n")
        cleaned_lines = []

        for i, line in enumerate(lines):
            stripped = line.strip()
            # 跳过只包含引号和空白的不完整行
            if stripped and not stripped in ['"', '""', '",', '":', '" ']:
                # 检查是否是不完整的字段定义
                if (
                    stripped.startswith('"')
                    and ":" not in stripped
                    and i == len(lines) - 1
                ):
                    # 最后一行是不完整的字段名，跳过
                    continue
                cleaned_lines.append(line)

        result = "\n".join(cleaned_lines)

        # 检查是否有不完整的对象或数组
        if result.strip() and not result.strip().endswith(("]", "}")):
            # 尝试修复截断的JSON
            result = result.rstrip()
            # 如果最后一行是不完整的，尝试补全
            if result.endswith(","):
                result = result.rstrip(",")

            # 计算未闭合的括号
            open_braces = result.count("{") - result.count("}")
            open_brackets = result.count("[") - result.count("]")

            # 补全缺失的闭合括号
            for _ in range(open_braces):
                result += "\n}"
            for _ in range(open_brackets):
                result += "\n]"

        # 6. 尝试再次解析
        try:
            data = json.loads(result)
            return json.dumps(data, ensure_ascii=False, indent=2)
        except json.JSONDecodeError as e2:
            log.error(f"清理后仍无法解析JSON: {e2}")
            # 尝试最后的修复：移除问题行和修复格式
            lines = result.split("\n")
            final_lines = []
            for line in lines:
                stripped = line.strip()
                # 跳过只有逗号或空白的行
                if stripped and stripped not in [",", "，"]:
                    # 修复行中的问题
                    if '":' in line and not '": ' in line:
                        line = line.replace('":', '": ')
                    final_lines.append(line)

            final_result = "\n".join(final_lines)

            # 最后一次尝试修复截断
            if final_result.strip() and not final_result.strip().endswith(("]", "}")):
                open_braces = final_result.count("{") - final_result.count("}")
                open_brackets = final_result.count("[") - final_result.count("]")
                for _ in range(open_braces):
                    final_result += "\n}"
                for _ in range(open_brackets):
                    final_result += "\n]"

            try:
                data = json.loads(final_result)
                return json.dumps(data, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                return final_result


def process_json_data(json_str: str) -> dict:
    """处理JSON数据

    Args:
        json_str: 原始JSON字符串

    Returns:
        解析后的JSON数据
    """
    try:
        # 清理JSON数据
        cleaned_json = clean_json_data(json_str)
        # 解析JSON
        return json.loads(cleaned_json)
    except json.JSONDecodeError as e:
        log.error(f"JSON解析失败: {e}")
        raise


def clean_json_markdown(result: str) -> List[str]:
    """
    去除 markdown 代码块标记，返回多个json字符串列表

    Args:
        result: 包含多个json的markdown字符串

    Returns:
        List[str]: 清理后的json字符串列表
    """
    # 按```json分割
    parts = result.split("```json")
    json_strings = []

    for part in parts:
        if not part.strip():
            continue

        # 去除可能的```结尾
        if "```" in part:
            part = part.split("```")[0]

        # 清理并添加到列表
        cleaned = part.strip()
        if cleaned:
            json_strings.append(cleaned)

    return json_strings


def llm(
    messages: List[Dict[str, str]],
    model_name: str = "Qwen/Qwen2.5-32B-Instruct",
    model_apikey: Optional[str] = None,
    model_url: str = "https://api-inference.modelscope.cn/v1",
    temperature: float = 0,  # 新增温度参数，默认为0.7
    max_output_tokens: int = 8192,  # 新增最大输出token数，默认为150
    timeout: int = 300,  # 超时时间，默认5分钟
    max_retries: int = 2,  # 最大重试次数
) -> str:
    """
    用 OpenAI 兼容API进行多轮对话，一次性返回全部内容。

    Args:
        messages (List[Dict[str, str]]): context7格式的历史消息，形如
            [
                {"role": "system", "content": "你是助手"},
                {"role": "user", "content": "问题1"},
                {"role": "assistant", "content": "回答1"},
                {"role": "user", "content": "问题2"}
            ]
        model_name (str): 模型ID，默认Qwen2.5-VL-72B-Instruct。
        model_apikey (Optional[str]): Token。
        model_url (str): API地址，默认OpenAI兼容地址。

    Returns:
        str: LLM生成的完整回复内容。

    Raises:
        Exception: API调用失败时抛出异常。

    Example:
        >>> messages = [
        ...     {"role": "system", "content": "你是助手"},
        ...     {"role": "user", "content": "写个快排"}
        ... ]
        >>> result = llm(messages, model_apikey="xxxxxx")
        >>> print(result)
    """
    if model_apikey is None:
        raise ValueError("model_apikey不能为空，请传入Token。")

    import time

    for attempt in range(max_retries):
        try:
            # 创建客户端时设置超时
            client = OpenAI(api_key=model_apikey, base_url=model_url, timeout=timeout)

            log.info(f"正在调用LLM API (尝试 {attempt + 1}/{max_retries})...")
            # 构建请求参数
            request_params = {
                "model": model_name,
                "messages": messages,
                "stream": False,
                "temperature": temperature,
                "max_tokens": max_output_tokens,
                "response_format": {"type": "json_object"},
                "seed": 42,
            }

            # 尝试添加enable_thinking参数
            try:
                request_params["enable_thinking"] = False
                log.info("enable_thinking=False")
                response = client.chat.completions.create(**request_params)
            except TypeError:
                # 如果enable_thinking参数不被支持，移除它并重试
                request_params.pop("enable_thinking", None)
                request_params["extra_body"] = {"enable_thinking": False}
                log.info("extra_body={'enable_thinking': False}")
                response = client.chat.completions.create(**request_params)

            # 只取第一个choice的message内容
            log.info("LLM API调用成功")
            return response.choices[0].message.content.strip()

        except Exception as e:
            error_msg = str(e)
            log.error(
                f"LLM API调用失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}"
            )

            # 如果是最后一次尝试，抛出异常
            if attempt == max_retries - 1:
                raise Exception(f"LLM API调用失败 (已重试{max_retries}次): {error_msg}")

            # 检查是否是超时或网关错误，如果是则等待后重试
            if any(
                keyword in error_msg.lower()
                for keyword in ["timeout", "504", "502", "503", "gateway"]
            ):
                # wait_time = (attempt + 1) * 10  # 递增等待时间：10s, 20s, 30s
                wait_time = 1  # 等待时间：1s
                log.info(f"检测到网络错误，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 其他错误直接抛出
                raise Exception(f"LLM API调用失败: {error_msg}")


class DocumentAnalyzer:
    def __init__(
        self,
        es_client,
        es_index_links: str,
        es_index_analysis: str,
        model_apikey: str,
        model_name: str,
        model_url: str,
        prompt_spec: str,
        timeout: int = 300,
        max_retries: int = 2,  # 新增参数model_url和model_name，用于指定模型名称和URL。
    ):
        """
        初始化文档分析器

        Args:
            es_client: Elasticsearch客户端实例
            es_index_links: 源数据索引名
            es_index_analysis: 分析结果索引名
            model_apikey: API Token
        """
        self.es = es_client
        self.es_index_links = es_index_links
        self.es_index_analysis = es_index_analysis
        self.model_apikey = model_apikey
        self.model_name = model_name
        self.model_url = model_url
        self.timeout = timeout
        self.max_retries = max_retries

        # 初始化黑名单管理器
        self.blacklist_manager = BlacklistManager()

        # 打印黑名单统计信息
        stats = self.blacklist_manager.get_blacklist_stats()
        if stats["total_count"] > 0:
            log.info(f"黑名单中有 {stats['total_count']} 个文档将被跳过")
            if stats["today_count"] > 0:
                log.info(f"今日新增黑名单文档: {stats['today_count']} 个")

        # 使用传入的prompt_spec，如果为空则使用默认值
        self.prompt_spec = (
            prompt_spec
            if prompt_spec
            else """
        你是招投标行业的专家，请根据下列要求提取招标人及相关信息：
        - 使用中文回答。
        - 以 JSON 格式返回关键信息，可直接用 Python 的 json.loads 解析。
        - 金额单位统一转换为人民币元。
        - 下面字段类型是Elasticsearch数据库的数据类型。
        - **如果公告涉及多个物品（如object_name、object_brand、object_model、object_supplier、object_price等字段），请将每个物品单独作为一个对象输出，最终返回一个对象列表（list of dict），每个对象包含该物品的所有相关字段。不要将多个物品的信息合并为一个字符串。**
        - **list of dict的最外层必须是list，内部必须是dict，内部不要是list的类型。一定不要像bad case那样输出**
        - 其他非物品相关字段（如项目名称、招标人等）需要放在每个对象中。
        - 严格根据以下字典里字段的类型和描述要求提取公告中的键值对，未提取到的值赋值null，不确定的赋值null，只输出确定的结果，不要有多余注释和重复字段：
        {
            'bid_name': '标段名称,标段有时也叫包组或者包,字段类型是text',
            'bid_number': '标段编号,标段有时也叫包组或者包,字段类型是keyword',
            'bid_budget': '标段预算金额,标段有时也叫包组或者包,字段类型是double',
            'fiscal_delegation_number': '财政委托编号,字段类型是keyword',
            'prj_addr': '招标项目地址,字段类型是text',
            'prj_name': '招标项目名称,字段类型是text',
            'prj_number': '招标项目编号,字段类型是keyword',
            'prj_type': '招标项目类型(工程,货物,服务),字段类型是keyword',
            'release_time': '发布日期,字段类型是date,字段格式是yyyy-MM-dd HH:mm:ss',
            'prj_approval_authority': '项目审批单位,字段类型是text',
            'superintendent_office': '监督部门,字段类型是text',
            'superintendent_office_code': '监督部门编号,字段类型是keyword',
            'tenderee': '招标人,字段类型是text',
            'bid_submission_deadline': '投标截止时间,字段类型是date,字段格式是yyyy-MM-dd HH:mm:ss',
            'trade_platform': '交易平台,字段类型是text',
            'procurement_method': '采购方式,字段类型是text',
            'prj_sub_type': '项目细分类型(设备,维保,耗材,试剂,手术器械,其他),字段类型是keyword',
            'province': '省份,必须是XX省,字段类型是keyword',
            'city': '城市,city必须是XX市,字段类型是keyword',
            'county': '区县,必须是XX区或者XX县,但不能是市辖区,字段类型是keyword',
            'announcement_type': '公告类型(001,002,003,004,010,999),001代表招标公告,002代表候选人公示,003代表更正/澄清公告,004代表结果公告,010代表中标通知书,999代表其他,字段类型是keyword',
            'object_name': '标的物名称,字段类型是text',
            'object_brand': '标的物品牌,字段类型是keyword',
            'object_model': '标的物型号,字段类型是keyword',
            'object_supplier': '标的物供应商,字段类型是text',
            'object_produce_area': '标的物产地,字段类型是text',
            "object_conf": "标的物配置参数,它是招标需求、采购需求、项目需求里的采购需求、技术要求、技术规格、技术参数、参数要求、配置要求、规格型号、货物明细、规格参数等内容,字段类型是text",
            'object_oem': '标的物OEM厂家,字段类型是text',
            'object_amount': '标的物数量,字段类型是integer',
            'object_unit': '标的物单位,字段类型是keyword',
            'object_price': '标的物单价,只有一个单价,不存在多个单价,字段类型是double',
            'object_total_price': '标的物总价,只有一个总价,不存在多个总价,如果对象存在标的物单价(object_price)和标的物数量(object_amount)且不为""、0和null的情况下,标的物总价(object_total_price)等于标的物单价(object_price)乘以标的物数量(object_amount),,字段类型是double',
            'object_maintenance_period': '标的物维保期限,字段类型是keyword',
            'object_price_source': '标的物价格来源,字段类型是keyword',
            'object_quality': '标的物质量层次(1,2),1代表国产,2代表进口,字段类型是keyword',
            'bidder_price': '中标金额,字段类型是double',
            'bidder_name': '中标单位名称,字段类型是text',
            'bidder_contact_person': '中标单位联系人,字段类型是text',
            'bidder_contact_phone_number': '中标单位联系人电话,字段类型是keyword',
            'bidder_contract_config_param': '中标合同配置参数,它是中标合同或者服务协议里的报价表和技术应答,字段类型是text',
            'agent': '代理机构,字段类型是text',
            'service_fee': '代理服务收费金额,字段类型是double',
            'bid_cancelled_flag': '标段是否废标标记,废标填1,否则填null,标段有时也叫包组或者包,字段类型是keyword',
            'bid_cancelled_reason': '标段废标原因,标段有时也叫包组或者包,字段类型是text',
        }
        - **输出示例：**
        [
            {
                "bid_name": null,
                "bid_number": "JXMT【2025】G123",
                "bid_budget": null,
                "fiscal_delegation_number": null,
                "prj_addr": "江西省南昌市红谷滩区碟子湖大道1399号",
                "prj_name": "南昌市洪都中医院采购骨科手术床等设备一批",
                "prj_number": "JXMT【2025】G123",
                "prj_type": "货物",
                "release_time": "2025-06-26 16:14:00",
                "prj_approval_authority": null,
                "superintendent_office": null,
                "superintendent_office_code": null,
                "tenderee": "南昌市洪都中医院",
                "bid_submission_deadline": null,
                "trade_platform": "江西省公共资源交易平台",
                "procurement_method": null,
                "prj_sub_type": "设备",
                "province": "江西省",
                "city": "南昌市",
                "county": "红谷滩区",
                "announcement_type": "004",
                "object_name": "气压止血带",
                "object_brand": "亿凡",
                "object_model": "YF-ATS-D",
                "object_supplier": "上海捷仰医疗科技发展有限公司",
                "object_produce_area": null,
                "object_conf": null,
                "object_oem": null,
                "object_amount": 5,
                "object_unit": "个",
                "object_price": 7000.0,
                "object_total_price": 35000.0,
                "object_maintenance_period": null,
                "object_price_source": null,
                "object_quality": null,
                "bidder_price": 985800.0,
                "bidder_name": "上海捷仰医疗科技发展有限公司",
                "bidder_contact_person": "胡波平",
                "bidder_contact_phone_number": "13681737817",
                "bidder_contract_config_param": null,
                "agent": "江西明台项目咨询管理有限公司",
                "service_fee": 14787.0,
                "bid_cancelled_flag": null,
                "bid_cancelled_reason": null
            },
            {
                "bid_name": "六安市裕安区独山镇中心卫生院（苏维埃红色医院）医技综合楼能力提升设备采购项目第二包",
                "bid_number": "FS34150320250102号-2",
                "bid_budget": null,
                "fiscal_delegation_number": null,
                "prj_addr": "六安市裕安区独山镇环城路43号",
                "prj_name": "六安市裕安区独山镇中心卫生院（苏维埃红色医院）医技综合楼能力提升设备采购项目",
                "prj_number": "FS34150320250102号",
                "prj_type": "货物",
                "release_time": "2025-06-26 11:05:00",
                "prj_approval_authority": null,
                "superintendent_office": null,
                "superintendent_office_code": null,
                "tenderee": "六安市裕安区独山镇中心卫生院",
                "bid_submission_deadline": null,
                "trade_platform": "中国政府采购网",
                "procurement_method": null,
                "prj_sub_type": "设备",
                "province": "安徽省",
                "city": "六安市",
                "county": "裕安区",
                "announcement_type": "004",
                "object_name": "CT",
                "object_brand": "东软",
                "object_model": "NeuViz CT",
                "object_supplier": "合肥亿行医药有限公司",
                "object_produce_area": null,
                "object_conf": null,
                "object_oem": null,
                "object_amount": 1,
                "object_unit": "台",
                "object_price": 3886000.00,
                "object_total_price": 3886000.00,
                "object_maintenance_period": null,
                "object_price_source": null,
                "object_quality": null,
                "bidder_price": 3886000.00,
                "bidder_name": "合肥亿行医药有限公司",
                "bidder_contact_person": null,
                "bidder_contact_phone_number": null,
                "bidder_contract_config_param": null,
                "agent": "安徽省六投项目管理有限公司",
                "service_fee": 46700.00,
                "bid_cancelled_flag": null,
                "bid_cancelled_reason": null
            }
        ]
        - **bad case：**
        [
            [
                2025
            ],
            [
                {
                "agent": "甘肃全标项目管理有限公司",
                "city": "酒泉市",
                "county": "瓜州县",
                "bid_submission_deadline": "2025-07-09 09:00",
                "province": "甘肃省",
                "object_name": "双能X射线骨密度仪",
                "object_quality": "2",
                "prj_name": "瓜州县人民医院双能X射线骨密度仪和肺功能仪采购项目",
                "announcement_type": "001",
                "release_time": "2025-06-18 20:40:00",
                "prj_number": "GZZFCG[2025]84号",
                "object_unit": "台",
                "bid_number": "GZZFCG[2025]84号",
                "bid_budget": 1400000.0,
                "prj_type": "货物",
                "object_conf": "需采购双能X射线骨密度仪1台（进口）",
                "trade_platform": "酒泉市公共资源交易网瓜州分中心网站",
                "tenderee": "瓜州县人民医院",
                "prj_sub_type": "设备",
                "prj_addr": "瓜州县渊泉镇文化街",
                "object_amount": 1
                },
                {
                "agent": " 甘肃全标项目管理有限公司",
                "city": "酒泉市",
                "county": "瓜州县",
                "bid_submission_deadline": "2025-07-09 09:00",
                "province": "甘肃省",
                "object_name": "肺功能仪",
                "prj_name": "瓜州县人民医院双能X射线骨密度仪和肺功能仪采购项目",
                "announcement_type": "001",
                "release_time": "2025-06-18 20:40:00",
                "prj_number": "GZZFCG[2025]84号",
                "object_unit": "台",
                "bid_number": "GZZFCG[2025]84号",
                "bid_budget": 1400000.0,
                "prj_type": "货物",
                "object_conf": "需采购肺功能仪1台",
                "trade_platform": "酒泉市公共资源交易网瓜州分中心网站",
                "tenderee": "瓜州县人民医院",
                "prj_sub_type": "设备",
                "prj_addr": "瓜州县渊泉镇文化街",
                "object_amount": 1
                }
            ]
        ]
        """
        )

    def build_query(
        self,
        categories: List[str],
        exclude_ids: List[str] = None,
    ) -> Dict:
        """
        构建ES查询

        Args:
            categories: 分类列表
            exclude_ids: 需要排除的ID列表

        Returns:
            Dict: ES查询语句
        """
        must_conditions = [
            {"terms": {"category": categories}},
            # 使用context7方式检查appendix字段
            {
                "bool": {
                    "must_not": [
                        {
                            "nested": {
                                "path": "appendix",
                                "query": {
                                    "bool": {
                                        "must": [
                                            {"exists": {"field": "appendix.text"}},
                                            {"exists": {"field": "appendix.url"}},
                                        ]
                                    }
                                },
                            }
                        }
                    ]
                }
            },
        ]

        if exclude_ids:
            must_conditions.append(
                {"bool": {"must_not": [{"ids": {"values": exclude_ids}}]}}
            )

        return {
            "query": {"bool": {"must": must_conditions}},
            "size": 1,
            "sort": [{"_id": {"order": "asc"}}],
        }

    def get_processed_ids(self) -> List[str]:
        """
        获取已处理的文档ID列表

        Returns:
            List[str]: 已处理的文档ID列表
        """
        processed_ids = []

        # 初始化scroll查询，指定需要返回source_id字段
        query = {
            "query": {"match_all": {}},
            "_source": ["source_id"],  # 只返回source_id字段
            "size": 10000,
        }

        # 执行初始搜索
        result = search_documents(self.es, self.es_index_analysis, query=query)
        if not result:
            return processed_ids

        # 获取第一批结果
        hits = result.get("hits", {}).get("hits", [])
        # 从source中获取source_id
        processed_ids.extend([hit["_source"]["source_id"] for hit in hits])

        # 获取scroll_id
        scroll_id = result.get("_scroll_id")
        if not scroll_id:
            return processed_ids

        # 使用scroll API获取剩余结果
        while True:
            scroll_result = self.es.scroll(
                scroll_id=scroll_id, scroll="5m"  # 保持scroll上下文5分钟
            )

            hits = scroll_result.get("hits", {}).get("hits", [])
            if not hits:
                break

            # 从source中获取source_id
            processed_ids.extend([hit["_source"]["source_id"] for hit in hits])

        # 清理scroll上下文
        self.es.clear_scroll(scroll_id=scroll_id)

        # 去重
        processed_ids = list(set(processed_ids))

        return processed_ids

    def analyze_document(self, doc: Dict) -> List[Dict]:
        """
        分析单个文档

        Args:
            doc: ES文档

        Returns:
            List[Dict]: 分析结果列表
        """
        title = doc["_source"]["title"]
        html_content = doc["_source"]["response"]
        content = md(html_content)

        # 检查内容长度，避免过长内容导致超时
        max_content_length = 50000  # 最大50K字符
        if len(content) > max_content_length:
            log.warning(
                f"文档内容过长({len(content)}字符)，截取前{max_content_length}字符"
            )
            content = content[:max_content_length] + "\n...(内容已截取)"

        prompt = f"""
        标题：{title}
        内容：
        {content}
        {self.prompt_spec}
        """

        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": "严格按照系统提示词输出结果，不要胡编乱造"},
        ]

        result = llm(
            messages,
            model_name=self.model_name,
            model_apikey=self.model_apikey,
            model_url=self.model_url,
            timeout=self.timeout,
            max_retries=self.max_retries,
        )
        log.info(result)

        # 获取多个JSON字符串
        json_strings = clean_json_markdown(result)
        analyzed_results = []

        for json_str in json_strings:
            try:
                # 处理每个JSON字符串
                llm_result = process_json_data(json_str)
                meta = {
                    "source_id": doc["_id"],
                    "source_title": doc["_source"].get("title"),
                    "source_create_time": doc["_source"].get("create_time"),
                    "source_category": doc["_source"].get("category"),
                    "source_url": doc["_source"].get("url"),
                    "source_appendix": doc["_source"].get("appendix"),
                    "insert_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
                if isinstance(llm_result, list):
                    for item in llm_result:
                        if isinstance(item, dict):
                            item.update(meta)
                            analyzed_results.append(item)
                else:
                    llm_result.update(meta)
                    analyzed_results.append(llm_result)

            except json.JSONDecodeError as e:
                log.error(f"JSON解析失败: {e}")
                log.error(f"原始JSON字符串: {json_str}")
                continue

        return analyzed_results

    def process_one_record(
        self,
        categories: List[str] = ["001", "002", "003", "004", "010", "999"],
    ):
        """
        处理一条记录

        Args:
            categories: 分类列表
        """
        try:
            # 获取已处理的ID列表
            processed_ids = self.get_processed_ids()

            # 获取黑名单ID列表
            blacklisted_ids = self.blacklist_manager.get_blacklisted_ids()

            # 合并排除列表
            exclude_ids = list(set(processed_ids + blacklisted_ids))

            if blacklisted_ids:
                log.info(f"排除 {len(blacklisted_ids)} 个黑名单文档")

            # 构建查询，排除已处理的ID和黑名单ID
            query = self.build_query(categories, exclude_ids=exclude_ids)
            result = search_documents(self.es, self.es_index_links, query=query)

            if result and result.get("hits", {}).get("hits"):
                hits = result["hits"]["hits"]
                if hits:
                    doc = hits[0]  # 只处理第一条记录

                    if doc["_source"]["appendix"]:
                        log.info(f"ID: {doc['_id']}附件链接不为空")
                        log.info(f"公告标题: {doc['_source']['title']}")
                        log.info(f"公告链接: {doc['_source']['url']}")
                        log.info(f"公告类型: {doc['_source']['category']}")
                        log.info(f"附件链接: {doc['_source']['appendix']}")
                    else:
                        log.info(f"ID: {doc['_id']}的附件链接为空")
                        log.info(f"公告标题: {doc['_source']['title']}")
                        log.info(f"公告链接: {doc['_source']['url']}")
                        log.info(f"公告类型: {doc['_source']['category']}")
                        log.info(f"附件链接: {doc['_source']['appendix']}")

                        # 获取多个分析结果
                        try:
                            analyzed_docs = self.analyze_document(doc)
                        except Exception as e:
                            log.error(f"文档解析失败: {e}")

                            # 检查是否是LLM调用失败（已重试2次）
                            error_msg = str(e)
                            if (
                                "LLM API调用失败" in error_msg
                                and "已重试2次" in error_msg
                            ):
                                log.warning(
                                    f"LLM调用失败达到最大重试次数，将文档 {doc['_id']} 添加到黑名单"
                                )

                                # 添加到黑名单
                                doc_title = doc.get("_source", {}).get(
                                    "title", "无标题"
                                )
                                doc_url = doc.get("_source", {}).get("url", "")

                                success = self.blacklist_manager.add_to_blacklist(
                                    document_id=doc["_id"],
                                    document_title=doc_title,
                                    document_url=doc_url,
                                    failure_reason=f"LLM API调用失败: {error_msg[:200]}...",
                                )

                                if success:
                                    log.info(
                                        f"✓ 文档 {doc['_id']} 已添加到黑名单，下次运行将跳过"
                                    )
                                else:
                                    log.error(f"✗ 添加文档 {doc['_id']} 到黑名单失败")

                            log.warning(f"跳过文档解析: {doc['_id']}")
                            # 跳过这个文档的解析，但不中断整个流程
                            return

                        # 为每个分析结果生成唯一的ID并插入
                        for i, analyzed_doc in enumerate(analyzed_docs):
                            # 生成新的文档ID
                            new_doc_id = f"{analyzed_doc['source_id']}_{i}"

                            # 获取公告类型用于字段校验
                            announcement_type = analyzed_doc["source_category"]

                            # 字段校验和标准化
                            validated_document = validate_and_normalize_fields(
                                analyzed_doc, announcement_type=announcement_type
                            )

                            # 插入文档
                            insert_document(
                                self.es,
                                self.es_index_analysis,
                                doc_id=new_doc_id,
                                document=validated_document,
                            )
                            log.info(f"成功插入文档 {new_doc_id}")
                else:
                    log.info("未找到未处理的数据")
            else:
                log.info("未找到未处理的数据")
        except Exception as e:
            log.error(f"处理过程中发生异常: {e}")
            log.warning("文档处理失败，但不影响后续处理")
            # 记录错误但不重新抛出，避免中断整个流程


def get_one_record():
    try:
        # 初始化ES客户端
        es = init_es_client()

        # 加载.env文件
        load_dotenv()

        # 从环境变量获取配置
        es_index_links = os.getenv("ES_INDEX_LINKS")
        es_index_analysis = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
        model_apikey = os.getenv("MODEL_APIKEY")
        model_name = os.getenv("MODEL_NAME")
        model_url = os.getenv("MODEL_URL")
        prompt_spec = os.getenv(
            "PROMPT_SPEC", ""
        )  # 从环境变量获取prompt_spec，如果没有则使用空字符串

        # 创建分析器实例
        analyzer = DocumentAnalyzer(
            es_client=es,
            es_index_links=es_index_links,
            es_index_analysis=es_index_analysis,
            model_apikey=model_apikey,
            model_name=model_name,  # 新增参数model_url和model_name，用于指定模型名称和URL。
            model_url=model_url,  # 新增参数model_url和model_name，用于指定模型名称和URL。
            prompt_spec=prompt_spec,  # 添加缺少的prompt_spec参数
        )

        # 处理一条记录
        analyzer.process_one_record()

    except Exception as e:
        raise Exception(f"处理失败: {e}")


def manage_blacklist():
    """黑名单管理功能"""
    import argparse
    import sys

    # 处理以-开头的文档ID的特殊情况
    # 如果命令行参数中有以-开头但不是已知选项的参数，将其视为文档ID
    processed_args = []
    i = 0
    while i < len(sys.argv):
        arg = sys.argv[i]
        if arg.startswith("-") and not arg.startswith("--") and len(arg) > 2:
            # 检查是否是文档ID（不是已知的短选项）
            if i > 0 and sys.argv[i - 1] == "--id":
                # 这是--id参数的值，保持原样
                processed_args.append(arg)
            elif arg not in ["-h"]:
                # 可能是文档ID，需要特殊处理
                if i > 0 and sys.argv[i - 1] == "--id":
                    processed_args.append(arg)
                else:
                    processed_args.append(arg)
            else:
                processed_args.append(arg)
        else:
            processed_args.append(arg)
        i += 1

    parser = argparse.ArgumentParser(description="黑名单管理工具")
    parser.add_argument(
        "--action",
        choices=["list", "stats", "clear", "remove", "add"],
        required=True,
        help="操作类型",
    )
    parser.add_argument(
        "--id",
        metavar="DOCUMENT_ID",
        help='文档ID（用于add和remove操作）。对于以-开头的ID，请使用：--id="-1o4r5cBsUtJ06NfkR31" 格式',
    )
    parser.add_argument("--title", help="文档标题（用于add操作，可选）")
    parser.add_argument("--url", help="文档URL（用于add操作，可选）")
    parser.add_argument("--reason", help="失败原因（用于add操作，可选）")
    parser.add_argument(
        "--limit", type=int, default=20, help="显示数量限制（仅用于list操作）"
    )

    try:
        args = parser.parse_args()
    except SystemExit as e:
        if e.code != 0:
            print("\n提示：如果文档ID以-开头，请使用以下格式之一：")
            print('  --id="-1o4r5cBsUtJ06NfkR31"')
            print('  或在PowerShell中使用：--id `"-1o4r5cBsUtJ06NfkR31`"')
        raise

    blacklist_manager = BlacklistManager()

    if args.action == "stats":
        print_blacklist_stats(blacklist_manager)

    elif args.action == "list":
        blacklist = blacklist_manager.get_blacklist(limit=args.limit)
        if blacklist:
            print(f"\n黑名单文档列表 (显示前{len(blacklist)}条):")
            print("=" * 100)
            for i, item in enumerate(blacklist, 1):
                print(f"{i}. ID: {item['document_id']}")
                print(f"   标题: {item['document_title'] or '无标题'}")
                print(f"   失败次数: {item['failure_count']}")
                print(f"   最后失败时间: {item['last_failure_time']}")
                print(f"   失败原因: {item['failure_reason'][:100]}...")
                print("-" * 100)
        else:
            print("黑名单为空")

    elif args.action == "add":
        if not args.id:
            print("错误：add操作需要指定--id参数")
            return

        # 检查文档是否已在黑名单中
        if blacklist_manager.is_blacklisted(args.id):
            print(f"警告：文档 {args.id} 已在黑名单中")
            confirm = input("是否要更新该文档的信息？(y/N): ")
            if confirm.lower() != "y":
                print("操作已取消")
                return

        # 添加到黑名单
        success = blacklist_manager.add_to_blacklist(
            document_id=args.id,
            document_title=args.title or "手动添加",
            document_url=args.url or "",
            failure_reason=args.reason or "手动添加到黑名单",
        )

        if success:
            print(f"✓ 已将文档 {args.id} 添加到黑名单")
            if args.title:
                print(f"  标题: {args.title}")
            if args.url:
                print(f"  URL: {args.url}")
            if args.reason:
                print(f"  原因: {args.reason}")
        else:
            print(f"✗ 添加文档 {args.id} 到黑名单失败")

    elif args.action == "remove":
        if not args.id:
            print("错误：remove操作需要指定--id参数")
            return

        if blacklist_manager.remove_from_blacklist(args.id):
            print(f"✓ 已从黑名单中移除文档: {args.id}")
        else:
            print(f"✗ 移除失败，文档可能不在黑名单中: {args.id}")

    elif args.action == "clear":
        confirm = input("确认要清空整个黑名单吗？(y/N): ")
        if confirm.lower() == "y":
            if blacklist_manager.clear_blacklist():
                print("✓ 黑名单已清空")
            else:
                print("✗ 清空黑名单失败")
        else:
            print("操作已取消")


if __name__ == "__main__":
    import sys

    # 检查是否是黑名单管理模式
    if len(sys.argv) > 1 and sys.argv[1] == "blacklist":
        # 移除第一个参数，让argparse正确解析
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        manage_blacklist()
    else:
        get_one_record()
