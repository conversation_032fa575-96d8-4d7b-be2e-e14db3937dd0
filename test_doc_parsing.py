#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试.doc文件解析修复
"""

import os
import sys
import tempfile
import requests
from io import BytesIO

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入解析函数
try:
    from analyse_appendix import parse_doc, convert_to_markdown_with_markitdown
    print("✓ 成功导入解析函数")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_doc_parsing_methods():
    """测试不同的.doc文件解析方法"""
    print("=" * 80)
    print("测试.doc文件解析方法")
    print("=" * 80)
    
    # 创建一个简单的测试.doc文件内容（模拟）
    # 注意：这不是真正的.doc文件，只是用于测试回退机制
    test_content = b"This is a test document content for .doc file parsing."
    
    print("\n1. 测试parse_doc函数")
    try:
        result = parse_doc(test_content)
        if result:
            print(f"✓ parse_doc成功，提取内容长度: {len(result)}")
            print(f"  内容预览: {result[:100]}...")
        else:
            print("⚠ parse_doc返回空内容，但没有异常")
        return True
    except Exception as e:
        print(f"✗ parse_doc失败: {e}")
        return False


def test_convert_to_markdown_with_markitdown():
    """测试convert_to_markdown_with_markitdown函数的回退机制"""
    print("\n2. 测试convert_to_markdown_with_markitdown回退机制")
    
    # 测试.doc文件的回退
    test_content = b"This is a test document content for .doc file parsing."
    
    try:
        result = convert_to_markdown_with_markitdown(test_content, ".doc")
        if result:
            print(f"✓ convert_to_markdown_with_markitdown成功，内容长度: {len(result)}")
            print(f"  内容预览: {result[:100]}...")
        else:
            print("⚠ convert_to_markdown_with_markitdown返回空内容")
        return True
    except Exception as e:
        print(f"✗ convert_to_markdown_with_markitdown失败: {e}")
        return False


def test_available_libraries():
    """测试可用的解析库"""
    print("\n3. 测试可用的解析库")
    
    libraries = [
        ("docx2txt", "docx2txt"),
        ("markitdown", "markitdown"),
        ("antiword", "subprocess")  # 通过subprocess测试antiword
    ]
    
    available_count = 0
    
    for lib_name, import_name in libraries:
        try:
            if lib_name == "antiword":
                import subprocess
                result = subprocess.run(["antiword", "--version"], 
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"✓ {lib_name}: 可用")
                    available_count += 1
                else:
                    print(f"✗ {lib_name}: 不可用（命令未找到）")
            else:
                __import__(import_name)
                print(f"✓ {lib_name}: 可用")
                available_count += 1
        except ImportError:
            print(f"✗ {lib_name}: 不可用（未安装）")
        except subprocess.TimeoutExpired:
            print(f"✗ {lib_name}: 不可用（超时）")
        except FileNotFoundError:
            print(f"✗ {lib_name}: 不可用（命令未找到）")
        except Exception as e:
            print(f"✗ {lib_name}: 不可用（{e}）")
    
    print(f"\n可用库数量: {available_count}/{len(libraries)}")
    return available_count > 0


def test_file_type_support():
    """测试文件类型支持"""
    print("\n4. 测试文件类型支持")
    
    file_types = [".pdf", ".docx", ".doc", ".txt", ".unknown"]
    test_content = b"Test content"
    
    for file_type in file_types:
        try:
            result = convert_to_markdown_with_markitdown(test_content, file_type)
            if result:
                print(f"✓ {file_type}: 支持（内容长度: {len(result)}）")
            else:
                print(f"⚠ {file_type}: 支持但返回空内容")
        except Exception as e:
            print(f"✗ {file_type}: 不支持（{e}）")


def create_simple_doc_content():
    """创建一个简单的.doc文件内容用于测试"""
    # 这是一个非常简化的.doc文件头部，用于测试
    # 实际的.doc文件格式非常复杂
    doc_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'  # OLE文件头
    text_content = b'Test Document Content\x00\x00This is a test document for parsing.\x00\x00'
    return doc_header + text_content


def main():
    """运行所有测试"""
    print("测试.doc文件解析修复")
    print("=" * 80)
    
    test_results = []
    
    try:
        # 测试解析方法
        test_results.append(test_doc_parsing_methods())
        
        # 测试markitdown回退
        test_results.append(test_convert_to_markdown_with_markitdown())
        
        # 测试可用库
        test_results.append(test_available_libraries())
        
        # 测试文件类型支持
        test_file_type_support()
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "parse_doc函数测试",
            "convert_to_markdown_with_markitdown回退测试",
            "可用库检测"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！.doc文件解析修复成功！")
        else:
            print("\n⚠ 部分测试失败，但基本功能应该可用")
            
        # 显示建议
        print("\n" + "=" * 80)
        print("建议:")
        print("=" * 80)
        print("1. 如果需要更好的.doc文件支持，可以安装:")
        print("   pip install docx2txt")
        print("2. 在Linux/Mac系统上，可以安装antiword:")
        print("   sudo apt-get install antiword  # Ubuntu/Debian")
        print("   brew install antiword          # macOS")
        print("3. 当前的回退机制会尝试基本的文本提取")
        print("4. markitdown失败时会自动回退到传统解析方法")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
