#!/usr/bin/env python3
"""
删除markersweb_attachment_analysis_alias索引中的旧文档

删除条件：
1. insert_time在2025-07-04 12:00:00之前
2. source_appendix不等于[]（即有附件的文档）

使用方法：
python delete_old_documents.py [--dry-run] [--batch-size 1000]
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import List, Dict, Any
import argparse
from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan, bulk
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("delete_old_documents.log"), logging.StreamHandler()],
)
log = logging.getLogger(__name__)


class DocumentDeleter:
    def __init__(self, env_file=None):
        """初始化ES客户端"""
        # 加载指定的环境变量文件
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()
        self.es_host = os.getenv("ES_HOST", "localhost:9200")
        self.es_user = os.getenv("ES_USER", "")
        self.es_password = os.getenv("ES_PASSWORD", "")
        self.index_name = os.getenv(
            "ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias"
        )

        # 创建ES客户端
        if self.es_user and self.es_password:
            self.es = Elasticsearch(
                [self.es_host],
                basic_auth=(self.es_user, self.es_password),
                verify_certs=False,
            )
        else:
            self.es = Elasticsearch([self.es_host])

        log.info(f"连接到Elasticsearch: {self.es_host}")
        log.info(f"目标索引: {self.index_name}")

        # 验证连接
        try:
            info = self.es.info()
            log.info(
                f"ES集群信息: {info['cluster_name']} - {info['version']['number']}"
            )
        except Exception as e:
            log.error(f"无法连接到Elasticsearch: {e}")
            raise

    def build_query(self, cutoff_time: str) -> Dict[str, Any]:
        """
        构建查询条件

        Args:
            cutoff_time: 截止时间，格式：2025-07-04 12:00:00

        Returns:
            Elasticsearch查询DSL
        """
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"range": {"insert_time": {"lt": cutoff_time}}},
                        {
                            "bool": {
                                "must_not": [
                                    {"term": {"source_appendix.keyword": "[]"}}
                                ]
                            }
                        },
                    ],
                    "filter": [{"exists": {"field": "source_appendix"}}],
                }
            }
        }

        log.info(f"查询条件: {json.dumps(query, indent=2, ensure_ascii=False)}")
        return query

    def count_documents(self, query: Dict[str, Any]) -> int:
        """
        统计符合条件的文档数量

        Args:
            query: 查询条件

        Returns:
            文档数量
        """
        try:
            result = self.es.count(index=self.index_name, body=query)
            count = result["count"]
            log.info(f"找到符合删除条件的文档数量: {count}")
            return count
        except Exception as e:
            log.error(f"统计文档数量失败: {e}")
            raise

    def get_documents_to_delete(self, query: Dict[str, Any]) -> List[str]:
        """
        获取需要删除的文档ID列表

        Args:
            query: 查询条件

        Returns:
            文档ID列表
        """
        doc_ids = []

        try:
            # 使用scan获取所有匹配的文档ID
            for doc in scan(
                self.es,
                query=query,
                index=self.index_name,
                _source=False,  # 只获取ID，不获取文档内容
                scroll="5m",
                size=1000,
            ):
                doc_ids.append(doc["_id"])

                # 每1000个文档打印一次进度
                if len(doc_ids) % 1000 == 0:
                    log.info(f"已扫描 {len(doc_ids)} 个文档...")

            log.info(f"总共找到 {len(doc_ids)} 个需要删除的文档")
            return doc_ids

        except Exception as e:
            log.error(f"获取文档ID列表失败: {e}")
            raise

    def delete_documents_batch(
        self, doc_ids: List[str], batch_size: int = 1000
    ) -> Dict[str, int]:
        """
        批量删除文档

        Args:
            doc_ids: 文档ID列表
            batch_size: 批量大小

        Returns:
            删除结果统计
        """
        total_docs = len(doc_ids)
        deleted_count = 0
        failed_count = 0

        log.info(f"开始批量删除 {total_docs} 个文档，批量大小: {batch_size}")

        # 分批删除
        for i in range(0, total_docs, batch_size):
            batch_ids = doc_ids[i : i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (total_docs + batch_size - 1) // batch_size

            log.info(
                f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_ids)} 个文档"
            )

            # 构建批量删除操作
            actions = []
            for doc_id in batch_ids:
                actions.append(
                    {"_op_type": "delete", "_index": self.index_name, "_id": doc_id}
                )

            try:
                # 执行批量删除
                success_count, failed_items = bulk(
                    self.es,
                    actions,
                    index=self.index_name,
                    timeout="30s",
                    max_retries=3,
                )

                deleted_count += success_count
                failed_count += len(failed_items)

                log.info(
                    f"第 {batch_num} 批删除完成: 成功 {success_count}, 失败 {len(failed_items)}"
                )

                if failed_items:
                    log.warning(
                        f"第 {batch_num} 批中有 {len(failed_items)} 个文档删除失败"
                    )
                    for item in failed_items[:5]:  # 只显示前5个失败项
                        log.warning(f"删除失败: {item}")

            except Exception as e:
                log.error(f"第 {batch_num} 批删除失败: {e}")
                failed_count += len(batch_ids)

        result = {"total": total_docs, "deleted": deleted_count, "failed": failed_count}

        log.info(f"批量删除完成: {result}")
        return result

    def dry_run(self, cutoff_time: str) -> None:
        """
        干运行模式，只查看会删除哪些文档，不实际删除

        Args:
            cutoff_time: 截止时间
        """
        log.info("=" * 60)
        log.info("DRY RUN 模式 - 不会实际删除文档")
        log.info("=" * 60)

        query = self.build_query(cutoff_time)
        count = self.count_documents(query)

        if count == 0:
            log.info("没有找到符合删除条件的文档")
            return

        log.info(f"在DRY RUN模式下，将会删除 {count} 个文档")

        # 获取前10个文档的详细信息作为示例
        query_with_source = query.copy()
        query_with_source["size"] = 10
        query_with_source["_source"] = ["insert_time", "source_appendix", "title"]

        try:
            result = self.es.search(index=self.index_name, body=query_with_source)

            log.info("示例文档（前10个）:")
            for i, doc in enumerate(result["hits"]["hits"], 1):
                source = doc["_source"]
                log.info(f"  {i}. ID: {doc['_id']}")
                log.info(f"     标题: {source.get('title', 'N/A')}")
                log.info(f"     插入时间: {source.get('insert_time', 'N/A')}")
                log.info(f"     附件数量: {len(source.get('source_appendix', []))}")
                log.info("")

        except Exception as e:
            log.error(f"获取示例文档失败: {e}")

    def execute_deletion(self, cutoff_time: str, batch_size: int = 1000) -> None:
        """
        执行实际删除操作

        Args:
            cutoff_time: 截止时间
            batch_size: 批量大小
        """
        log.info("=" * 60)
        log.info("执行实际删除操作")
        log.info("=" * 60)

        query = self.build_query(cutoff_time)
        count = self.count_documents(query)

        if count == 0:
            log.info("没有找到符合删除条件的文档")
            return

        # 确认删除
        log.warning(f"即将删除 {count} 个文档，此操作不可逆！")

        # 获取文档ID列表
        doc_ids = self.get_documents_to_delete(query)

        if not doc_ids:
            log.info("没有获取到需要删除的文档ID")
            return

        # 执行批量删除
        result = self.delete_documents_batch(doc_ids, batch_size)

        log.info("=" * 60)
        log.info("删除操作完成")
        log.info(f"总文档数: {result['total']}")
        log.info(f"成功删除: {result['deleted']}")
        log.info(f"删除失败: {result['failed']}")
        log.info("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="删除markersweb_attachment_analysis_alias索引中的旧文档"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="干运行模式，只查看会删除哪些文档，不实际删除",
    )
    parser.add_argument(
        "--batch-size", type=int, default=1000, help="批量删除的大小（默认1000）"
    )
    parser.add_argument(
        "--cutoff-time",
        type=str,
        default="2025-07-04 12:00:00",
        help="截止时间，删除此时间之前的文档（默认：2025-07-04 12:00:00）",
    )
    parser.add_argument(
        "--env-file", type=str, help="指定环境变量文件路径（默认使用.env）"
    )

    args = parser.parse_args()

    try:
        deleter = DocumentDeleter(env_file=args.env_file)

        if args.dry_run:
            deleter.dry_run(args.cutoff_time)
        else:
            deleter.execute_deletion(args.cutoff_time, args.batch_size)

    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
