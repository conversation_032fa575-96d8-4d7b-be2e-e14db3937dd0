#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最新的JSON修复功能
"""

import json
from analyse_noappendix import clean_json_data

def test_latest_json_issues():
    """
    测试最新发现的JSON问题
    """
    print("=" * 80)
    print("测试最新JSON问题修复")
    print("=" * 80)
    
    # 模拟最新遇到的问题JSON
    problematic_json = '''[
    {
        "bid_name": null,
        "object_name": "集成平台系统",
        "object_brand": "嘉和美康",
        "object_price": 1587800.0,
        "service_fee": 137798.0
    },
    {
        "bid_name": "",
        "object_name": "数据中心系统",
        "object_brand": "嘉和美康",
        " object_model": "嘉和医院数据中心系统V5.0",
        "object_price": 1865100.０,
        "object_total_price": 1865100,
        "bidder_contact_person": "王冉",
        "bidder_contact_person": "王冉",
        "service_fee": 1３7798.０
    },
    {
        "bid_name": "",
        "prj_addr": "延吉市龙东街63314011401140114011401号",
        "
        
    
    
    }
    
    ]'''
    
    print("问题JSON包含:")
    print("1. 字段名前有空格: \" object_model\"")
    print("2. 数值中的全角数字: 1865100.０ 和 1３7798.０")
    print("3. 重复字段: bidder_contact_person 出现两次")
    print("4. JSON截断: 最后一个对象包含不完整的字段和换行符")
    print()
    
    # 测试修复
    try:
        print("开始修复...")
        cleaned_json = clean_json_data(problematic_json)
        
        print("✅ JSON清理完成")
        
        # 尝试解析
        parsed_data = json.loads(cleaned_json)
        print("✅ 修复后的JSON解析成功")
        
        print(f"\n修复结果:")
        print(f"  解析出 {len(parsed_data)} 个对象")
        
        # 检查具体修复效果
        for i, obj in enumerate(parsed_data, 1):
            print(f"\n  对象 {i}:")
            
            # 检查字段名修复
            if "object_model" in obj:
                print(f"    ✅ object_model: {obj['object_model']} (字段名空格已修复)")
            
            # 检查全角数字修复
            if "object_price" in obj:
                price = obj["object_price"]
                if isinstance(price, (int, float)):
                    print(f"    ✅ object_price: {price} (全角数字已修复)")
                else:
                    print(f"    ⚠️  object_price: {price}")
            
            if "service_fee" in obj:
                fee = obj["service_fee"]
                if isinstance(fee, (int, float)):
                    print(f"    ✅ service_fee: {fee} (全角数字已修复)")
                else:
                    print(f"    ⚠️  service_fee: {fee}")
            
            # 检查重复字段处理
            if "bidder_contact_person" in obj:
                print(f"    ✅ bidder_contact_person: {obj['bidder_contact_person']} (重复字段已处理)")
        
        print(f"\n✅ 所有问题都已修复！")
        
        # 显示修复后的JSON结构
        print(f"\n修复后的JSON结构:")
        print(json.dumps(parsed_data, ensure_ascii=False, indent=2)[:500] + "...")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()

def test_specific_new_issues():
    """
    测试特定的新问题
    """
    print("\n" + "=" * 80)
    print("测试特定新问题修复")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "字段名前有空格",
            "json": '{"normal": "value", " spaced_field": "test"}',
            "check": lambda data: "spaced_field" in data and " spaced_field" not in data
        },
        {
            "name": "数值中的全角数字",
            "json": '{"price": 1２３.０, "amount": 4５６}',
            "check": lambda data: data.get("price") == 123.0 and data.get("amount") == 456
        },
        {
            "name": "不完整的JSON截断",
            "json": '{"name": "test", "value": "incomplete\n\n\n}',
            "check": lambda data: "name" in data
        },
        {
            "name": "复合问题",
            "json": '{"price": 1２３.０, " field": "value", "duplicate": "a", "duplicate": "b"}',
            "check": lambda data: data.get("price") == 123.0 and "field" in data
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        print(f"原始: {case['json']}")
        
        try:
            # 测试修复
            cleaned = clean_json_data(case['json'])
            parsed = json.loads(cleaned)
            
            # 检查修复效果
            if case['check'](parsed):
                print(f"  ✅ 修复成功: {json.dumps(parsed, ensure_ascii=False)}")
            else:
                print(f"  ⚠️  修复部分成功: {json.dumps(parsed, ensure_ascii=False)}")
            
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")

def test_edge_cases_new():
    """
    测试新的边界情况
    """
    print("\n" + "=" * 80)
    print("测试新的边界情况")
    print("=" * 80)
    
    edge_cases = [
        ("字段名全是空格", '{"   ": "value"}'),
        ("多个全角数字", '{"num": 1２３４５.６７８９０}'),
        ("截断在字段名中间", '{"name": "value", "incom'),
        ("多层嵌套全角数字", '{"data": {"price": 1２３.０, "count": ４５６}}'),
    ]
    
    for name, test_json in edge_cases:
        print(f"\n测试: {name}")
        print(f"原始: {test_json}")
        
        try:
            result = clean_json_data(test_json)
            parsed = json.loads(result)
            print(f"  ✅ 处理成功: {json.dumps(parsed, ensure_ascii=False)}")
            
        except Exception as e:
            print(f"  ⚠️  处理失败但没有崩溃: {e}")

def main():
    """
    主函数
    """
    test_latest_json_issues()
    test_specific_new_issues()
    test_edge_cases_new()
    
    print("\n" + "=" * 80)
    print("最新JSON修复测试完成")
    print("现在定时任务应该能够处理更多复杂的JSON格式问题")
    print("=" * 80)

if __name__ == "__main__":
    main()
