#!/usr/bin/env python3
"""
清理文件字段脚本

该脚本的功能：
1. 对bid_doc_name字段有数据的文档进行筛选，如果文件名称不包含指定关键词，则清空相关字段
2. 对contract_name字段有数据的文档进行筛选，如果文件名称不包含指定关键词，则清空相关字段

清理规则：
- 招标文件：如果bid_doc_name不包含"招标文件"、"磋商文件"、"谈判文件"，则清空bid_doc_*字段
- 合同文件：如果contract_name不包含"合同"、"服务协议"，则清空contract_*字段
"""

import argparse
import sys
from typing import List, Dict, Optional
from dotenv import load_dotenv
import os

from es_deal import init_es_client, search_documents
from utils.log_cfg import log


# 招标文件关键词
BID_DOC_KEYWORDS = ["招标文件", "磋商文件", "谈判文件"]

# 合同文件关键词
CONTRACT_KEYWORDS = ["合同", "服务协议"]


def contains_keywords(text: str, keywords: List[str]) -> bool:
    """
    检查文本是否包含指定关键词中的任一个

    Args:
        text (str): 要检查的文本
        keywords (List[str]): 关键词列表

    Returns:
        bool: 如果包含任一关键词则返回True，否则返回False
    """
    if not text:
        return False

    text_lower = text.lower()
    for keyword in keywords:
        if keyword.lower() in text_lower:
            log.debug(f"在文本中找到关键词: {keyword}")
            return True

    return False


def get_documents_with_file_fields(
    es_client, index_name: str, batch_size: int = 100
) -> List[Dict]:
    """
    获取包含文件字段的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小

    Returns:
        包含文件字段的文档列表
    """
    try:
        # 查询包含bid_doc_name或contract_name字段的文档
        query = {
            "query": {
                "bool": {
                    "should": [
                        {"exists": {"field": "bid_doc_name"}},
                        {"exists": {"field": "contract_name"}},
                    ],
                    "minimum_should_match": 1,
                }
            },
            "size": batch_size,
            "_source": [
                "bid_doc_name",
                "bid_doc_ext",
                "bid_doc_link_out",
                "bid_doc_link_key",
                "contract_name",
                "contract_ext",
                "contract_link_out",
                "contract_link_key",
            ],
        }

        result = search_documents(es_client, index_name, query=query)

        if result and result.get("hits", {}).get("hits"):
            documents = []
            for hit in result["hits"]["hits"]:
                source = hit["_source"]
                doc_info = {
                    "doc_id": hit["_id"],
                    "bid_doc_name": source.get("bid_doc_name"),
                    "bid_doc_ext": source.get("bid_doc_ext"),
                    "bid_doc_link_out": source.get("bid_doc_link_out"),
                    "bid_doc_link_key": source.get("bid_doc_link_key"),
                    "contract_name": source.get("contract_name"),
                    "contract_ext": source.get("contract_ext"),
                    "contract_link_out": source.get("contract_link_out"),
                    "contract_link_key": source.get("contract_link_key"),
                }
                documents.append(doc_info)
            return documents
        else:
            return []

    except Exception as e:
        log.error(f"查询包含文件字段的文档失败: {e}")
        return []


def get_total_documents_count(es_client, index_name: str) -> int:
    """
    获取包含文件字段的文档总数

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称

    Returns:
        文档总数
    """
    try:
        query = {
            "query": {
                "bool": {
                    "should": [
                        {"exists": {"field": "bid_doc_name"}},
                        {"exists": {"field": "contract_name"}},
                    ],
                    "minimum_should_match": 1,
                }
            }
        }

        result = es_client.count(index=index_name, body=query)
        return result.get("count", 0)

    except Exception as e:
        log.error(f"获取文档总数失败: {e}")
        return 0


def bulk_update_documents(es_client, index_name: str, updates: List[Dict]):
    """
    批量更新文档字段

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        updates: 更新操作列表
    """
    if not updates:
        return {"success": 0, "failed": 0}

    try:
        # 构建批量更新请求
        body = []
        for update in updates:
            # 更新操作头
            body.append({"update": {"_index": index_name, "_id": update["doc_id"]}})
            # 更新内容
            body.append({"doc": update["fields"]})

        # 执行批量更新
        response = es_client.bulk(body=body)

        # 检查结果
        success_count = 0
        error_count = 0

        for item in response["items"]:
            if "update" in item:
                if item["update"].get("status") in [200, 201]:
                    success_count += 1
                else:
                    error_count += 1
                    log.error(f"更新失败: {item['update']}")

        log.info(f"批量更新完成 - 成功: {success_count}, 失败: {error_count}")
        return {"success": success_count, "failed": error_count}

    except Exception as e:
        log.error(f"批量更新失败: {e}")
        return {"success": 0, "failed": len(updates)}


def process_documents_cleanup(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理文档字段清理

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info(f"开始处理文件字段清理...")
        log.info(f"索引: {index_name}")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 先获取总数统计
        total_docs = get_total_documents_count(es_client, index_name)
        log.info(f"索引中包含文件字段的文档总数: {total_docs}")

        if total_docs == 0:
            log.info("没有找到包含文件字段的文档")
            return

        # 获取需要检查的文档
        documents = get_documents_with_file_fields(es_client, index_name, batch_size)

        if not documents:
            log.info("没有找到需要检查的文档")
            return

        log.info(f"本批次检查 {len(documents)} 个文档（总共 {total_docs} 个）")

        # 分析需要清理的文档
        updates = []
        bid_doc_cleaned = 0
        contract_cleaned = 0
        bid_doc_kept = 0
        contract_kept = 0

        for i, doc in enumerate(documents, 1):
            doc_id = doc["doc_id"]
            update_fields = {}

            # 检查招标文件字段
            bid_doc_name = doc["bid_doc_name"]
            if bid_doc_name:  # 如果bid_doc_name有数据
                if not contains_keywords(bid_doc_name, BID_DOC_KEYWORDS):
                    # 不包含关键词，清空相关字段
                    update_fields.update(
                        {
                            "bid_doc_name": None,
                            "bid_doc_ext": None,
                            "bid_doc_link_out": None,
                            "bid_doc_link_key": None,
                        }
                    )
                    bid_doc_cleaned += 1
                    log.info(
                        f"[{i}/{len(documents)}] 清理招标文件字段 {doc_id}: '{bid_doc_name}' (不包含关键词)"
                    )
                else:
                    bid_doc_kept += 1
                    log.debug(
                        f"[{i}/{len(documents)}] 保留招标文件字段 {doc_id}: '{bid_doc_name}' (包含关键词)"
                    )

            # 检查合同文件字段
            contract_name = doc["contract_name"]
            if contract_name:  # 如果contract_name有数据
                if not contains_keywords(contract_name, CONTRACT_KEYWORDS):
                    # 不包含关键词，清空相关字段
                    update_fields.update(
                        {
                            "contract_name": None,
                            "contract_ext": None,
                            "contract_link_out": None,
                            "contract_link_key": None,
                        }
                    )
                    contract_cleaned += 1
                    log.info(
                        f"[{i}/{len(documents)}] 清理合同文件字段 {doc_id}: '{contract_name}' (不包含关键词)"
                    )
                else:
                    contract_kept += 1
                    log.debug(
                        f"[{i}/{len(documents)}] 保留合同文件字段 {doc_id}: '{contract_name}' (包含关键词)"
                    )

            # 如果有字段需要更新，添加到更新列表
            if update_fields:
                updates.append({"doc_id": doc_id, "fields": update_fields})

        # 输出统计信息
        log.info("=" * 50)
        log.info("处理统计:")
        log.info(f"  本批次文档总数: {len(documents)}")
        log.info(f"  需要更新的文档: {len(updates)}")
        log.info(f"  招标文件字段:")
        log.info(f"    清理的文档: {bid_doc_cleaned}")
        log.info(f"    保留的文档: {bid_doc_kept}")
        log.info(f"  合同文件字段:")
        log.info(f"    清理的文档: {contract_cleaned}")
        log.info(f"    保留的文档: {contract_kept}")
        log.info("=" * 50)

        if updates and not dry_run:
            # 执行批量更新
            result = bulk_update_documents(es_client, index_name, updates)
            log.info(f"清理完成 - 成功: {result['success']}, 失败: {result['failed']}")
        elif dry_run:
            log.info("试运行模式，未执行实际更新")
        else:
            log.info("没有需要清理的文档")

    except Exception as e:
        log.error(f"处理过程中发生错误: {e}")


def process_all_documents_cleanup(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理所有需要清理的文档（分批处理）

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info("开始处理所有需要清理的文档...")

        # 获取总数
        total_docs = get_total_documents_count(es_client, index_name)
        log.info(f"索引中包含文件字段的文档总数: {total_docs}")

        if total_docs == 0:
            log.info("没有找到需要检查的文档")
            return

        processed_count = 0
        batch_num = 1
        total_cleaned = 0
        total_kept = 0

        # 使用scroll API处理大量数据
        query = {
            "query": {
                "bool": {
                    "should": [
                        {"exists": {"field": "bid_doc_name"}},
                        {"exists": {"field": "contract_name"}},
                    ],
                    "minimum_should_match": 1,
                }
            },
            "_source": [
                "bid_doc_name",
                "bid_doc_ext",
                "bid_doc_link_out",
                "bid_doc_link_key",
                "contract_name",
                "contract_ext",
                "contract_link_out",
                "contract_link_key",
            ],
        }

        # 初始化scroll
        scroll_response = es_client.search(
            index=index_name, body=query, scroll="5m", size=batch_size
        )

        scroll_id = scroll_response.get("_scroll_id")
        hits = scroll_response["hits"]["hits"]

        while hits:
            log.info(f"\n{'='*60}")
            log.info(f"开始处理第 {batch_num} 批次")
            log.info(f"已处理: {processed_count}/{total_docs}")
            log.info(f"{'='*60}")

            # 处理当前批次
            documents = []
            for hit in hits:
                source = hit["_source"]
                doc_info = {
                    "doc_id": hit["_id"],
                    "bid_doc_name": source.get("bid_doc_name"),
                    "bid_doc_ext": source.get("bid_doc_ext"),
                    "bid_doc_link_out": source.get("bid_doc_link_out"),
                    "bid_doc_link_key": source.get("bid_doc_link_key"),
                    "contract_name": source.get("contract_name"),
                    "contract_ext": source.get("contract_ext"),
                    "contract_link_out": source.get("contract_link_out"),
                    "contract_link_key": source.get("contract_link_key"),
                }
                documents.append(doc_info)

            # 分析和清理当前批次
            batch_stats = process_batch_documents(
                es_client, index_name, documents, dry_run
            )

            # 累计统计
            total_cleaned += batch_stats["cleaned"]
            total_kept += batch_stats["kept"]

            processed_count += len(hits)
            batch_num += 1

            # 获取下一批数据
            scroll_response = es_client.scroll(scroll_id=scroll_id, scroll="5m")
            hits = scroll_response["hits"]["hits"]

        # 清理scroll上下文
        if scroll_id:
            es_client.clear_scroll(scroll_id=scroll_id)

        # 输出最终统计
        log.info(f"\n{'='*60}")
        log.info("最终统计:")
        log.info(f"  处理文档总数: {processed_count}")
        log.info(f"  清理的字段: {total_cleaned}")
        log.info(f"  保留的字段: {total_kept}")
        log.info(f"  共处理 {batch_num - 1} 个批次")
        log.info(f"{'='*60}")

    except Exception as e:
        log.error(f"处理所有文档时发生错误: {e}")


def process_batch_documents(
    es_client, index_name: str, documents: List[Dict], dry_run: bool = False
) -> Dict:
    """
    处理一个批次的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        documents: 文档列表
        dry_run: 是否为试运行模式

    Returns:
        Dict: 处理统计信息
    """
    updates = []
    cleaned_count = 0
    kept_count = 0

    for doc in documents:
        doc_id = doc["doc_id"]
        update_fields = {}

        # 检查招标文件字段
        bid_doc_name = doc["bid_doc_name"]
        if bid_doc_name:
            if not contains_keywords(bid_doc_name, BID_DOC_KEYWORDS):
                update_fields.update(
                    {
                        "bid_doc_name": None,
                        "bid_doc_ext": None,
                        "bid_doc_link_out": None,
                        "bid_doc_link_key": None,
                    }
                )
                cleaned_count += 1
            else:
                kept_count += 1

        # 检查合同文件字段
        contract_name = doc["contract_name"]
        if contract_name:
            if not contains_keywords(contract_name, CONTRACT_KEYWORDS):
                update_fields.update(
                    {
                        "contract_name": None,
                        "contract_ext": None,
                        "contract_link_out": None,
                        "contract_link_key": None,
                    }
                )
                cleaned_count += 1
            else:
                kept_count += 1

        # 如果有字段需要更新，添加到更新列表
        if update_fields:
            updates.append({"doc_id": doc_id, "fields": update_fields})

    # 执行更新
    update_result = {"success": 0, "failed": 0}
    if updates and not dry_run:
        update_result = bulk_update_documents(es_client, index_name, updates)
    elif dry_run and updates:
        log.info(f"试运行模式: 将清理 {len(updates)} 个文档")

    return {
        "cleaned": cleaned_count,
        "kept": kept_count,
        "updated": update_result["success"],
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="清理文件字段：根据文件名称关键词清理不符合条件的招标文件和合同文件字段"
    )
    parser.add_argument(
        "--index",
        type=str,
        help="目标索引名称（默认从环境变量获取）",
    )
    parser.add_argument(
        "--batch-size", type=int, default=100, help="批处理大小（默认100）"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="试运行模式，不执行实际更新"
    )
    parser.add_argument(
        "--all", action="store_true", help="处理所有需要清理的文档（分批处理）"
    )

    args = parser.parse_args()

    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()

        # 确定索引名称
        if args.index:
            index_name = args.index
        else:
            index_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
            if not index_name:
                log.error("未指定索引名称，且环境变量ES_INDEX_ANALYSIS_ALIAS未设置")
                log.error(
                    "请使用 --index 参数指定索引名称，或在.env文件中设置ES_INDEX_ANALYSIS_ALIAS"
                )
                sys.exit(1)

        log.info(f"目标索引: {index_name}")
        log.info(f"批处理大小: {args.batch_size}")
        log.info(f"试运行模式: {args.dry_run}")
        log.info(f"处理所有文档: {args.all}")

        # 输出清理规则
        log.info("=" * 60)
        log.info("清理规则:")
        log.info(f"  招标文件关键词: {BID_DOC_KEYWORDS}")
        log.info(f"  合同文件关键词: {CONTRACT_KEYWORDS}")
        log.info("  如果文件名不包含对应关键词，将清空相关字段")
        log.info("=" * 60)

        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            sys.exit(1)

        # 处理文档清理
        if args.all:
            process_all_documents_cleanup(es, index_name, args.batch_size, args.dry_run)
        else:
            process_documents_cleanup(es, index_name, args.batch_size, args.dry_run)

    except KeyboardInterrupt:
        log.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
