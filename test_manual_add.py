#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试手动添加文档到黑名单的功能
"""

import os
import sys
import subprocess
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from blacklist_manager import BlacklistManager


def test_manual_add_functionality():
    """测试手动添加功能"""
    print("=" * 80)
    print("测试手动添加文档到黑名单功能")
    print("=" * 80)
    
    # 使用测试数据库
    manager = BlacklistManager("test_manual_add.db")
    manager.clear_blacklist()
    
    print("\n1. 测试基本的手动添加功能")
    
    # 测试数据
    test_cases = [
        {
            "id": "manual_test_001",
            "title": "手动添加测试文档1",
            "url": "http://example.com/manual1",
            "reason": "测试手动添加功能"
        },
        {
            "id": "manual_test_002", 
            "title": "手动添加测试文档2",
            "url": "http://example.com/manual2",
            "reason": "已知有问题的文档"
        },
        {
            "id": "manual_test_003",
            "title": None,  # 测试可选参数
            "url": None,
            "reason": None
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n  测试用例 {i}: {case['id']}")
        
        success = manager.add_to_blacklist(
            document_id=case["id"],
            document_title=case["title"],
            document_url=case["url"],
            failure_reason=case["reason"]
        )
        
        if success:
            print(f"    ✓ 成功添加文档: {case['id']}")
        else:
            print(f"    ✗ 添加失败: {case['id']}")
        
        # 验证是否真的添加了
        if manager.is_blacklisted(case["id"]):
            print(f"    ✓ 验证成功：文档在黑名单中")
        else:
            print(f"    ✗ 验证失败：文档不在黑名单中")
    
    print("\n2. 测试重复添加处理")
    
    # 尝试重复添加
    success = manager.add_to_blacklist(
        document_id="manual_test_001",
        document_title="更新后的标题",
        document_url="http://example.com/updated",
        failure_reason="更新测试"
    )
    
    if success:
        print("    ✓ 重复添加成功（应该更新失败次数）")
    else:
        print("    ✗ 重复添加失败")
    
    print("\n3. 查看添加结果")
    
    blacklist = manager.get_blacklist()
    print(f"    黑名单中共有 {len(blacklist)} 个文档:")
    
    for item in blacklist:
        print(f"      - ID: {item['document_id']}")
        print(f"        标题: {item['document_title']}")
        print(f"        URL: {item['document_url']}")
        print(f"        原因: {item['failure_reason']}")
        print(f"        失败次数: {item['failure_count']}")
        print()
    
    # 清理测试数据
    manager.clear_blacklist()
    try:
        os.remove("test_manual_add.db")
        print("✓ 测试数据清理完成")
    except:
        print("✗ 测试数据清理失败")
    
    return True


def test_command_line_interface():
    """测试命令行接口"""
    print("\n" + "=" * 80)
    print("测试命令行手动添加功能")
    print("=" * 80)
    
    # 清理可能存在的测试数据
    if os.path.exists("blacklist.db"):
        manager = BlacklistManager("blacklist.db")
        # 移除测试文档（如果存在）
        test_ids = ["cli_test_001", "cli_test_002", "cli_test_003"]
        for test_id in test_ids:
            manager.remove_from_blacklist(test_id)
    
    print("\n1. 测试完整参数的手动添加")
    
    cmd = [
        "python", "analyse_appendix.py", "blacklist", "--action", "add",
        "--id", "cli_test_001",
        "--title", "命令行测试文档1",
        "--url", "http://example.com/cli1",
        "--reason", "命令行测试"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(f"    命令执行结果: {result.returncode}")
        print(f"    输出: {result.stdout.strip()}")
        if result.stderr:
            print(f"    错误: {result.stderr.strip()}")
        
        if result.returncode == 0 and "✓" in result.stdout:
            print("    ✓ 完整参数添加测试通过")
        else:
            print("    ✗ 完整参数添加测试失败")
    except Exception as e:
        print(f"    ✗ 命令执行失败: {e}")
    
    print("\n2. 测试最小参数的手动添加")
    
    cmd = [
        "python", "analyse_appendix.py", "blacklist", "--action", "add",
        "--id", "cli_test_002"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(f"    命令执行结果: {result.returncode}")
        print(f"    输出: {result.stdout.strip()}")
        if result.stderr:
            print(f"    错误: {result.stderr.strip()}")
        
        if result.returncode == 0 and "✓" in result.stdout:
            print("    ✓ 最小参数添加测试通过")
        else:
            print("    ✗ 最小参数添加测试失败")
    except Exception as e:
        print(f"    ✗ 命令执行失败: {e}")
    
    print("\n3. 测试缺少必需参数的情况")
    
    cmd = [
        "python", "analyse_appendix.py", "blacklist", "--action", "add"
        # 故意不提供 --id 参数
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(f"    命令执行结果: {result.returncode}")
        print(f"    输出: {result.stdout.strip()}")
        if result.stderr:
            print(f"    错误: {result.stderr.strip()}")
        
        if "错误：add操作需要指定--id参数" in result.stdout:
            print("    ✓ 参数验证测试通过")
        else:
            print("    ✗ 参数验证测试失败")
    except Exception as e:
        print(f"    ✗ 命令执行失败: {e}")
    
    print("\n4. 验证添加的文档")
    
    cmd = [
        "python", "analyse_appendix.py", "blacklist", "--action", "list", "--limit", "5"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(f"    命令执行结果: {result.returncode}")
        print(f"    输出:")
        print("    " + "\n    ".join(result.stdout.strip().split("\n")))
        
        if "cli_test_001" in result.stdout or "cli_test_002" in result.stdout:
            print("    ✓ 文档列表验证通过")
        else:
            print("    ✗ 文档列表验证失败")
    except Exception as e:
        print(f"    ✗ 命令执行失败: {e}")
    
    # 清理测试数据
    print("\n5. 清理测试数据")
    
    if os.path.exists("blacklist.db"):
        manager = BlacklistManager("blacklist.db")
        test_ids = ["cli_test_001", "cli_test_002", "cli_test_003"]
        for test_id in test_ids:
            if manager.remove_from_blacklist(test_id):
                print(f"    ✓ 清理测试文档: {test_id}")
    
    return True


def main():
    """运行所有测试"""
    print("手动添加黑名单功能测试")
    print("=" * 80)
    
    test_results = []
    
    try:
        test_results.append(test_manual_add_functionality())
        test_results.append(test_command_line_interface())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "手动添加基本功能",
            "命令行接口测试"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！手动添加功能正常！")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
