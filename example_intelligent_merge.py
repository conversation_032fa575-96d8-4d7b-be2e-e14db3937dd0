#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能融合功能使用示例
演示如何对主体解析结果的空缺字段进行检索和融合
"""

import json
from analyse_appendix import (
    identify_missing_fields,
    intelligent_merge_analysis,
    DocumentAnalyzer
)
from es_deal import init_es_client
from dotenv import load_dotenv
import os


def example_basic_usage():
    """基础使用示例：不使用LLM的智能融合"""
    print("=" * 60)
    print("示例1: 基础智能融合（不使用LLM）")
    print("=" * 60)
    
    # 模拟主体解析结果：包含多个标的物，每个都有一些空缺字段
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "prj_number": "YY2025001",
            "tenderee": None,  # 空缺，需要从招标文件补充
            "object_name": "CT设备",
            "object_brand": None,  # 空缺，需要从招标文件补充
            "object_model": None,  # 空缺，需要从招标文件补充
            "bidder_name": None,  # 空缺，需要从合同文件补充
            "bidder_price": None,  # 空缺，需要从合同文件补充
            "agent": None,  # 空缺，需要补充
        },
        {
            "prj_name": "医院设备采购项目",
            "prj_number": "YY2025001",
            "tenderee": None,  # 空缺
            "object_name": "MRI设备",
            "object_brand": None,  # 空缺
            "object_model": None,  # 空缺
            "bidder_name": None,  # 空缺
            "bidder_price": None,  # 空缺
            "agent": None,  # 空缺
        }
    ]
    
    # 模拟招标文件解析结果
    tender_results = [
        {
            "tenderee": "某市人民医院",
            "object_brand": "西门子",
            "object_model": "SOMATOM CT",
            "agent": "某招标代理公司",
        }
    ]
    
    # 模拟合同文件解析结果
    contract_results = [
        {
            "bidder_name": "医疗设备有限公司",
            "bidder_price": 2500000.0,
            "bidder_contact_person": "张经理",
            "bidder_contact_phone_number": "13800138000",
        }
    ]
    
    print("主体解析结果（融合前）:")
    for i, result in enumerate(main_results):
        print(f"标的物 {i+1}: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    print("\n招标文件解析结果:")
    print(json.dumps(tender_results[0], ensure_ascii=False, indent=2))
    
    print("\n合同文件解析结果:")
    print(json.dumps(contract_results[0], ensure_ascii=False, indent=2))
    
    # 执行智能融合
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
    )
    
    print("\n智能融合后的结果:")
    for i, result in enumerate(merged_results):
        print(f"融合结果 {i+1}:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        print("-" * 40)
    
    # 验证融合效果
    print("\n融合效果验证:")
    for i, result in enumerate(merged_results):
        missing_before = identify_missing_fields(main_results[i])
        missing_after = identify_missing_fields(result)
        
        print(f"标的物 {i+1}:")
        print(f"  融合前空缺字段数量: {len(missing_before)}")
        print(f"  融合后空缺字段数量: {len(missing_after)}")
        print(f"  减少空缺字段: {len(missing_before) - len(missing_after)}个")
        
        # 检查关键字段是否已补充
        key_fields = ["tenderee", "object_brand", "bidder_name", "bidder_price", "agent"]
        filled_fields = [f for f in key_fields if result.get(f) is not None]
        print(f"  关键字段补充情况: {filled_fields}")


def example_with_document_content():
    """使用文档内容的智能融合示例（需要LLM）"""
    print("\n" + "=" * 60)
    print("示例2: 使用文档内容的智能融合（需要LLM配置）")
    print("=" * 60)
    
    # 模拟主体解析结果
    main_results = [
        {
            "prj_name": "医疗设备采购",
            "object_name": "超声设备",
            "tenderee": None,  # 需要从文档内容中提取
            "object_brand": None,  # 需要从文档内容中提取
            "bidder_name": None,  # 需要从文档内容中提取
        }
    ]
    
    # 模拟招标文件内容
    tender_content = """
    招标公告
    
    项目名称：医疗设备采购项目
    招标人：北京市第一人民医院
    
    采购清单：
    1. 超声设备 1台
       品牌：飞利浦
       型号：EPIQ 7
       技术要求：...
    
    代理机构：北京招标有限公司
    """
    
    # 模拟合同文件内容
    contract_content = """
    采购合同
    
    甲方：北京市第一人民医院
    乙方：医疗科技有限公司
    
    中标信息：
    中标单位：医疗科技有限公司
    中标金额：1,500,000元
    联系人：李经理
    联系电话：010-12345678
    
    设备信息：
    超声设备 飞利浦 EPIQ 7 1台
    """
    
    print("主体解析结果:")
    print(json.dumps(main_results[0], ensure_ascii=False, indent=2))
    
    print(f"\n招标文件内容片段:")
    print(tender_content[:200] + "...")
    
    print(f"\n合同文件内容片段:")
    print(contract_content[:200] + "...")
    
    # 注意：这里需要实际的LLM配置才能运行
    print("\n注意：要使用文档内容提取功能，需要配置以下参数：")
    print("- model_apikey: LLM API密钥")
    print("- model_name: 模型名称")
    print("- model_url: 模型API地址")
    print("\n如果配置了这些参数，可以调用：")
    print("""
merged_results = intelligent_merge_analysis(
    main_list=main_results,
    tender_content=tender_content,
    contract_content=contract_content,
    model_apikey="your_api_key",
    model_name="your_model_name", 
    model_url="your_model_url"
)
    """)


def example_document_analyzer_usage():
    """DocumentAnalyzer类的使用示例"""
    print("\n" + "=" * 60)
    print("示例3: DocumentAnalyzer类的智能融合使用")
    print("=" * 60)
    
    print("DocumentAnalyzer类现在支持智能融合功能：")
    print("""
# 创建分析器实例
analyzer = DocumentAnalyzer(
    es_client=es_client,
    es_index_links="your_links_index",
    es_index_analysis="your_analysis_index", 
    model_apikey="your_api_key",
    model_name="your_model_name",
    model_url="your_model_url",
    prompt_spec="your_prompt"
)

# 使用智能融合处理记录（默认启用）
analyzer.process_one_record(use_intelligent_merge=True)

# 或使用传统融合方式
analyzer.process_one_record(use_intelligent_merge=False)
    """)
    
    print("\n智能融合的优势：")
    print("1. 自动识别主体解析结果中的空缺字段")
    print("2. 根据字段类型优先从相应文档中检索（合同字段优先从合同文件检索）")
    print("3. 使用LLM从文档内容中智能提取字段值")
    print("4. 支持跨文档检索（如果优先文档中找不到，会从另一个文档中检索）")
    print("5. 保持向后兼容，可以选择使用传统融合方式")


def main():
    """运行所有示例"""
    print("智能融合功能使用示例")
    print("=" * 60)
    
    try:
        example_basic_usage()
        example_with_document_content()
        example_document_analyzer_usage()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n示例运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
