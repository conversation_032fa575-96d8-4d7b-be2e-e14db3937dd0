#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试JSON清理功能
"""

import json
from analyse_appendix import clean_json_data

# 测试有问题的JSON字符串（从错误日志中提取）
test_json = """[
    {
        "bid_name": null,
        "bid_number": "2025(JKJ)143",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐市米东区府前中路1055号",
        "prj_name": "乌鲁木齐市米东区中医医院血透室医疗设备采购",
        "prj_number": "2025(JKJ)143",
        "prj_type": "货物",
        "release_time": "2025-06-20 19:09:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "乌鲁木齐市米东区中医医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": "米东区",
        "announcement_type": "004",
        "object_name": "自动腹膜透析机",
        "object_brand": "江苏杰瑞",
        "object_model": "JARI-APD-1A",
        "object_supplier": "新疆生荣医学科技有限公司",
        "object_produce_area": "新疆乌鲁木齐市",
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 71000.0,
        "object_total_price": 427500.0,
        "object_maintenance_period": null,
        "object_price_source": "报价",
        "object_quality": null,
        "bidder_price": 427510.0,
        "bidder_name": "新疆生荣医学科技有限公司",
        "bidder_contact_person": "陈晨、岑小龙",
        "bidder_contact_phone_number": "13201097691、16699887301",
        "bidder_contract_config_param": null,
        "agent": "新疆君凯杰工程项目管理有限公司",
        "service_fee": 6412.5,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null

    },
    {
        "bid_name": null ,
        "bid_number": "2025(JKJ)143",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐市米东区府前中路1055号",
        "prj_name": "乌鲁木齐市米东区中医医院血透室医疗设备采购",
        "prj_number": "2025(JKJ)143",
        "prj_type": "货物",
        "release_time": "2025-06-20 19:09:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "乌鲁木齐市米东区中医医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": "米东区",
        "announcement_type": "004",
        "object_name": "电热恒温培养箱",
        "object_brand": "上海精宏",
        "object_model": "DNP-9082",
        "object_supplier": "新疆生荣医学科技有限公司",
        "object_produce_area": "新疆乌鲁木齐市",
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 4500.0,
        "object_total_price": 4500.0,
        "object_maintenance_period": null,
        "object_price_source": "报价",
        "object_quality": null,
        "bidder_price": 427510.0,
            "bidder_name": "新疆生荣医学科技有限公司",
        "bidder_contact_person": "陈晨、岑小龙",
        "bidder_contact_phone_number": "13201097691、16699887301",
        "bidder_contract_config_param": null,
        "agent": "新疆君凯杰工程项目管理有限公司",
        "service_fee": 6412.5,
        "bid_cancelled_flag":   null，
                                                                                                                                                                                                                                                                      　
    }
]"""


def test_json_cleaning():
    print("原始JSON:")
    print(test_json)
    print("\n" + "=" * 80 + "\n")

    try:
        # 测试清理功能
        cleaned = clean_json_data(test_json)
        print("清理后的JSON:")
        print(cleaned)
        print("\n" + "=" * 80 + "\n")

        # 尝试解析清理后的JSON
        data = json.loads(cleaned)
        print("解析成功！")
        print(f"解析结果类型: {type(data)}")
        print(f"数组长度: {len(data) if isinstance(data, list) else 'N/A'}")

        if isinstance(data, list) and len(data) > 0:
            print(
                f"第一个对象的字段数: {len(data[0]) if isinstance(data[0], dict) else 'N/A'}"
            )
            if len(data) > 1:
                print(
                    f"第二个对象的字段数: {len(data[1]) if isinstance(data[1], dict) else 'N/A'}"
                )
                if isinstance(data[1], dict):
                    print(
                        f"第二个对象的object_name: {data[1].get('object_name', 'N/A')}"
                    )

    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_json_cleaning()
