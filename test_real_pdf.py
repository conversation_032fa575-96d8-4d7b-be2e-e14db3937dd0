#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实PDF文件的解析
"""

import os
import requests
from analyse_appendix import parse_pdf, extract_preview_text, PDF_PARSER

def test_with_real_pdf():
    """
    使用真实的PDF文件进行测试
    """
    print("=" * 60)
    print("测试真实PDF文件解析")
    print("=" * 60)
    print(f"当前PDF解析器: {PDF_PARSER}")
    
    # 检查downloads目录中是否有PDF文件
    downloads_dir = "downloads"
    pdf_files = []
    
    if os.path.exists(downloads_dir):
        for file in os.listdir(downloads_dir):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(downloads_dir, file))
    
    if pdf_files:
        print(f"\n找到 {len(pdf_files)} 个PDF文件:")
        for i, pdf_file in enumerate(pdf_files[:3]):  # 只测试前3个
            print(f"  {i+1}. {os.path.basename(pdf_file)}")
        
        # 测试第一个PDF文件
        test_file = pdf_files[0]
        print(f"\n测试文件: {os.path.basename(test_file)}")
        
        try:
            # 读取PDF文件
            with open(test_file, 'rb') as f:
                pdf_content = f.read()
            
            print(f"文件大小: {len(pdf_content)} 字节")
            
            # 测试完整解析
            print("\n1. 测试完整PDF解析...")
            full_text = parse_pdf(pdf_content)
            print(f"   提取文本长度: {len(full_text)} 字符")
            if full_text.strip():
                # 显示前200个字符
                preview = full_text.strip()[:200].replace('\n', ' ')
                print(f"   文本预览: {preview}...")
            else:
                print("   未提取到文本内容")
            
            # 测试预览解析
            print("\n2. 测试PDF预览解析...")
            preview_text = extract_preview_text(".pdf", pdf_content)
            print(f"   预览文本长度: {len(preview_text)} 字符")
            if preview_text.strip():
                preview = preview_text.strip()[:200].replace('\n', ' ')
                print(f"   预览内容: {preview}...")
            else:
                print("   未提取到预览内容")
            
            # 比较完整解析和预览解析
            if full_text and preview_text:
                similarity = len(preview_text) / len(full_text) if len(full_text) > 0 else 0
                print(f"\n3. 预览与完整文本比例: {similarity:.2%}")
            
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    else:
        print("\n未找到PDF文件，创建测试PDF...")
        # 创建一个简单的测试PDF
        test_pdf_content = create_simple_pdf()
        
        print("测试自创建的PDF...")
        full_text = parse_pdf(test_pdf_content)
        print(f"提取文本: '{full_text.strip()}'")
        
        preview_text = extract_preview_text(".pdf", test_pdf_content)
        print(f"预览文本: '{preview_text.strip()}'")
    
    print("\n" + "=" * 60)
    print("真实PDF测试完成！")
    print("=" * 60)

def create_simple_pdf():
    """
    创建一个简单的PDF用于测试
    """
    # 使用reportlab创建PDF
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        import io
        
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        
        # 添加文本
        p.drawString(100, 750, "Simple Test PDF")
        p.drawString(100, 730, "This PDF is created for testing pdfplumber")
        p.drawString(100, 710, "Line 3: Testing Chinese characters: 测试中文字符")
        p.drawString(100, 690, "Line 4: Numbers and symbols: 123 !@#$%")
        
        p.save()
        return buffer.getvalue()
        
    except ImportError:
        print("reportlab not available")
        return b""

if __name__ == "__main__":
    test_with_real_pdf()
