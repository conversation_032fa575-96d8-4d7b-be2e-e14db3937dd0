#!/usr/bin/env python3
"""
测试文件类型缓存优化

验证优化内容：
1. Phase 1检测文件类型并缓存
2. Phase 2使用缓存，避免重复检测
3. 减少get_file_info_from_content调用次数
"""

import os
import sys
import json
import logging
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import process_all_attachments

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def test_file_type_cache_optimization():
    """测试文件类型缓存优化"""
    log.info("=" * 80)
    log.info("测试文件类型缓存优化")
    log.info("=" * 80)
    
    # 创建测试数据 - 模拟无扩展名的URL
    test_appendix = [
        {
            "text": "点击下载",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=015cc8a6-3763-4fe2-8dea-13ee25"
        },
        {
            "text": "招标文件",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=123456"
        }
    ]
    
    def mock_download(url):
        # 模拟不同类型的文件内容
        if "015cc8a6" in url:
            return b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nPDF content here..."
        elif "123456" in url:
            return b"\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1DOC file content here..."
        return b"unknown content"
    
    # 计数器，用于统计get_file_info_from_content调用次数
    call_count = 0
    
    def mock_get_file_info(content):
        nonlocal call_count
        call_count += 1
        log.info(f"get_file_info_from_content 被调用第 {call_count} 次")
        
        # 模拟文件类型检测
        if content.startswith(b"%PDF"):
            return {"ext": ".pdf", "mime": "application/pdf"}
        elif content.startswith(b"\xd0\xcf\x11\xe0"):
            return {"ext": ".doc", "mime": "application/msword"}
        return None
    
    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id
    
    def mock_get_ext_from_url(url):
        # 模拟从URL无法获取扩展名
        return ""
    
    test_source_id = "test_cache_123"
    
    with patch('analyse_appendix.download_file', side_effect=mock_download), \
         patch('analyse_appendix.get_file_info_from_content', side_effect=mock_get_file_info), \
         patch('analyse_appendix.upload_attachment_file', side_effect=mock_upload), \
         patch('analyse_appendix.get_file_extension_from_url', side_effect=mock_get_ext_from_url):
        
        log.info("Phase 1: 执行process_all_attachments")
        # Phase 1: 调用process_all_attachments
        appendix_info, file_content_cache, file_type_cache = process_all_attachments(
            appendix_list=test_appendix,
            source_id=test_source_id,
            enable_file_upload=True
        )
        
        phase1_call_count = call_count
        log.info(f"Phase 1完成，get_file_info_from_content调用次数: {phase1_call_count}")
        
        # 验证Phase 1结果
        assert len(appendix_info) == 2, f"期望处理2个文件，实际处理{len(appendix_info)}个"
        assert len(file_content_cache) == 2, f"期望缓存2个文件内容，实际缓存{len(file_content_cache)}个"
        assert len(file_type_cache) == 2, f"期望缓存2个文件类型，实际缓存{len(file_type_cache)}个"
        
        log.info("✓ Phase 1验证通过")
        log.info(f"  文件内容缓存: {list(file_content_cache.keys())}")
        log.info(f"  文件类型缓存: {list(file_type_cache.keys())}")
        
        # 验证文件类型缓存内容
        for url, file_info in file_type_cache.items():
            log.info(f"  缓存 {url}: {file_info}")
        
        # 模拟Phase 2的文件类型使用
        log.info("\nPhase 2: 模拟使用缓存的文件类型")
        
        for appendix_item in test_appendix:
            appendix_url = appendix_item.get("url")
            appendix_text = appendix_item.get("text", "")
            
            # 模拟Phase 2中的文件类型获取逻辑
            file_info = file_type_cache.get(appendix_url)
            if file_info:
                detected_ext = file_info.get("ext", "")
                log.info(f"从缓存获取文件类型: {detected_ext} for {appendix_text}")
            else:
                # 如果缓存中没有，才进行检测（这种情况不应该发生）
                log.warning(f"缓存中未找到文件类型，需要重新检测: {appendix_text}")
                file_content = file_content_cache.get(appendix_url)
                if file_content:
                    file_info = mock_get_file_info(file_content)
                    detected_ext = file_info.get("ext", "") if file_info else ""
                    log.info(f"重新检测到文件类型: {detected_ext} for {appendix_text}")
        
        phase2_call_count = call_count - phase1_call_count
        log.info(f"\nPhase 2完成，额外的get_file_info_from_content调用次数: {phase2_call_count}")
        
        # 验证优化效果
        log.info("\n" + "=" * 60)
        log.info("优化效果验证")
        log.info("=" * 60)
        
        expected_calls = len(test_appendix)  # 每个文件在Phase 1中检测一次
        actual_calls = call_count
        
        log.info(f"期望的get_file_info_from_content调用次数: {expected_calls}")
        log.info(f"实际的get_file_info_from_content调用次数: {actual_calls}")
        
        if actual_calls == expected_calls:
            log.info("✅ 优化成功！没有重复的文件类型检测")
        else:
            log.warning(f"⚠️  可能存在重复检测，多调用了 {actual_calls - expected_calls} 次")
        
        # 验证缓存有效性
        cache_hit_rate = len(file_type_cache) / len(test_appendix) * 100
        log.info(f"文件类型缓存命中率: {cache_hit_rate:.1f}%")
        
        if cache_hit_rate == 100.0:
            log.info("✅ 所有文件类型都被成功缓存")
        else:
            log.warning(f"⚠️  部分文件类型未被缓存")
        
        return {
            "total_calls": actual_calls,
            "expected_calls": expected_calls,
            "cache_hit_rate": cache_hit_rate,
            "optimization_success": actual_calls == expected_calls
        }

def test_cache_miss_scenario():
    """测试缓存未命中的场景"""
    log.info("\n" + "=" * 80)
    log.info("测试缓存未命中场景")
    log.info("=" * 80)
    
    # 模拟一个文件在Phase 1中没有被检测（比如从URL能直接获取扩展名）
    test_appendix = [
        {
            "text": "正常PDF文件",
            "url": "http://example.com/document.pdf"  # 有扩展名，Phase 1不会检测
        }
    ]
    
    call_count = 0
    
    def mock_get_file_info(content):
        nonlocal call_count
        call_count += 1
        log.info(f"get_file_info_from_content 被调用第 {call_count} 次")
        return {"ext": ".pdf", "mime": "application/pdf"}
    
    def mock_download(url):
        return b"%PDF-1.4\nPDF content here..."
    
    def mock_upload(file_content, source_id, original_filename, file_ext):
        return True, f"upload_{hash(original_filename)}"
    
    def mock_get_ext_from_url(url):
        # 这次能从URL获取扩展名
        if ".pdf" in url:
            return ".pdf"
        return ""
    
    with patch('analyse_appendix.download_file', side_effect=mock_download), \
         patch('analyse_appendix.get_file_info_from_content', side_effect=mock_get_file_info), \
         patch('analyse_appendix.upload_attachment_file', side_effect=mock_upload), \
         patch('analyse_appendix.get_file_extension_from_url', side_effect=mock_get_ext_from_url):
        
        # Phase 1
        appendix_info, file_content_cache, file_type_cache = process_all_attachments(
            appendix_list=test_appendix,
            source_id="test_cache_miss",
            enable_file_upload=True
        )
        
        phase1_calls = call_count
        log.info(f"Phase 1完成，调用次数: {phase1_calls}")
        log.info(f"文件类型缓存大小: {len(file_type_cache)}")
        
        # 模拟Phase 2中缓存未命中的情况
        appendix_url = test_appendix[0]["url"]
        file_info = file_type_cache.get(appendix_url)
        
        if not file_info:
            log.info("缓存未命中，需要重新检测文件类型")
            file_content = file_content_cache.get(appendix_url)
            if file_content:
                file_info = mock_get_file_info(file_content)
                log.info(f"重新检测结果: {file_info}")
        else:
            log.info(f"缓存命中: {file_info}")
        
        total_calls = call_count
        log.info(f"总调用次数: {total_calls}")
        
        # 在这种情况下，Phase 1不检测（因为URL有扩展名），Phase 2需要检测
        # 这是正常的，不算重复检测
        log.info("✅ 缓存未命中场景测试通过")

def main():
    """主测试函数"""
    log.info("开始文件类型缓存优化测试")
    log.info("=" * 100)
    
    try:
        # 测试1：正常的缓存优化场景
        result1 = test_file_type_cache_optimization()
        
        # 测试2：缓存未命中场景
        test_cache_miss_scenario()
        
        log.info("\n" + "=" * 100)
        log.info("🎉 所有文件类型缓存优化测试通过！")
        log.info("=" * 100)
        
        log.info("\n优化效果总结:")
        log.info(f"✓ 避免了重复的文件类型检测")
        log.info(f"✓ Phase 1检测并缓存文件类型")
        log.info(f"✓ Phase 2使用缓存，减少计算开销")
        log.info(f"✓ 缓存命中率: {result1['cache_hit_rate']:.1f}%")
        log.info(f"✓ 优化成功: {result1['optimization_success']}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
