#!/usr/bin/env python3
"""
测试相似度算法优化

验证优化内容：
1. 优化后的相似度算法能正确识别包含关系
2. 提高包含关系的权重
3. 增加核心词汇匹配
4. 去除无关词汇的干扰
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import calculate_text_similarity, check_object_name_match

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def test_similarity_cases():
    """测试各种相似度计算场景"""
    log.info("=" * 80)
    log.info("测试相似度算法优化效果")
    log.info("=" * 80)
    
    # 测试用例：包含您提到的实际案例
    test_cases = [
        {
            "name": "实际问题案例",
            "text1": "绍兴市口腔医院关于保安服务项目",
            "text2": "保安服务",
            "expected_high": True,  # 期望高相似度
            "description": "短文本完全包含在长文本中"
        },
        {
            "name": "完全匹配",
            "text1": "保安服务",
            "text2": "保安服务",
            "expected_high": True,
            "description": "完全相同的文本"
        },
        {
            "name": "包含关系1",
            "text1": "医疗设备采购项目",
            "text2": "医疗设备",
            "expected_high": True,
            "description": "核心词汇包含"
        },
        {
            "name": "包含关系2",
            "text1": "CT扫描仪",
            "text2": "某某医院关于CT扫描仪采购项目招标公告",
            "expected_high": True,
            "description": "短文本包含在长文本中"
        },
        {
            "name": "核心词汇匹配",
            "text1": "清洁服务外包",
            "text2": "物业清洁服务项目",
            "expected_high": True,
            "description": "核心词汇匹配"
        },
        {
            "name": "无关文本",
            "text1": "保安服务",
            "text2": "医疗设备",
            "expected_high": False,
            "description": "完全不相关的文本"
        },
        {
            "name": "部分相关",
            "text1": "保安服务项目",
            "text2": "清洁服务项目",
            "expected_high": False,
            "description": "部分词汇相同但核心不同"
        }
    ]
    
    log.info("测试用例结果:")
    log.info("-" * 80)
    
    for i, case in enumerate(test_cases, 1):
        similarity = calculate_text_similarity(case["text1"], case["text2"])
        
        # 判断是否符合期望
        is_high = similarity >= 0.5  # 使用0.5作为高相似度阈值
        is_correct = is_high == case["expected_high"]
        
        status = "✓" if is_correct else "❌"
        expectation = "高相似度" if case["expected_high"] else "低相似度"
        
        log.info(f"{status} 案例{i}: {case['name']}")
        log.info(f"    文本1: '{case['text1']}'")
        log.info(f"    文本2: '{case['text2']}'")
        log.info(f"    相似度: {similarity}")
        log.info(f"    期望: {expectation}, 实际: {'高相似度' if is_high else '低相似度'}")
        log.info(f"    说明: {case['description']}")
        log.info("")
        
        # 验证关键案例
        if case["name"] == "实际问题案例":
            assert similarity >= 0.5, f"实际问题案例相似度应该>=0.5，实际为{similarity}"
            log.info(f"    🎯 关键案例验证通过！相似度从0.213提升到{similarity}")
    
    log.info("✅ 相似度算法测试完成")

def test_object_name_matching():
    """测试object_name匹配功能"""
    log.info("\n" + "=" * 80)
    log.info("测试object_name匹配功能")
    log.info("=" * 80)
    
    # 测试用例
    test_cases = [
        {
            "main": "绍兴市口腔医院关于保安服务项目",
            "tender": "保安服务",
            "should_match": True
        },
        {
            "main": "医疗设备采购",
            "tender": "CT扫描仪",
            "should_match": False  # 不够相似
        },
        {
            "main": "某某医院CT设备采购项目",
            "tender": "CT设备",
            "should_match": True
        },
        {
            "main": "",  # 空值
            "tender": "保安服务",
            "should_match": True  # 空值应该匹配
        },
        {
            "main": "保安服务",
            "tender": "",  # 空值
            "should_match": False  # 招标文件空值不匹配
        }
    ]
    
    log.info("object_name匹配测试结果:")
    log.info("-" * 80)
    
    for i, case in enumerate(test_cases, 1):
        is_match, similarity, reason = check_object_name_match(
            case["main"], case["tender"]
        )
        
        status = "✓" if is_match == case["should_match"] else "❌"
        
        log.info(f"{status} 测试{i}:")
        log.info(f"    主体: '{case['main']}'")
        log.info(f"    招标: '{case['tender']}'")
        log.info(f"    匹配结果: {is_match}")
        log.info(f"    相似度: {similarity}")
        log.info(f"    原因: {reason}")
        log.info(f"    期望匹配: {case['should_match']}")
        log.info("")
    
    log.info("✅ object_name匹配测试完成")

def test_before_after_comparison():
    """对比优化前后的效果"""
    log.info("\n" + "=" * 80)
    log.info("优化前后效果对比")
    log.info("=" * 80)
    
    # 关键测试案例
    main_text = "绍兴市口腔医院关于保安服务项目"
    tender_text = "保安服务"
    
    # 计算优化后的相似度
    new_similarity = calculate_text_similarity(main_text, tender_text)
    
    log.info("关键案例分析:")
    log.info(f"主体文本: '{main_text}'")
    log.info(f"招标文本: '{tender_text}'")
    log.info("")
    log.info("优化前:")
    log.info("  相似度: 0.213")
    log.info("  匹配结果: ❌ 不匹配 (< 0.5)")
    log.info("  问题: 编辑距离权重过高，包含关系权重过低")
    log.info("")
    log.info("优化后:")
    log.info(f"  相似度: {new_similarity}")
    log.info(f"  匹配结果: {'✅ 匹配' if new_similarity >= 0.5 else '❌ 不匹配'} ({'≥' if new_similarity >= 0.5 else '<'} 0.5)")
    log.info("  改进: 提高包含关系权重，增加核心词汇匹配，去除无关词汇")
    log.info("")
    
    improvement = new_similarity - 0.213
    improvement_percent = (improvement / 0.213) * 100
    
    log.info(f"改进效果:")
    log.info(f"  相似度提升: +{improvement:.3f}")
    log.info(f"  提升幅度: +{improvement_percent:.1f}%")
    log.info(f"  匹配状态: 从不匹配 → {'匹配' if new_similarity >= 0.5 else '仍不匹配'}")
    
    # 验证改进效果
    assert new_similarity > 0.213, "优化后相似度应该有所提升"
    assert new_similarity >= 0.5, "关键案例应该能够匹配"
    
    log.info("\n✅ 优化效果验证通过")

def test_edge_cases():
    """测试边界情况"""
    log.info("\n" + "=" * 80)
    log.info("测试边界情况")
    log.info("=" * 80)
    
    edge_cases = [
        ("", "", "两个空字符串"),
        ("", "保安服务", "一个空字符串"),
        ("保安服务", "", "另一个空字符串"),
        ("a", "b", "单字符"),
        ("保", "安", "单个中文字符"),
        ("保安", "保安服务项目招标公告", "短文本包含在长文本开头"),
        ("项目", "保安服务项目招标公告", "短文本包含在长文本结尾"),
        ("服务", "保安服务项目招标公告", "短文本包含在长文本中间"),
    ]
    
    log.info("边界情况测试:")
    log.info("-" * 50)
    
    for text1, text2, description in edge_cases:
        try:
            similarity = calculate_text_similarity(text1, text2)
            log.info(f"✓ {description}")
            log.info(f"    '{text1}' vs '{text2}' → {similarity}")
        except Exception as e:
            log.error(f"❌ {description}")
            log.error(f"    '{text1}' vs '{text2}' → 错误: {e}")
        log.info("")
    
    log.info("✅ 边界情况测试完成")

def main():
    """主测试函数"""
    log.info("开始相似度算法优化测试")
    log.info("=" * 100)
    
    try:
        # 运行所有测试
        test_similarity_cases()
        test_object_name_matching()
        test_before_after_comparison()
        test_edge_cases()
        
        log.info("\n" + "=" * 100)
        log.info("🎉 所有相似度算法优化测试通过！")
        log.info("=" * 100)
        
        log.info("\n优化总结:")
        log.info("✓ 提高包含关系权重从20%到50%")
        log.info("✓ 增加核心词汇匹配机制(30%权重)")
        log.info("✓ 降低编辑距离权重从40%到15%")
        log.info("✓ 去除常见无关词汇的干扰")
        log.info("✓ 解决实际问题案例：相似度从0.213提升到>0.5")
        log.info("✓ 保持对完全不相关文本的区分能力")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
