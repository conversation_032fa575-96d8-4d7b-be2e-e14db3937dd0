#!/usr/bin/env python3
"""
测试 update_source_title.py 脚本的功能

该脚本用于验证：
1. ES连接是否正常
2. 查询缺失source_title字段的文档是否正常
3. 从chn_ylcg获取title是否正常
"""

import sys
from dotenv import load_dotenv
import os

from es_deal import init_es_client, search_documents
from utils.log_cfg import log


def test_es_connection():
    """测试ES连接"""
    try:
        log.info("测试ES连接...")
        es = init_es_client()
        
        # 测试连接
        if es.ping():
            log.info("✓ ES连接成功")
            return es
        else:
            log.error("✗ ES连接失败")
            return None
            
    except Exception as e:
        log.error(f"✗ ES连接异常: {e}")
        return None


def test_query_missing_source_title(es_client, index_name: str):
    """测试查询缺失source_title字段的文档"""
    try:
        log.info("测试查询缺失source_title字段的文档...")
        
        # 查询缺失source_title字段的文档总数
        query = {
            "query": {
                "bool": {
                    "must_not": {
                        "exists": {
                            "field": "source_title"
                        }
                    }
                }
            }
        }
        
        result = es_client.count(index=index_name, body=query)
        total_count = result.get("count", 0)
        log.info(f"✓ 缺失source_title字段的文档总数: {total_count}")
        
        if total_count > 0:
            # 获取前5个文档作为样本
            query["size"] = 5
            query["_source"] = ["source_id"]
            
            result = search_documents(es_client, index_name, query=query)
            
            if result and result.get("hits", {}).get("hits"):
                log.info("✓ 样本文档:")
                for i, hit in enumerate(result["hits"]["hits"][:5], 1):
                    doc_id = hit["_id"]
                    source_id = hit["_source"].get("source_id", "N/A")
                    log.info(f"  {i}. doc_id: {doc_id}, source_id: {source_id}")
                return result["hits"]["hits"]
            else:
                log.warning("✗ 未获取到样本文档")
                return []
        else:
            log.info("没有缺失source_title字段的文档")
            return []
            
    except Exception as e:
        log.error(f"✗ 查询失败: {e}")
        return []


def test_get_title_from_chn_ylcg(es_client, source_ids: list):
    """测试从chn_ylcg获取title"""
    try:
        log.info("测试从chn_ylcg获取title...")
        
        success_count = 0
        for i, source_id in enumerate(source_ids[:3], 1):  # 只测试前3个
            try:
                response = es_client.get(
                    index="chn_ylcg", 
                    id=source_id, 
                    _source=["title"]
                )
                
                if response and response.get("_source"):
                    title = response["_source"].get("title", "")
                    if title:
                        log.info(f"  ✓ {i}. source_id: {source_id} -> title: '{title[:50]}...'")
                        success_count += 1
                    else:
                        log.warning(f"  ✗ {i}. source_id: {source_id} -> title为空")
                else:
                    log.warning(f"  ✗ {i}. source_id: {source_id} -> 未找到文档")
                    
            except Exception as e:
                if "not_found" in str(e).lower():
                    log.warning(f"  ✗ {i}. source_id: {source_id} -> 文档不存在")
                else:
                    log.error(f"  ✗ {i}. source_id: {source_id} -> 查询异常: {e}")
        
        log.info(f"✓ 成功获取title的文档: {success_count}/{min(len(source_ids), 3)}")
        
    except Exception as e:
        log.error(f"✗ 测试获取title失败: {e}")


def test_index_mapping(es_client, index_name: str):
    """测试索引映射"""
    try:
        log.info("测试索引映射...")
        
        # 检查索引是否存在
        if es_client.indices.exists(index=index_name):
            log.info(f"✓ 索引 {index_name} 存在")
            
            # 获取映射信息
            mapping = es_client.indices.get_mapping(index=index_name)
            
            # 检查是否有source_title字段的映射
            index_mapping = mapping.get(index_name, {}).get("mappings", {}).get("properties", {})
            
            if "source_title" in index_mapping:
                log.info("✓ source_title字段已在映射中定义")
            else:
                log.warning("✗ source_title字段未在映射中定义（这是正常的，字段会动态添加）")
                
            if "source_id" in index_mapping:
                log.info("✓ source_id字段已在映射中定义")
            else:
                log.warning("✗ source_id字段未在映射中定义")
                
        else:
            log.error(f"✗ 索引 {index_name} 不存在")
            
    except Exception as e:
        log.error(f"✗ 测试索引映射失败: {e}")


def main():
    """主函数"""
    try:
        log.info("开始测试 update_source_title.py 脚本功能...")
        log.info("=" * 60)
        
        # 加载环境变量
        load_dotenv()
        
        # 获取索引名称
        index_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
        if not index_name:
            log.error("环境变量ES_INDEX_ANALYSIS_ALIAS未设置")
            sys.exit(1)
            
        log.info(f"目标索引: {index_name}")
        log.info("=" * 60)
        
        # 1. 测试ES连接
        es = test_es_connection()
        if not es:
            log.error("ES连接失败，停止测试")
            sys.exit(1)
        
        log.info("=" * 60)
        
        # 2. 测试索引映射
        test_index_mapping(es, index_name)
        
        log.info("=" * 60)
        
        # 3. 测试查询缺失source_title字段的文档
        sample_docs = test_query_missing_source_title(es, index_name)
        
        if sample_docs:
            log.info("=" * 60)
            
            # 4. 测试从chn_ylcg获取title
            source_ids = [doc["_source"].get("source_id") for doc in sample_docs if doc["_source"].get("source_id")]
            if source_ids:
                test_get_title_from_chn_ylcg(es, source_ids)
            else:
                log.warning("没有有效的source_id用于测试")
        
        log.info("=" * 60)
        log.info("测试完成")
        
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
