#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试当主体解析结果为空但招标文件解析结果不为空时的处理逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import intelligent_merge_analysis, ensure_list
import json


def test_empty_main_with_tender_results():
    """测试主体解析结果为空，但有招标文件解析结果的情况"""
    print("=" * 80)
    print("测试1: 主体解析结果为空，但有招标文件解析结果")
    print("=" * 80)
    
    # 模拟空的主体解析结果
    main_results = []
    
    # 模拟招标文件解析结果
    tender_results = [
        {
            "source_id": "test_001",
            "prj_name": "医院设备采购项目",
            "prj_number": "YY2025001",
            "tenderee": "某市人民医院",
            "object_name": "CT设备",
            "object_brand": "西门子",
            "object_model": "SOMATOM CT",
            "agent": "某招标代理公司",
            "bid_doc_name": "招标文件.pdf",
            "bid_doc_ext": "pdf",
            "bid_doc_link_out": "http://example.com/tender.pdf",
            "bid_doc_link_key": "tender_123"
        }
    ]
    
    # 模拟空的合同文件解析结果
    contract_results = []
    
    print("输入数据:")
    print(f"  主体解析结果数量: {len(main_results)}")
    print(f"  招标文件解析结果数量: {len(tender_results)}")
    print(f"  合同文件解析结果数量: {len(contract_results)}")
    
    print("\n招标文件解析结果:")
    for i, result in enumerate(tender_results):
        print(f"  结果 {i+1}: {result.get('prj_name')} - {result.get('object_name')}")
    
    # 执行智能融合分析
    print("\n执行智能融合分析...")
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
    )
    
    print(f"\n融合结果数量: {len(merged_results)}")
    
    if merged_results:
        print("\n融合结果:")
        for i, result in enumerate(merged_results):
            print(f"  结果 {i+1}:")
            print(f"    项目名称: {result.get('prj_name')}")
            print(f"    招标人: {result.get('tenderee')}")
            print(f"    标的物名称: {result.get('object_name')}")
            print(f"    标的物品牌: {result.get('object_brand')}")
            print(f"    招标文件名: {result.get('bid_doc_name')}")
        
        # 验证结果
        expected_prj_name = "医院设备采购项目"
        expected_tenderee = "某市人民医院"
        expected_object_name = "CT设备"
        
        if (merged_results[0].get('prj_name') == expected_prj_name and
            merged_results[0].get('tenderee') == expected_tenderee and
            merged_results[0].get('object_name') == expected_object_name):
            print("\n✓ 测试通过：成功使用招标文件解析结果")
            return True
        else:
            print("\n✗ 测试失败：融合结果不正确")
            return False
    else:
        print("\n✗ 测试失败：没有返回融合结果")
        return False


def test_empty_main_with_contract_results():
    """测试主体解析结果为空，但有合同文件解析结果的情况"""
    print("\n" + "=" * 80)
    print("测试2: 主体解析结果为空，但有合同文件解析结果")
    print("=" * 80)
    
    # 模拟空的主体解析结果
    main_results = []
    
    # 模拟空的招标文件解析结果
    tender_results = []
    
    # 模拟合同文件解析结果
    contract_results = [
        {
            "source_id": "test_002",
            "bidder_name": "医疗设备有限公司",
            "bidder_price": 2500000.0,
            "bidder_contact_person": "张经理",
            "bidder_contact_phone_number": "13800138000",
            "contract_name": "合同文件.pdf",
            "contract_ext": "pdf",
            "contract_link_out": "http://example.com/contract.pdf",
            "contract_link_key": "contract_456"
        }
    ]
    
    print("输入数据:")
    print(f"  主体解析结果数量: {len(main_results)}")
    print(f"  招标文件解析结果数量: {len(tender_results)}")
    print(f"  合同文件解析结果数量: {len(contract_results)}")
    
    print("\n合同文件解析结果:")
    for i, result in enumerate(contract_results):
        print(f"  结果 {i+1}: {result.get('bidder_name')} - {result.get('bidder_price')}")
    
    # 执行智能融合分析
    print("\n执行智能融合分析...")
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
    )
    
    print(f"\n融合结果数量: {len(merged_results)}")
    
    if merged_results:
        print("\n融合结果:")
        for i, result in enumerate(merged_results):
            print(f"  结果 {i+1}:")
            print(f"    中标单位: {result.get('bidder_name')}")
            print(f"    中标金额: {result.get('bidder_price')}")
            print(f"    联系人: {result.get('bidder_contact_person')}")
            print(f"    合同文件名: {result.get('contract_name')}")
        
        # 验证结果
        expected_bidder_name = "医疗设备有限公司"
        expected_bidder_price = 2500000.0
        
        if (merged_results[0].get('bidder_name') == expected_bidder_name and
            merged_results[0].get('bidder_price') == expected_bidder_price):
            print("\n✓ 测试通过：成功使用合同文件解析结果")
            return True
        else:
            print("\n✗ 测试失败：融合结果不正确")
            return False
    else:
        print("\n✗ 测试失败：没有返回融合结果")
        return False


def test_empty_main_with_both_results():
    """测试主体解析结果为空，但同时有招标文件和合同文件解析结果的情况"""
    print("\n" + "=" * 80)
    print("测试3: 主体解析结果为空，但同时有招标文件和合同文件解析结果")
    print("=" * 80)
    
    # 模拟空的主体解析结果
    main_results = []
    
    # 模拟招标文件解析结果
    tender_results = [
        {
            "source_id": "test_003",
            "prj_name": "医院设备采购项目",
            "tenderee": "某市人民医院",
            "object_name": "MRI设备",
            "object_brand": "飞利浦",
            "bid_doc_name": "招标文件.pdf",
            "bid_doc_ext": "pdf"
        }
    ]
    
    # 模拟合同文件解析结果
    contract_results = [
        {
            "source_id": "test_003",
            "bidder_name": "医疗设备有限公司",
            "bidder_price": 3500000.0,
            "bidder_contact_person": "李经理",
            "contract_name": "合同文件.pdf",
            "contract_ext": "pdf"
        }
    ]
    
    print("输入数据:")
    print(f"  主体解析结果数量: {len(main_results)}")
    print(f"  招标文件解析结果数量: {len(tender_results)}")
    print(f"  合同文件解析结果数量: {len(contract_results)}")
    
    # 执行智能融合分析
    print("\n执行智能融合分析...")
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
    )
    
    print(f"\n融合结果数量: {len(merged_results)}")
    
    if merged_results:
        print("\n融合结果:")
        for i, result in enumerate(merged_results):
            print(f"  结果 {i+1}:")
            print(f"    项目名称: {result.get('prj_name')}")
            print(f"    招标人: {result.get('tenderee')}")
            print(f"    标的物名称: {result.get('object_name')}")
            print(f"    标的物品牌: {result.get('object_brand')}")
            print(f"    中标单位: {result.get('bidder_name')}")
            print(f"    中标金额: {result.get('bidder_price')}")
            print(f"    招标文件名: {result.get('bid_doc_name')}")
            print(f"    合同文件名: {result.get('contract_name')}")
        
        # 验证结果：应该包含来自招标文件和合同文件的信息
        result = merged_results[0]
        has_tender_info = result.get('prj_name') == "医院设备采购项目"
        has_contract_info = result.get('bidder_name') == "医疗设备有限公司"
        
        if has_tender_info and has_contract_info:
            print("\n✓ 测试通过：成功融合招标文件和合同文件解析结果")
            return True
        else:
            print("\n✗ 测试失败：融合结果不完整")
            return False
    else:
        print("\n✗ 测试失败：没有返回融合结果")
        return False


def test_all_empty_results():
    """测试所有解析结果都为空的情况"""
    print("\n" + "=" * 80)
    print("测试4: 所有解析结果都为空")
    print("=" * 80)
    
    # 模拟所有空的解析结果
    main_results = []
    tender_results = []
    contract_results = []
    
    print("输入数据:")
    print(f"  主体解析结果数量: {len(main_results)}")
    print(f"  招标文件解析结果数量: {len(tender_results)}")
    print(f"  合同文件解析结果数量: {len(contract_results)}")
    
    # 执行智能融合分析
    print("\n执行智能融合分析...")
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
    )
    
    print(f"\n融合结果数量: {len(merged_results)}")
    
    if len(merged_results) == 0:
        print("\n✓ 测试通过：所有结果为空时正确返回空列表")
        return True
    else:
        print("\n✗ 测试失败：应该返回空列表")
        return False


def main():
    """运行所有测试"""
    print("测试主体解析结果为空时的处理逻辑")
    print("=" * 80)
    
    test_results = []
    
    try:
        test_results.append(test_empty_main_with_tender_results())
        test_results.append(test_empty_main_with_contract_results())
        test_results.append(test_empty_main_with_both_results())
        test_results.append(test_all_empty_results())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "主体为空，有招标文件结果",
            "主体为空，有合同文件结果", 
            "主体为空，有招标和合同文件结果",
            "所有结果都为空"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！修复成功！")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
