#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证markersweb_attachment_analysis_alias索引的source_response字段更新结果
"""

import os
from es_deal import init_es_client
from dotenv import load_dotenv
from utils.log_cfg import log


def verify_update_results(es_client, analysis_index: str):
    """
    验证更新结果
    
    Args:
        es_client: Elasticsearch客户端
        analysis_index: 分析结果索引名
    """
    try:
        # 查询有source_response字段的文档数量
        query_with_response = {
            "query": {
                "exists": {"field": "source_response"}
            }
        }
        
        response_with = es_client.count(
            index=analysis_index,
            body=query_with_response
        )
        
        # 查询没有source_response字段的文档数量
        query_without_response = {
            "query": {
                "bool": {
                    "must_not": [
                        {"exists": {"field": "source_response"}}
                    ]
                }
            }
        }
        
        response_without = es_client.count(
            index=analysis_index,
            body=query_without_response
        )
        
        # 查询总文档数量
        total_response = es_client.count(index=analysis_index)
        
        total_count = total_response["count"]
        with_response_count = response_with["count"]
        without_response_count = response_without["count"]
        
        log.info("=" * 60)
        log.info("更新结果验证:")
        log.info(f"索引: {analysis_index}")
        log.info(f"总文档数: {total_count}")
        log.info(f"有source_response字段的文档数: {with_response_count}")
        log.info(f"没有source_response字段的文档数: {without_response_count}")
        log.info(f"更新覆盖率: {with_response_count/total_count*100:.2f}%")
        log.info("=" * 60)
        
        # 抽样检查几个文档
        sample_query = {
            "query": {
                "exists": {"field": "source_response"}
            },
            "size": 3,
            "_source": ["source_id", "source_response"]
        }
        
        sample_response = es_client.search(
            index=analysis_index,
            body=sample_query
        )
        
        log.info("抽样检查结果:")
        for i, hit in enumerate(sample_response.get("hits", {}).get("hits", []), 1):
            source_id = hit["_source"].get("source_id", "N/A")
            source_response_length = len(hit["_source"].get("source_response", ""))
            log.info(f"样本 {i}: source_id={source_id}, source_response长度={source_response_length}")
        
    except Exception as e:
        log.error(f"验证失败: {e}")
        raise


def main():
    """
    主函数
    """
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        # 验证更新结果
        analysis_index = "markersweb_attachment_analysis_alias"
        verify_update_results(es_client, analysis_index)
        
    except Exception as e:
        log.error(f"验证失败: {e}")
        raise


if __name__ == "__main__":
    main()
