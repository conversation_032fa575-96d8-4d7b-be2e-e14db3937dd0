#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试黑名单功能
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from blacklist_manager import BlacklistManager, print_blacklist_stats


def test_blacklist_basic_operations():
    """测试黑名单基本操作"""
    print("=" * 80)
    print("测试黑名单基本操作")
    print("=" * 80)
    
    # 使用测试数据库
    manager = BlacklistManager("test_blacklist.db")
    
    # 清空测试数据库
    manager.clear_blacklist()
    
    # 测试添加到黑名单
    print("\n1. 测试添加文档到黑名单")
    success = manager.add_to_blacklist(
        document_id="test_001",
        document_title="测试文档1",
        document_url="http://example.com/test1",
        failure_reason="LLM API调用失败: 504 Gateway Timeout"
    )
    
    if success:
        print("✓ 成功添加文档到黑名单")
    else:
        print("✗ 添加文档到黑名单失败")
    
    # 测试检查黑名单状态
    print("\n2. 测试检查文档是否在黑名单中")
    is_blacklisted = manager.is_blacklisted("test_001")
    if is_blacklisted:
        print("✓ 文档在黑名单中")
    else:
        print("✗ 文档不在黑名单中")
    
    # 测试重复添加（应该增加失败次数）
    print("\n3. 测试重复添加文档（应该增加失败次数）")
    success = manager.add_to_blacklist(
        document_id="test_001",
        document_title="测试文档1",
        document_url="http://example.com/test1",
        failure_reason="LLM API调用失败: 再次超时"
    )
    
    if success:
        print("✓ 成功更新文档失败次数")
    else:
        print("✗ 更新文档失败次数失败")
    
    # 添加更多测试数据
    print("\n4. 添加更多测试数据")
    test_docs = [
        ("test_002", "测试文档2", "http://example.com/test2", "JSON解析错误"),
        ("test_003", "测试文档3", "http://example.com/test3", "网络连接超时"),
        ("test_004", "测试文档4", "http://example.com/test4", "模型响应异常"),
    ]
    
    for doc_id, title, url, reason in test_docs:
        manager.add_to_blacklist(doc_id, title, url, reason)
    
    print(f"✓ 添加了 {len(test_docs)} 个测试文档")
    
    # 测试获取黑名单列表
    print("\n5. 测试获取黑名单列表")
    blacklist = manager.get_blacklist()
    print(f"黑名单中共有 {len(blacklist)} 个文档:")
    for item in blacklist:
        print(f"  - {item['document_id']}: {item['document_title']} (失败{item['failure_count']}次)")
    
    # 测试获取黑名单ID列表
    print("\n6. 测试获取黑名单ID列表")
    blacklisted_ids = manager.get_blacklisted_ids()
    print(f"黑名单ID列表: {blacklisted_ids}")
    
    # 测试统计信息
    print("\n7. 测试统计信息")
    print_blacklist_stats(manager)
    
    # 测试移除文档
    print("\n8. 测试移除文档")
    success = manager.remove_from_blacklist("test_002")
    if success:
        print("✓ 成功移除文档 test_002")
    else:
        print("✗ 移除文档失败")
    
    # 验证移除结果
    is_blacklisted = manager.is_blacklisted("test_002")
    if not is_blacklisted:
        print("✓ 文档 test_002 已不在黑名单中")
    else:
        print("✗ 文档 test_002 仍在黑名单中")
    
    # 清理测试数据
    print("\n9. 清理测试数据")
    manager.clear_blacklist()
    stats = manager.get_blacklist_stats()
    if stats['total_count'] == 0:
        print("✓ 测试数据清理完成")
    else:
        print("✗ 测试数据清理失败")
    
    # 删除测试数据库文件
    try:
        os.remove("test_blacklist.db")
        print("✓ 测试数据库文件已删除")
    except:
        print("✗ 删除测试数据库文件失败")
    
    return True


def test_blacklist_integration():
    """测试黑名单与主程序的集成"""
    print("\n" + "=" * 80)
    print("测试黑名单与主程序的集成")
    print("=" * 80)
    
    # 模拟主程序的使用场景
    manager = BlacklistManager("integration_test.db")
    manager.clear_blacklist()
    
    # 模拟处理失败的文档
    failed_docs = [
        {
            "id": "-1kMqpcBsUtJ06NfAuOo",
            "title": "北京大学人民医院诊疗能力提升项目公开招标公告",
            "url": "http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm",
            "error": "LLM API调用失败 (已重试3次): 504 Gateway Time-out"
        },
        {
            "id": "-1o4r5cBsUtJ06NfkR31",
            "title": "巴楚县中医医院中草药饮片采购项目中标(成交)结果公告",
            "url": "http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250626_24849935.htm",
            "error": "LLM API调用失败 (已重试3次): 504 Gateway Time-out"
        }
    ]
    
    print("\n1. 模拟添加失败文档到黑名单")
    for doc in failed_docs:
        success = manager.add_to_blacklist(
            document_id=doc["id"],
            document_title=doc["title"],
            document_url=doc["url"],
            failure_reason=doc["error"]
        )
        if success:
            print(f"✓ 添加失败文档到黑名单: {doc['id']}")
        else:
            print(f"✗ 添加失败: {doc['id']}")
    
    print("\n2. 模拟主程序获取黑名单ID列表")
    blacklisted_ids = manager.get_blacklisted_ids()
    print(f"需要排除的黑名单ID: {blacklisted_ids}")
    
    print("\n3. 模拟检查文档是否在黑名单中")
    test_ids = ["-1kMqpcBsUtJ06NfAuOo", "-1o4r5cBsUtJ06NfkR31", "new_document_id"]
    for test_id in test_ids:
        is_blacklisted = manager.is_blacklisted(test_id)
        status = "在黑名单中" if is_blacklisted else "不在黑名单中"
        print(f"  文档 {test_id}: {status}")
    
    print("\n4. 显示黑名单统计")
    print_blacklist_stats(manager)
    
    # 清理
    manager.clear_blacklist()
    try:
        os.remove("integration_test.db")
        print("\n✓ 集成测试数据清理完成")
    except:
        print("\n✗ 集成测试数据清理失败")
    
    return True


def main():
    """运行所有测试"""
    print("黑名单功能测试")
    print("=" * 80)
    
    test_results = []
    
    try:
        test_results.append(test_blacklist_basic_operations())
        test_results.append(test_blacklist_integration())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "黑名单基本操作",
            "黑名单与主程序集成"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！黑名单功能正常！")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
