# clean_file_fields_by_keywords.py 使用说明

## 功能描述

该脚本用于根据文件名称关键词清理不符合条件的招标文件和合同文件字段，确保数据的准确性和一致性。

### 清理规则

#### 招标文件字段清理
- **触发条件**：`bid_doc_name` 字段有数据
- **关键词要求**：文件名称必须包含以下关键词之一：
  - "招标文件"
  - "磋商文件" 
  - "谈判文件"
- **清理操作**：如果不包含关键词，将以下字段设置为 `null`：
  - `bid_doc_name`
  - `bid_doc_ext`
  - `bid_doc_link_out`
  - `bid_doc_link_key`

#### 合同文件字段清理
- **触发条件**：`contract_name` 字段有数据
- **关键词要求**：文件名称必须包含以下关键词之一：
  - "合同"
  - "服务协议"
- **清理操作**：如果不包含关键词，将以下字段设置为 `null`：
  - `contract_name`
  - `contract_ext`
  - `contract_link_out`
  - `contract_link_key`

## 使用方法

### 基本用法

```bash
# 处理一个批次（默认100个文档）
python clean_file_fields_by_keywords.py

# 指定批处理大小
python clean_file_fields_by_keywords.py --batch-size 50

# 试运行模式（不执行实际更新）
python clean_file_fields_by_keywords.py --dry-run

# 处理所有需要清理的文档
python clean_file_fields_by_keywords.py --all

# 指定索引名称
python clean_file_fields_by_keywords.py --index markersweb_attachment_analysis_alias
```

### 参数说明

- `--index`: 目标索引名称（默认从环境变量 `ES_INDEX_ANALYSIS_ALIAS` 获取）
- `--batch-size`: 批处理大小（默认100）
- `--dry-run`: 试运行模式，不执行实际更新
- `--all`: 处理所有需要清理的文档（分批处理）

### 环境变量配置

脚本需要以下环境变量（在 `.env` 文件中配置）：

```env
ES_HOST=http://**********:9200
ES_USER=elastic
ES_PASSWORD=your_password
ES_INDEX_ANALYSIS_ALIAS=markersweb_attachment_analysis_alias
```

## 执行流程

### 单批次处理模式（默认）

1. 连接到Elasticsearch
2. 查询包含 `bid_doc_name` 或 `contract_name` 字段的文档总数
3. 获取一个批次的文档（默认100个）
4. 对每个文档：
   - 检查 `bid_doc_name` 是否包含招标文件关键词
   - 检查 `contract_name` 是否包含合同文件关键词
   - 准备清理不符合条件的字段
5. 批量更新文档
6. 输出统计信息

### 全量处理模式（--all）

1. 使用 Scroll API 分批处理所有文档
2. 每个批次处理指定数量的文档
3. 处理完一个批次后继续下一批次
4. 输出最终统计信息

## 清理示例

### 招标文件字段清理示例

```python
# 保留的情况（包含关键词）
"医疗设备采购招标文件.pdf" → 保留所有 bid_doc_* 字段
"设备采购磋商文件.docx" → 保留所有 bid_doc_* 字段
"医疗器械采购谈判文件.pdf" → 保留所有 bid_doc_* 字段

# 清理的情况（不包含关键词）
"医疗设备采购公告.pdf" → 清空所有 bid_doc_* 字段
"设备中标公告.docx" → 清空所有 bid_doc_* 字段
"技术规格书.pdf" → 清空所有 bid_doc_* 字段
```

### 合同文件字段清理示例

```python
# 保留的情况（包含关键词）
"医疗设备采购合同.pdf" → 保留所有 contract_* 字段
"设备维护服务协议.docx" → 保留所有 contract_* 字段

# 清理的情况（不包含关键词）
"医疗设备招标文件.pdf" → 清空所有 contract_* 字段
"设备中标通知书.docx" → 清空所有 contract_* 字段
"验收报告.pdf" → 清空所有 contract_* 字段
```

## 输出信息

### 统计信息

```
==================================================
处理统计:
  本批次文档总数: 100
  需要更新的文档: 25
  招标文件字段:
    清理的文档: 10
    保留的文档: 15
  合同文件字段:
    清理的文档: 8
    保留的文档: 12
==================================================
```

### 日志信息

- 清理操作：`清理招标文件字段 doc_id: 'filename' (不包含关键词)`
- 保留操作：`保留招标文件字段 doc_id: 'filename' (包含关键词)`
- 更新结果：`批量更新完成 - 成功: 25, 失败: 0`

## 注意事项

1. **试运行模式**：建议首次运行时使用 `--dry-run` 参数查看清理结果
2. **批处理大小**：根据系统性能调整 `--batch-size` 参数
3. **关键词匹配**：使用简单的字符串包含匹配，大小写不敏感
4. **数据备份**：重要数据建议先备份
5. **索引检查**：脚本会自动检查目标索引是否存在

## 错误处理

- **连接失败**：检查ES配置和网络连接
- **索引不存在**：检查索引名称是否正确
- **权限错误**：检查ES用户权限
- **批量更新失败**：检查ES集群状态和索引健康状况

## 测试验证

使用测试脚本验证功能：

```bash
python test_clean_file_fields.py
```

测试内容包括：
- 常量定义测试
- 招标文件关键词测试
- 合同文件关键词测试
- 自定义关键词测试
- 真实世界例子测试

**测试结果：** ✅ 所有测试通过（51/51）

## 性能特点

- **高效查询**：只查询包含相关字段的文档
- **批量处理**：使用批量更新减少网络开销
- **内存友好**：使用 Scroll API 处理大量数据
- **详细日志**：提供完整的处理统计和错误信息

## 示例输出

```
2025-01-07 10:00:00 - INFO - 正在初始化Elasticsearch客户端...
2025-01-07 10:00:01 - INFO - 目标索引: markersweb_attachment_analysis_alias
2025-01-07 10:00:01 - INFO - 批处理大小: 100
2025-01-07 10:00:01 - INFO - 试运行模式: False
2025-01-07 10:00:01 - INFO - 处理所有文档: True
============================================================
清理规则:
  招标文件关键词: ['招标文件', '磋商文件', '谈判文件']
  合同文件关键词: ['合同', '服务协议']
  如果文件名不包含对应关键词，将清空相关字段
============================================================
2025-01-07 10:00:02 - INFO - 索引中包含文件字段的文档总数: 1500
2025-01-07 10:00:02 - INFO - 本批次检查 100 个文档（总共 1500 个）
2025-01-07 10:00:05 - INFO - 批量更新完成 - 成功: 25, 失败: 0
2025-01-07 10:00:05 - INFO - 清理完成 - 成功: 25, 失败: 0
```

## 建议使用流程

1. **首先运行测试脚本**：
   ```bash
   python test_clean_file_fields.py
   ```

2. **试运行主脚本**：
   ```bash
   python clean_file_fields_by_keywords.py --dry-run
   ```

3. **正式执行清理**：
   ```bash
   python clean_file_fields_by_keywords.py --all
   ```

这个脚本能够有效地清理不符合命名规范的文件字段，确保数据的准确性和一致性。
