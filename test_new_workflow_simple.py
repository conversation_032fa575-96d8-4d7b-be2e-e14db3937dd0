#!/usr/bin/env python3
"""
简化的新工作流程测试

专门测试新实现的两阶段附件处理功能：
1. process_all_attachments函数
2. 新的字段更新逻辑
3. 早期退出逻辑
"""

import os
import sys
import json
import logging
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import process_all_attachments, STANDARD_FIELDS

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def test_new_workflow_components():
    """测试新工作流程的各个组件"""
    log.info("=" * 80)
    log.info("测试新工作流程组件")
    log.info("=" * 80)

    # 1. 测试appendix_info字段在STANDARD_FIELDS中
    log.info("1. 测试STANDARD_FIELDS包含appendix_info")
    assert (
        "appendix_info" in STANDARD_FIELDS
    ), "STANDARD_FIELDS应该包含appendix_info字段"
    log.info("✓ appendix_info字段已添加到STANDARD_FIELDS")

    # 2. 测试process_all_attachments函数
    log.info("\n2. 测试process_all_attachments函数")

    # 创建测试数据
    test_appendix = [
        {"text": "招标文件.pdf", "url": "http://test.com/tender.pdf"},
        {"text": "合同文件.docx", "url": "http://test.com/contract.docx"},
        {"text": "技术文档.pdf", "url": "http://test.com/tech.pdf"},
    ]

    def mock_download(url):
        return f"content_for_{url.split('/')[-1]}".encode("utf-8")

    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id

    def mock_get_ext(url):
        if ".pdf" in url:
            return ".pdf"
        elif ".docx" in url:
            return ".docx"
        return ""

    with patch("analyse_appendix.download_file", side_effect=mock_download), patch(
        "analyse_appendix.upload_attachment_file", side_effect=mock_upload
    ), patch("analyse_appendix.get_file_extension_from_url", side_effect=mock_get_ext):

        result, file_cache = process_all_attachments(
            appendix_list=test_appendix,
            source_id="test_doc_123",
            enable_file_upload=True,
        )

        # 验证结果
        assert len(result) == 3, f"期望3个附件，实际{len(result)}个"

        for i, attachment in enumerate(result):
            assert "url" in attachment, f"附件{i}缺少url字段"
            assert "text" in attachment, f"附件{i}缺少text字段"
            assert "file_ext" in attachment, f"附件{i}缺少file_ext字段"
            assert "file_link_key" in attachment, f"附件{i}缺少file_link_key字段"
            assert (
                attachment["file_link_key"] is not None
            ), f"附件{i}的file_link_key为空"

            log.info(
                f"  ✓ 附件{i+1}: {attachment['text']} -> {attachment['file_link_key']}"
            )

    log.info("✓ process_all_attachments函数测试通过")

    # 3. 测试禁用上传的情况
    log.info("\n3. 测试禁用文件上传的情况")

    with patch("analyse_appendix.download_file", side_effect=mock_download), patch(
        "analyse_appendix.get_file_extension_from_url", side_effect=mock_get_ext
    ):

        result_no_upload, _ = process_all_attachments(
            appendix_list=test_appendix,
            source_id="test_doc_456",
            enable_file_upload=False,
        )

        # 验证结果
        assert len(result_no_upload) == 3, f"期望3个附件，实际{len(result_no_upload)}个"

        for i, attachment in enumerate(result_no_upload):
            assert (
                attachment["file_link_key"] is None
            ), f"禁用上传时附件{i}的file_link_key应该为None"
            log.info(f"  ✓ 附件{i+1}: {attachment['text']} -> None (上传已禁用)")

    log.info("✓ 禁用上传测试通过")

    return True


def test_field_update_logic():
    """测试字段更新逻辑"""
    log.info("\n" + "=" * 80)
    log.info("测试字段更新逻辑")
    log.info("=" * 80)

    # 模拟processed_main_results
    processed_main_results = [
        {
            "source_id": "test_doc",
            "bid_doc_name": None,
            "bid_doc_ext": None,
            "bid_doc_link_out": None,
            "bid_doc_link_key": None,
            "contract_name": None,
            "contract_ext": None,
            "contract_link_out": None,
            "contract_link_key": None,
            "appendix_info": [],
        }
    ]

    # 模拟appendix_info
    appendix_info = [
        {
            "url": "http://test.com/tender.pdf",
            "text": "项目招标文件.pdf",
            "file_ext": ".pdf",
            "file_link_key": "upload_tender_123",
        },
        {
            "url": "http://test.com/contract.docx",
            "text": "中标合同文件.docx",
            "file_ext": ".docx",
            "file_link_key": "upload_contract_456",
        },
    ]

    # 测试招标文件字段更新
    log.info("1. 测试招标文件字段更新")
    appendix_url = "http://test.com/tender.pdf"
    appendix_text = "项目招标文件.pdf"
    file_ext = ".pdf"
    file_type = "招标文件"

    # 找到匹配的附件
    matching_attachment = None
    for attachment in appendix_info:
        if attachment["url"] == appendix_url:
            matching_attachment = attachment
            break

    assert matching_attachment is not None, "应该找到匹配的附件"

    # 更新字段
    for result in processed_main_results:
        result["bid_doc_name"] = appendix_text
        result["bid_doc_ext"] = file_ext
        result["bid_doc_link_out"] = appendix_url
        result["bid_doc_link_key"] = matching_attachment.get("file_link_key")

    # 验证更新
    result = processed_main_results[0]
    assert (
        result["bid_doc_name"] == "项目招标文件.pdf"
    ), "招标文件名称应该使用原始文件名"
    assert result["bid_doc_ext"] == ".pdf", "招标文件扩展名应该正确"
    assert (
        result["bid_doc_link_out"] == "http://test.com/tender.pdf"
    ), "招标文件URL应该正确"
    assert result["bid_doc_link_key"] == "upload_tender_123", "招标文件上传ID应该正确"

    log.info("✓ 招标文件字段更新成功")
    log.info(f"  bid_doc_name: {result['bid_doc_name']}")
    log.info(f"  bid_doc_ext: {result['bid_doc_ext']}")
    log.info(f"  bid_doc_link_key: {result['bid_doc_link_key']}")

    # 测试合同文件字段更新
    log.info("\n2. 测试合同文件字段更新")
    appendix_url = "http://test.com/contract.docx"
    appendix_text = "中标合同文件.docx"
    file_ext = ".docx"
    file_type = "合同文件"

    # 找到匹配的附件
    matching_attachment = None
    for attachment in appendix_info:
        if attachment["url"] == appendix_url:
            matching_attachment = attachment
            break

    assert matching_attachment is not None, "应该找到匹配的附件"

    # 更新字段
    for result in processed_main_results:
        result["contract_name"] = appendix_text
        result["contract_ext"] = file_ext
        result["contract_link_out"] = appendix_url
        result["contract_link_key"] = matching_attachment.get("file_link_key")

    # 验证更新
    assert (
        result["contract_name"] == "中标合同文件.docx"
    ), "合同文件名称应该使用原始文件名"
    assert result["contract_ext"] == ".docx", "合同文件扩展名应该正确"
    assert (
        result["contract_link_out"] == "http://test.com/contract.docx"
    ), "合同文件URL应该正确"
    assert (
        result["contract_link_key"] == "upload_contract_456"
    ), "合同文件上传ID应该正确"

    log.info("✓ 合同文件字段更新成功")
    log.info(f"  contract_name: {result['contract_name']}")
    log.info(f"  contract_ext: {result['contract_ext']}")
    log.info(f"  contract_link_key: {result['contract_link_key']}")

    return True


def test_early_exit_logic():
    """测试早期退出逻辑"""
    log.info("\n" + "=" * 80)
    log.info("测试早期退出逻辑")
    log.info("=" * 80)

    # 模拟文件类型检测
    def detect_file_type(filename, preview):
        if "招标" in filename:
            return "招标文件"
        elif "合同" in filename:
            return "合同文件"
        else:
            return "其他"

    # 测试数据：多个同类型文件
    test_files = [
        {"filename": "招标文件1.pdf", "content": "招标内容1"},
        {"filename": "招标文件2.pdf", "content": "招标内容2"},
        {"filename": "合同文件1.docx", "content": "合同内容1"},
        {"filename": "合同文件2.docx", "content": "合同内容2"},
        {"filename": "其他文件.pdf", "content": "其他内容"},
    ]

    # 模拟处理过程
    found_tender_file = False
    found_contract_file = False
    processed_files = []
    skipped_files = []

    for file_item in test_files:
        filename = file_item["filename"]
        file_type = detect_file_type(filename, "")

        # 早期退出检查
        if file_type == "招标文件" and found_tender_file:
            log.info(f"  ✓ 早期退出：跳过后续招标文件 {filename}")
            skipped_files.append(filename)
            continue
        elif file_type == "合同文件" and found_contract_file:
            log.info(f"  ✓ 早期退出：跳过后续合同文件 {filename}")
            skipped_files.append(filename)
            continue
        elif file_type == "其他":
            log.info(f"  ✓ 跳过非招标/合同文件: {filename}")
            skipped_files.append(filename)
            continue

        # 处理文件
        processed_files.append(filename)
        log.info(f"  ✓ 处理文件: {filename}")

        # 设置标志
        if file_type == "招标文件":
            found_tender_file = True
        elif file_type == "合同文件":
            found_contract_file = True

    # 验证结果
    expected_processed = ["招标文件1.pdf", "合同文件1.docx"]
    expected_skipped = ["招标文件2.pdf", "合同文件2.docx", "其他文件.pdf"]

    assert (
        processed_files == expected_processed
    ), f"期望处理{expected_processed}，实际处理{processed_files}"
    assert (
        skipped_files == expected_skipped
    ), f"期望跳过{expected_skipped}，实际跳过{skipped_files}"

    log.info("✓ 早期退出逻辑测试通过")
    log.info(f"  处理的文件: {processed_files}")
    log.info(f"  跳过的文件: {skipped_files}")

    return True


def main():
    """主测试函数"""
    log.info("开始新工作流程组件测试")
    log.info("=" * 100)

    try:
        # 运行所有测试
        test_new_workflow_components()
        test_field_update_logic()
        test_early_exit_logic()

        log.info("\n" + "=" * 100)
        log.info("🎉 所有新工作流程组件测试通过！")
        log.info("=" * 100)

        log.info("\n新工作流程实现总结:")
        log.info("✓ 添加了appendix_info字段到STANDARD_FIELDS")
        log.info("✓ 实现了process_all_attachments函数（下载和上传所有附件）")
        log.info("✓ 实现了新的字段更新逻辑（使用原始文件名）")
        log.info("✓ 保持了早期退出逻辑（仅在分析阶段应用）")
        log.info("✓ 支持文件上传开关（enable_file_upload）")

        return True

    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
