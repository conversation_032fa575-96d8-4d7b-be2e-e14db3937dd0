#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的文件上传测试
"""

from file_upload_service import upload_document_file
from utils.log_cfg import log


def test_complete_upload():
    """测试完整的文件上传流程"""
    print("=" * 60)
    print("完整文件上传测试")
    print("=" * 60)

    # 创建测试文件内容
    test_content = b"""
    This is a test bidding document.
    
    Project Name: Test Medical Equipment Procurement
    Bidder: Test Medical Company
    Amount: 1,000,000 RMB
    
    This file is used for testing the upload functionality.
    """

    source_id = "TEST_UPLOAD_001"
    file_type = "招标文件"
    file_ext = ".txt"

    print(f"测试参数:")
    print(f"  文件内容大小: {len(test_content)} 字节")
    print(f"  源文档ID: {source_id}")
    print(f"  文件类型: {file_type}")
    print(f"  文件扩展名: {file_ext}")

    expected_object_name = f"{file_type}_{source_id}{file_ext}"
    print(f"  期望的对象名: {expected_object_name}")

    print("\n开始上传...")

    try:
        success, upload_id = upload_document_file(
            test_content, source_id, file_type, file_ext
        )

        if success:
            print("✓ 文件上传成功！")
            print(f"  上传的文件名: {expected_object_name}")
            print(f"  上传ID: {upload_id}")
            return True
        else:
            print("✗ 文件上传失败")
            return False

    except Exception as e:
        print(f"✗ 上传过程中发生异常: {e}")
        return False


def test_contract_file_upload():
    """测试合同文件上传"""
    print("\n" + "=" * 60)
    print("合同文件上传测试")
    print("=" * 60)

    # 创建合同文件内容
    contract_content = b"""
    Contract Document
    
    Party A: Test Hospital
    Party B: Test Medical Equipment Company
    
    Contract Amount: 2,500,000 RMB
    Contract Date: 2025-07-02
    
    Equipment List:
    1. CT Scanner - 1 unit
    2. MRI Machine - 1 unit
    
    This is a test contract file.
    """

    source_id = "TEST_CONTRACT_001"
    file_type = "合同文件"
    file_ext = ".txt"

    print(f"测试参数:")
    print(f"  文件内容大小: {len(contract_content)} 字节")
    print(f"  源文档ID: {source_id}")
    print(f"  文件类型: {file_type}")
    print(f"  文件扩展名: {file_ext}")

    expected_object_name = f"{file_type}_{source_id}{file_ext}"
    print(f"  期望的对象名: {expected_object_name}")

    print("\n开始上传...")

    try:
        success, upload_id = upload_document_file(
            contract_content, source_id, file_type, file_ext
        )

        if success:
            print("✓ 合同文件上传成功！")
            print(f"  上传的文件名: {expected_object_name}")
            print(f"  上传ID: {upload_id}")
            return True
        else:
            print("✗ 合同文件上传失败")
            return False

    except Exception as e:
        print(f"✗ 上传过程中发生异常: {e}")
        return False


def main():
    """运行完整上传测试"""
    print("开始完整文件上传测试...")

    test_results = []

    # 运行测试
    test_results.append(("招标文件上传", test_complete_upload()))
    test_results.append(("合同文件上传", test_contract_file_upload()))

    # 输出测试结果
    print("\n" + "=" * 60)
    print("完整上传测试结果汇总")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 所有完整上传测试通过！文件上传功能完全正常。")
    else:
        print("⚠️  部分上传测试失败，请检查网络连接和服务器状态。")

    return passed == total


if __name__ == "__main__":
    main()
