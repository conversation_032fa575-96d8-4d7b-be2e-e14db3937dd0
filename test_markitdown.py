#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试markitdown功能
"""

import os
from analyse_appendix import convert_to_markdown_with_markitdown, MARKITDOWN_AVAILABLE

def test_markitdown_functionality():
    """
    测试markitdown功能
    """
    print("=" * 80)
    print("测试markitdown功能")
    print("=" * 80)
    
    print(f"markitdown可用性: {MARKITDOWN_AVAILABLE}")
    
    if not MARKITDOWN_AVAILABLE:
        print("❌ markitdown不可用，请安装: pip install markitdown")
        return
    
    # 查找测试文件
    downloads_dir = "downloads"
    test_files = []
    
    if os.path.exists(downloads_dir):
        for file in os.listdir(downloads_dir):
            if file.lower().endswith(('.pdf', '.docx', '.doc')):
                test_files.append(os.path.join(downloads_dir, file))
    
    if not test_files:
        print("❌ 未找到测试文件（PDF、DOCX、DOC）")
        return
    
    print(f"找到 {len(test_files)} 个测试文件:")
    for i, file in enumerate(test_files, 1):
        print(f"  {i}. {os.path.basename(file)}")
    
    # 测试第一个文件
    test_file = test_files[0]
    print(f"\n测试文件: {os.path.basename(test_file)}")
    
    try:
        # 读取文件
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        file_ext = os.path.splitext(test_file)[1].lower()
        print(f"文件类型: {file_ext}")
        print(f"文件大小: {len(file_content):,} 字节")
        
        # 使用markitdown转换
        print("\n开始markitdown转换...")
        markdown_result = convert_to_markdown_with_markitdown(file_content, file_ext)
        
        if markdown_result:
            print("✅ markitdown转换成功！")
            print(f"生成Markdown长度: {len(markdown_result):,} 字符")
            print(f"生成Markdown行数: {len(markdown_result.splitlines())} 行")
            
            # 显示前500字符的预览
            preview_length = min(500, len(markdown_result))
            preview = markdown_result[:preview_length]
            print(f"\nMarkdown内容预览 (前{preview_length}字符):")
            print("-" * 60)
            print(preview)
            if len(markdown_result) > preview_length:
                print("...")
            print("-" * 60)
            
            # 分析Markdown结构
            lines = markdown_result.splitlines()
            headers = [line for line in lines if line.startswith('#')]
            tables = [line for line in lines if '|' in line]
            lists = [line for line in lines if line.strip().startswith(('-', '*', '+'))]
            
            print(f"\nMarkdown结构分析:")
            print(f"  标题数量: {len(headers)}")
            print(f"  表格行数: {len(tables)}")
            print(f"  列表项数: {len(lists)}")
            
            if headers:
                print(f"  标题示例:")
                for header in headers[:3]:
                    print(f"    {header}")
                if len(headers) > 3:
                    print(f"    ... 还有 {len(headers)-3} 个标题")
            
        else:
            print("❌ markitdown转换失败，返回空内容")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def compare_with_traditional_parsing():
    """
    比较markitdown和传统解析方法的效果
    """
    print("\n" + "=" * 80)
    print("比较markitdown与传统解析方法")
    print("=" * 80)
    
    # 查找PDF文件进行比较
    downloads_dir = "downloads"
    pdf_files = []
    
    if os.path.exists(downloads_dir):
        for file in os.listdir(downloads_dir):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(downloads_dir, file))
    
    if not pdf_files:
        print("未找到PDF文件进行比较")
        return
    
    test_file = pdf_files[0]
    print(f"比较文件: {os.path.basename(test_file)}")
    
    try:
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        # 传统方法
        from analyse_appendix import parse_pdf
        traditional_result = parse_pdf(file_content)
        
        # markitdown方法
        markitdown_result = convert_to_markdown_with_markitdown(file_content, '.pdf')
        
        print(f"\n📊 结果比较:")
        print(f"传统解析:")
        print(f"  字符数: {len(traditional_result):,}")
        print(f"  行数: {len(traditional_result.splitlines())}")
        
        print(f"markitdown解析:")
        print(f"  字符数: {len(markitdown_result):,}")
        print(f"  行数: {len(markitdown_result.splitlines())}")
        
        # 内容相似度简单比较
        if traditional_result and markitdown_result:
            # 简单的相似度计算
            traditional_words = set(traditional_result.split())
            markitdown_words = set(markitdown_result.split())
            
            if traditional_words and markitdown_words:
                common_words = traditional_words.intersection(markitdown_words)
                similarity = len(common_words) / max(len(traditional_words), len(markitdown_words))
                print(f"  内容相似度: {similarity:.2%}")
        
        print(f"\n传统解析预览:")
        print(traditional_result[:200] + "..." if len(traditional_result) > 200 else traditional_result)
        
        print(f"\nmarkitdown解析预览:")
        print(markitdown_result[:200] + "..." if len(markitdown_result) > 200 else markitdown_result)
        
    except Exception as e:
        print(f"比较失败: {e}")

def main():
    """
    主函数
    """
    test_markitdown_functionality()
    compare_with_traditional_parsing()
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
