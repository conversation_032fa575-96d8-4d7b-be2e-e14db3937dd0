#!/usr/bin/env python3
"""
测试移除文件类型检查后的附件处理工作流程

验证优化内容：
1. Phase 1不进行文件类型检查，上传所有文件
2. Phase 2自动检测文件类型并尝试解析
3. 支持多种解析方法的自动尝试
"""

import os
import sys
import json
import logging
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import process_all_attachments

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def test_phase1_no_file_type_check():
    """测试Phase 1不进行文件类型检查"""
    log.info("=" * 80)
    log.info("测试Phase 1：不进行文件类型检查，上传所有文件")
    log.info("=" * 80)

    # 创建测试数据，包含各种文件类型
    test_appendix = [
        {"text": "招标文件.pdf", "url": "http://test.com/tender.pdf"},
        {"text": "合同文件.docx", "url": "http://test.com/contract.docx"},
        {"text": "Excel文件.xls", "url": "http://test.com/data.xls"},  # 之前会被跳过
        {
            "text": "未知文件",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=123",
        },  # 无扩展名
        {"text": "图片文件.jpg", "url": "http://test.com/image.jpg"},  # 不支持的类型
    ]

    def mock_download(url):
        # 模拟不同类型的文件内容
        if "tender.pdf" in url:
            return b"PDF content"
        elif "contract.docx" in url:
            return b"DOCX content"
        elif "data.xls" in url:
            return b"XLS content"
        elif "uuid=123" in url:
            return b"Unknown file content"
        elif "image.jpg" in url:
            return b"JPEG image content"
        return b"default content"

    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id

    def mock_get_ext(url):
        # 模拟从URL获取扩展名
        if ".pdf" in url:
            return ".pdf"
        elif ".docx" in url:
            return ".docx"
        elif ".xls" in url:
            return ".xls"
        elif ".jpg" in url:
            return ".jpg"
        else:
            return ""  # 无法从URL确定扩展名

    with patch("analyse_appendix.download_file", side_effect=mock_download), patch(
        "analyse_appendix.upload_attachment_file", side_effect=mock_upload
    ), patch("analyse_appendix.get_file_extension_from_url", side_effect=mock_get_ext):

        # 调用process_all_attachments
        appendix_info, file_content_cache, file_type_cache = process_all_attachments(
            appendix_list=test_appendix,
            source_id="test_doc_123",
            enable_file_upload=True,
        )

        # 验证所有文件都被处理（不再跳过任何文件）
        assert (
            len(appendix_info) == 5
        ), f"期望处理5个文件，实际处理{len(appendix_info)}个"
        log.info(f"✓ 所有文件都被处理: {len(appendix_info)} 个")

        # 验证文件内容缓存
        assert (
            len(file_content_cache) == 5
        ), f"期望缓存5个文件，实际缓存{len(file_content_cache)}个"
        log.info(f"✓ 所有文件都被缓存: {len(file_content_cache)} 个")

        # 验证每个文件的处理结果
        for i, attachment in enumerate(appendix_info):
            log.info(
                f"  文件{i+1}: {attachment['text']} - {attachment['file_ext']} - {attachment['file_link_key']}"
            )
            assert (
                attachment["file_link_key"] is not None
            ), f"文件{i+1}的upload_id不应该为空"

        # 特别验证之前会被跳过的文件
        xls_file = next(
            (item for item in appendix_info if "Excel文件" in item["text"]), None
        )
        assert xls_file is not None, "Excel文件应该被处理"
        log.info(f"✓ Excel文件已处理: {xls_file['text']}")

        unknown_file = next(
            (item for item in appendix_info if "未知文件" in item["text"]), None
        )
        assert unknown_file is not None, "未知文件应该被处理"
        assert unknown_file["file_ext"] == ".bin", "未知文件应该使用.bin扩展名"
        log.info(
            f"✓ 未知文件已处理: {unknown_file['text']} - {unknown_file['file_ext']}"
        )

        log.info("✓ Phase 1文件类型检查移除测试通过")

        return file_content_cache


def test_phase2_auto_detection():
    """测试Phase 2自动检测文件类型"""
    log.info("\n" + "=" * 80)
    log.info("测试Phase 2：自动检测文件类型并尝试解析")
    log.info("=" * 80)

    # 模拟文件内容缓存
    file_content_cache = {
        "http://test.com/doc_file": b"DOC file content",
        "http://test.com/unknown_file": b"Unknown file content",
    }

    def mock_get_file_info(content):
        # 模拟文件类型检测
        if b"DOC file content" in content:
            return {"ext": ".doc", "mime": "application/msword"}
        elif b"Unknown file content" in content:
            return {"ext": ".xls", "mime": "application/vnd.ms-excel"}  # 误检测
        return None

    def mock_parse_doc(content):
        if b"DOC file content" in content:
            return "成功解析的DOC内容"
        raise Exception("DOC解析失败")

    def mock_parse_pdf(content):
        raise Exception("PDF解析失败")

    def mock_parse_docx(content):
        if b"Unknown file content" in content:
            return "意外成功解析的内容"  # 模拟尝试多种解析方法成功
        raise Exception("DOCX解析失败")

    with patch(
        "analyse_appendix.get_file_info_from_content", side_effect=mock_get_file_info
    ), patch("analyse_appendix.parse_doc", side_effect=mock_parse_doc), patch(
        "analyse_appendix.parse_pdf", side_effect=mock_parse_pdf
    ), patch(
        "analyse_appendix.parse_docx", side_effect=mock_parse_docx
    ):

        # 测试已知文件类型的解析
        log.info("测试已知文件类型解析:")
        content1 = file_content_cache["http://test.com/doc_file"]
        file_info1 = mock_get_file_info(content1)
        detected_ext1 = file_info1.get("ext", "") if file_info1 else ""
        log.info(f"  检测到文件类型: {detected_ext1}")

        if detected_ext1 == ".doc":
            parsed_content1 = mock_parse_doc(content1)
            log.info(f"  ✓ DOC解析成功: {parsed_content1[:20]}...")

        # 测试未知文件类型的多方法尝试
        log.info("\n测试未知文件类型的多方法尝试:")
        content2 = file_content_cache["http://test.com/unknown_file"]
        file_info2 = mock_get_file_info(content2)
        detected_ext2 = file_info2.get("ext", "") if file_info2 else ""
        log.info(f"  检测到文件类型: {detected_ext2}")

        # 模拟尝试多种解析方法
        parsed_content2 = ""
        if detected_ext2 == ".xls":
            # XLS不在支持列表中，会尝试多种方法
            log.info("  XLS类型不支持，尝试多种解析方法:")
            for parse_func, ext_name in [
                (mock_parse_pdf, "PDF"),
                (mock_parse_docx, "DOCX"),
                (mock_parse_doc, "DOC"),
            ]:
                try:
                    parsed_content2 = parse_func(content2)
                    if parsed_content2 and len(parsed_content2.strip()) > 0:
                        log.info(f"    ✓ 成功使用{ext_name}解析器解析文件")
                        break
                except Exception as e:
                    log.info(f"    - {ext_name}解析失败: {e}")
                    continue

        assert parsed_content2 == "意外成功解析的内容", "应该通过DOCX解析器成功解析"
        log.info("✓ Phase 2自动检测和多方法尝试测试通过")


def test_file_type_flexibility():
    """测试文件类型处理的灵活性"""
    log.info("\n" + "=" * 80)
    log.info("测试文件类型处理的灵活性")
    log.info("=" * 80)

    # 测试各种边界情况
    test_cases = [
        {
            "name": "无扩展名URL",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=123",
            "expected_ext": ".bin",
        },
        {
            "name": "复杂URL",
            "url": "http://example.com/file.php?id=123&type=doc",
            "expected_ext": ".bin",  # 无法从URL确定
        },
        {
            "name": "正常PDF文件",
            "url": "http://example.com/document.pdf",
            "expected_ext": ".pdf",
        },
    ]

    def mock_get_ext(url):
        if ".pdf" in url and "?" not in url:
            return ".pdf"
        return ""

    for case in test_cases:
        log.info(f"\n测试用例: {case['name']}")
        log.info(f"  URL: {case['url']}")

        ext = mock_get_ext(case["url"])
        if not ext:
            ext = ".bin"  # 模拟使用通用扩展名

        log.info(f"  检测到扩展名: {ext}")
        assert ext == case["expected_ext"], f"期望{case['expected_ext']}，实际{ext}"
        log.info(f"  ✓ 扩展名处理正确")

    log.info("✓ 文件类型处理灵活性测试通过")


def main():
    """主测试函数"""
    log.info("开始测试移除文件类型检查后的工作流程")
    log.info("=" * 100)

    try:
        # 运行所有测试
        file_cache = test_phase1_no_file_type_check()
        test_phase2_auto_detection()
        test_file_type_flexibility()

        log.info("\n" + "=" * 100)
        log.info("🎉 所有文件类型检查移除测试通过！")
        log.info("=" * 100)

        log.info("\n优化总结:")
        log.info("✓ Phase 1不再进行文件类型检查")
        log.info("✓ 所有文件都会被上传（包括之前被跳过的文件）")
        log.info("✓ Phase 2自动检测文件类型")
        log.info("✓ 支持多种解析方法的自动尝试")
        log.info("✓ 无扩展名文件使用.bin通用扩展名")
        log.info("✓ 提高了文件处理的成功率")

        return True

    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
