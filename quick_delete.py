#!/usr/bin/env python3
"""
快速删除脚本 - 删除markersweb_attachment_analysis_alias索引中的旧文档

简化版本，使用默认配置快速执行删除操作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from delete_old_documents import DocumentDeleter
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def main():
    """快速执行删除操作"""
    cutoff_time = "2025-07-04 12:00:00"
    batch_size = 1000
    
    print("=" * 80)
    print("快速删除脚本")
    print("=" * 80)
    print(f"删除条件：")
    print(f"  - insert_time < {cutoff_time}")
    print(f"  - source_appendix != []")
    print(f"  - 批量大小: {batch_size}")
    print("=" * 80)
    
    # 询问用户确认
    while True:
        choice = input("请选择操作：\n1. 干运行（查看会删除的文档）\n2. 执行删除\n3. 退出\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n执行干运行模式...")
            try:
                deleter = DocumentDeleter()
                deleter.dry_run(cutoff_time)
            except Exception as e:
                log.error(f"干运行失败: {e}")
            break
            
        elif choice == "2":
            print("\n⚠️  警告：即将执行实际删除操作，此操作不可逆！")
            confirm = input("确认执行删除？(yes/no): ").strip().lower()
            
            if confirm in ['yes', 'y']:
                print("\n执行删除操作...")
                try:
                    deleter = DocumentDeleter()
                    deleter.execute_deletion(cutoff_time, batch_size)
                except Exception as e:
                    log.error(f"删除操作失败: {e}")
            else:
                print("已取消删除操作")
            break
            
        elif choice == "3":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
