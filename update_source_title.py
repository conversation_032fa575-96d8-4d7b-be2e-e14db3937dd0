#!/usr/bin/env python3
"""
补充markersweb_attachment_analysis_alias索引中缺失的source_title字段

该脚本的功能：
1. 查询markersweb_attachment_analysis_alias索引中source_title字段缺失的文档
2. 根据source_id匹配chn_ylcg索引中的_id
3. 将chn_ylcg的title字段赋值给markersweb_attachment_analysis_alias的source_title字段
"""

import argparse
import sys
from typing import List, Dict, Optional
from dotenv import load_dotenv
import os

from es_deal import init_es_client, search_documents, update_document
from utils.log_cfg import log


def get_title_from_chn_ylcg(es_client, source_id: str) -> Optional[str]:
    """
    从chn_ylcg索引中根据_id获取title字段内容

    Args:
        es_client: Elasticsearch客户端
        source_id: 文档ID

    Returns:
        title字段内容，如果未找到则返回None
    """
    try:
        # 直接通过文档ID获取文档
        response = es_client.get(
            index="chn_ylcg", id=source_id, _source=["title"]  # 只获取title字段
        )

        if response and response.get("_source"):
            return response["_source"].get("title")
        else:
            log.warning(f"未找到ID为 {source_id} 的文档")
            return None

    except Exception as e:
        log.error(f"获取文档 {source_id} 失败: {e}")
        return None


def get_documents_missing_source_title(
    es_client, index_name: str, batch_size: int = 100
) -> List[Dict]:
    """
    获取缺失source_title字段的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小

    Returns:
        缺失source_title字段的文档列表
    """
    try:
        query = {
            "query": {"bool": {"must_not": {"exists": {"field": "source_title"}}}},
            "size": batch_size,
            "_source": ["source_id"],  # 只获取source_id字段
        }

        result = search_documents(es_client, index_name, query=query)

        if result and result.get("hits", {}).get("hits"):
            documents = []
            for hit in result["hits"]["hits"]:
                doc_info = {
                    "doc_id": hit["_id"],
                    "source_id": hit["_source"].get("source_id"),
                }
                documents.append(doc_info)
            return documents
        else:
            return []

    except Exception as e:
        log.error(f"查询缺失source_title字段的文档失败: {e}")
        return []


def bulk_update_source_title(es_client, index_name: str, updates: List[Dict]):
    """
    批量更新文档，添加source_title字段

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        updates: 更新操作列表，格式为[{"doc_id": "xxx", "source_title": "xxx"}, ...]
    """
    if not updates:
        return

    try:
        # 构建批量更新请求
        body = []
        for update in updates:
            # 更新操作头
            body.append({"update": {"_index": index_name, "_id": update["doc_id"]}})
            # 更新内容
            body.append({"doc": {"source_title": update["source_title"]}})

        # 执行批量更新
        response = es_client.bulk(body=body)

        # 检查结果
        success_count = 0
        error_count = 0

        for item in response["items"]:
            if "update" in item:
                if item["update"].get("status") in [200, 201]:
                    success_count += 1
                else:
                    error_count += 1
                    log.error(f"更新失败: {item['update']}")

        log.info(f"批量更新完成 - 成功: {success_count}, 失败: {error_count}")

    except Exception as e:
        log.error(f"批量更新失败: {e}")


def get_total_missing_count(es_client, index_name: str) -> int:
    """
    获取缺失source_title字段的文档总数

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称

    Returns:
        缺失source_title字段的文档总数
    """
    try:
        query = {"query": {"bool": {"must_not": {"exists": {"field": "source_title"}}}}}

        result = es_client.count(index=index_name, body=query)
        return result.get("count", 0)

    except Exception as e:
        log.error(f"获取缺失source_title字段的文档总数失败: {e}")
        return 0


def process_missing_source_title(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理缺失source_title字段的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info(f"开始处理缺失source_title字段的文档...")
        log.info(f"索引: {index_name}")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 先获取总数统计
        total_missing = get_total_missing_count(es_client, index_name)
        log.info(f"索引中缺失source_title字段的文档总数: {total_missing}")

        if total_missing == 0:
            log.info("没有找到缺失source_title字段的文档")
            return

        # 获取缺失source_title字段的文档
        missing_docs = get_documents_missing_source_title(
            es_client, index_name, batch_size
        )

        if not missing_docs:
            log.info("没有找到缺失source_title字段的文档")
            return

        log.info(f"本批次处理 {len(missing_docs)} 个文档（总共 {total_missing} 个）")

        # 准备更新数据
        updates = []
        not_found_count = 0
        empty_source_id_count = 0

        for i, doc in enumerate(missing_docs, 1):
            doc_id = doc["doc_id"]
            source_id = doc["source_id"]

            if not source_id:
                empty_source_id_count += 1
                log.warning(
                    f"[{i}/{len(missing_docs)}] 文档 {doc_id} 的source_id为空，跳过"
                )
                continue

            # 从chn_ylcg获取title
            title = get_title_from_chn_ylcg(es_client, source_id)

            if title:
                updates.append({"doc_id": doc_id, "source_title": title})
                log.info(
                    f"[{i}/{len(missing_docs)}] 准备更新文档 {doc_id}: source_title = '{title[:50]}...'"
                )
            else:
                not_found_count += 1
                log.warning(
                    f"[{i}/{len(missing_docs)}] 未找到source_id {source_id} 对应的title，跳过文档 {doc_id}"
                )

        # 输出统计信息
        log.info("=" * 50)
        log.info("处理统计:")
        log.info(f"  本批次文档总数: {len(missing_docs)}")
        log.info(f"  准备更新的文档: {len(updates)}")
        log.info(f"  source_id为空的文档: {empty_source_id_count}")
        log.info(f"  未找到对应title的文档: {not_found_count}")
        log.info("=" * 50)

        if updates and not dry_run:
            # 执行批量更新
            bulk_update_source_title(es_client, index_name, updates)
            log.info("更新完成")
        elif dry_run:
            log.info("试运行模式，未执行实际更新")
        else:
            log.info("没有需要更新的文档")

    except Exception as e:
        log.error(f"处理过程中发生错误: {e}")


def process_all_missing_source_title(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理所有缺失source_title字段的文档（分批处理）

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info("开始处理所有缺失source_title字段的文档...")

        # 获取总数
        total_missing = get_total_missing_count(es_client, index_name)
        log.info(f"索引中缺失source_title字段的文档总数: {total_missing}")

        if total_missing == 0:
            log.info("没有找到缺失source_title字段的文档")
            return

        processed_count = 0
        batch_num = 1

        while processed_count < total_missing:
            log.info(f"\n{'='*60}")
            log.info(f"开始处理第 {batch_num} 批次")
            log.info(f"已处理: {processed_count}/{total_missing}")
            log.info(f"{'='*60}")

            # 处理一个批次
            process_missing_source_title(es_client, index_name, batch_size, dry_run)

            processed_count += batch_size
            batch_num += 1

            # 重新检查剩余数量
            remaining = get_total_missing_count(es_client, index_name)
            if remaining == 0:
                log.info("所有缺失source_title字段的文档已处理完成")
                break
            elif remaining >= total_missing:
                log.warning("剩余文档数量未减少，可能存在问题，停止处理")
                break

        log.info(f"\n处理完成，共处理 {batch_num - 1} 个批次")

    except Exception as e:
        log.error(f"处理所有文档时发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="补充markersweb_attachment_analysis_alias索引中缺失的source_title字段"
    )
    parser.add_argument("--index", type=str, help="目标索引名称（默认从环境变量获取）")
    parser.add_argument(
        "--batch-size", type=int, default=100, help="批处理大小（默认100）"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="试运行模式，不执行实际更新"
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="处理所有缺失source_title字段的文档（分批处理）",
    )

    args = parser.parse_args()

    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()

        # 确定索引名称
        if args.index:
            index_name = args.index
        else:
            index_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
            if not index_name:
                log.error("未指定索引名称，且环境变量ES_INDEX_ANALYSIS_ALIAS未设置")
                log.error(
                    "请使用 --index 参数指定索引名称，或在.env文件中设置ES_INDEX_ANALYSIS_ALIAS"
                )
                sys.exit(1)

        log.info(f"目标索引: {index_name}")
        log.info(f"批处理大小: {args.batch_size}")
        log.info(f"试运行模式: {args.dry_run}")
        log.info(f"处理所有文档: {args.all}")

        # 处理缺失source_title字段的文档
        if args.all:
            process_all_missing_source_title(
                es, index_name, args.batch_size, args.dry_run
            )
        else:
            process_missing_source_title(es, index_name, args.batch_size, args.dry_run)

    except KeyboardInterrupt:
        log.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
