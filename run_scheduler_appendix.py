import schedule
import time
from datetime import datetime
from analyse_appendix import get_one_record
from utils.log_cfg import log


def job():
    """
    执行单条记录处理任务
    """
    try:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log.info(f"开始执行定时任务... 当前时间: {current_time}")
        get_one_record()
        log.info(f"定时任务执行完成 当前时间: {current_time}")
    except Exception as e:
        log.error(f"定时任务执行失败: {e}")


def main():
    """
    主函数，设置定时任务并运行
    """
    # 设置每1秒执行一次
    schedule.every(1).seconds.do(job)

    log.info("定时任务已启动，每1秒执行一次...")
    log.info("按 Ctrl+C 可以停止程序")

    # 持续运行定时任务
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)  # 每秒检查一次是否有待执行的任务
        except KeyboardInterrupt:
            log.info("程序被用户中断")
            break
        except Exception as e:
            log.error(f"定时任务运行出错: {e}")
            time.sleep(5)  # 出错后等待5秒再继续


if __name__ == "__main__":
    main()
