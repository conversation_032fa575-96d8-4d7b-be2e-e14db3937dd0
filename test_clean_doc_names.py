#!/usr/bin/env python3
"""
测试任务3：数据库字段清洗逻辑
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from clean_doc_names import clean_doc_name_by_appendix_info


def test_case_1_bid_doc_match_appendix():
    """测试情况1：bid_doc_link_key能匹配appendix_info"""
    print("=== 测试情况1：bid_doc_link_key能匹配appendix_info ===")

    doc_data = {
        "_source": {
            "bid_doc_name": "旧的招标文件名",
            "bid_doc_ext": ".docx",
            "bid_doc_link_out": "https://example.com/file.zip",
            "bid_doc_link_key": "upload_id_123",
            "appendix_info": [
                {
                    "text": "广州医科大学附属第五医院部分后勤保障服务项目招标文件.docx",  # 包含扩展名
                    "file_link_key": "upload_id_123",
                    "url": "https://example.com/file.zip",
                },
                {
                    "text": "其他附件",
                    "file_link_key": "upload_id_456",
                    "url": "https://example.com/other.pdf",
                },
            ],
        }
    }

    updates = clean_doc_name_by_appendix_info(doc_data)

    print(f"原始bid_doc_name: {doc_data['_source']['bid_doc_name']}")
    print(
        f"匹配的appendix_info text: 广州医科大学附属第五医院部分后勤保障服务项目招标文件.docx"
    )
    print(f"更新结果: {updates}")

    expected_name = (
        "广州医科大学附属第五医院部分后勤保障服务项目招标文件"  # 不包含扩展名
    )
    if updates.get("bid_doc_name") == expected_name:
        print("✅ 测试通过：正确使用appendix_info的text并移除了扩展名")
    else:
        print("❌ 测试失败：没有正确处理扩展名")


def test_case_2_contract_match_appendix():
    """测试情况2：contract_link_key能匹配appendix_info"""
    print("\n=== 测试情况2：contract_link_key能匹配appendix_info ===")

    doc_data = {
        "_source": {
            "contract_name": "旧的合同文件名",
            "contract_ext": ".pdf",
            "contract_link_out": "https://example.com/contract.zip",
            "contract_link_key": "contract_upload_789",
            "appendix_info": [
                {
                    "text": "服务合同协议书.pdf",  # 包含扩展名
                    "file_link_key": "contract_upload_789",
                    "url": "https://example.com/contract.zip",
                }
            ],
        }
    }

    updates = clean_doc_name_by_appendix_info(doc_data)

    print(f"原始contract_name: {doc_data['_source']['contract_name']}")
    print(f"匹配的appendix_info text: 服务合同协议书.pdf")
    print(f"更新结果: {updates}")

    expected_name = "服务合同协议书"  # 不包含扩展名
    if updates.get("contract_name") == expected_name:
        print("✅ 测试通过：正确使用appendix_info的text并移除了扩展名")
    else:
        print("❌ 测试失败：没有正确处理扩展名")


def test_case_3_bid_doc_no_match_fallback():
    """测试情况3：bid_doc_link_key无法匹配，使用默认值"""
    print("\n=== 测试情况3：bid_doc_link_key无法匹配，使用默认值 ===")

    doc_data = {
        "_source": {
            "bid_doc_name": "某个具体的文件名.docx",
            "bid_doc_ext": ".docx",
            "bid_doc_link_out": "https://example.com/file.zip",
            "bid_doc_link_key": "upload_id_999",  # 这个key在appendix_info中找不到
            "appendix_info": [
                {
                    "text": "其他文件",
                    "file_link_key": "upload_id_111",
                    "url": "https://example.com/other.pdf",
                }
            ],
        }
    }

    updates = clean_doc_name_by_appendix_info(doc_data)

    print(f"原始bid_doc_name: {doc_data['_source']['bid_doc_name']}")
    print(f"bid_doc_link_key: {doc_data['_source']['bid_doc_link_key']}")
    print(f"appendix_info中的file_link_key: upload_id_111")
    print(f"更新结果: {updates}")

    if updates.get("bid_doc_name") == "招标文件":
        print("✅ 测试通过：正确使用默认值'招标文件'")
    else:
        print("❌ 测试失败：没有使用默认值'招标文件'")


def test_case_4_contract_no_match_fallback():
    """测试情况4：contract_link_key无法匹配，使用默认值"""
    print("\n=== 测试情况4：contract_link_key无法匹配，使用默认值 ===")

    doc_data = {
        "_source": {
            "contract_name": "某个具体的合同名.pdf",
            "contract_ext": ".pdf",
            "contract_link_out": "https://example.com/contract.zip",
            "contract_link_key": "contract_upload_888",  # 这个key在appendix_info中找不到
            "appendix_info": [
                {
                    "text": "其他文件",
                    "file_link_key": "upload_id_222",
                    "url": "https://example.com/other.pdf",
                }
            ],
        }
    }

    updates = clean_doc_name_by_appendix_info(doc_data)

    print(f"原始contract_name: {doc_data['_source']['contract_name']}")
    print(f"contract_link_key: {doc_data['_source']['contract_link_key']}")
    print(f"appendix_info中的file_link_key: upload_id_222")
    print(f"更新结果: {updates}")

    if updates.get("contract_name") == "合同文件":
        print("✅ 测试通过：正确使用默认值'合同文件'")
    else:
        print("❌ 测试失败：没有使用默认值'合同文件'")


def test_case_5_incomplete_fields():
    """测试情况5：字段不完整，不应该更新"""
    print("\n=== 测试情况5：字段不完整，不应该更新 ===")

    doc_data = {
        "_source": {
            "bid_doc_name": "某个文件名",
            "bid_doc_ext": None,  # 缺少ext
            "bid_doc_link_out": "https://example.com/file.zip",
            "bid_doc_link_key": "upload_id_999",
            "appendix_info": [],
        }
    }

    updates = clean_doc_name_by_appendix_info(doc_data)

    print(f"原始数据: bid_doc_ext为None（字段不完整）")
    print(f"更新结果: {updates}")

    if "bid_doc_name" not in updates:
        print("✅ 测试通过：字段不完整时不更新")
    else:
        print("❌ 测试失败：字段不完整时仍然更新了")


def test_case_6_no_changes_needed():
    """测试情况6：已经是正确的值，不需要更新"""
    print("\n=== 测试情况6：已经是正确的值，不需要更新 ===")

    doc_data = {
        "_source": {
            "bid_doc_name": "招标文件",  # 已经是默认值
            "bid_doc_ext": ".docx",
            "bid_doc_link_out": "https://example.com/file.zip",
            "bid_doc_link_key": "upload_id_999",
            "appendix_info": [],
        }
    }

    updates = clean_doc_name_by_appendix_info(doc_data)

    print(f"原始bid_doc_name: {doc_data['_source']['bid_doc_name']}")
    print(f"更新结果: {updates}")

    if "bid_doc_name" not in updates:
        print("✅ 测试通过：已经是正确值时不更新")
    else:
        print("❌ 测试失败：已经是正确值时仍然更新了")


def test_extension_removal_in_cleaning():
    """测试清洗脚本中的扩展名移除功能"""
    print("\n=== 测试清洗脚本中的扩展名移除功能 ===")

    test_cases = [
        {
            "name": "招标文件带.docx扩展名",
            "doc_data": {
                "_source": {
                    "bid_doc_name": "旧文件名",
                    "bid_doc_ext": ".docx",
                    "bid_doc_link_out": "https://example.com/file.zip",
                    "bid_doc_link_key": "upload_123",
                    "appendix_info": [
                        {
                            "text": "新疆维吾尔自治区儿童医院招标文件.docx",
                            "file_link_key": "upload_123",
                            "url": "https://example.com/file.zip",
                        }
                    ],
                }
            },
            "expected": "新疆维吾尔自治区儿童医院招标文件",
        },
        {
            "name": "合同文件带.pdf扩展名",
            "doc_data": {
                "_source": {
                    "contract_name": "旧合同名",
                    "contract_ext": ".pdf",
                    "contract_link_out": "https://example.com/contract.zip",
                    "contract_link_key": "contract_456",
                    "appendix_info": [
                        {
                            "text": "广州医科大学服务合同.pdf",
                            "file_link_key": "contract_456",
                            "url": "https://example.com/contract.zip",
                        }
                    ],
                }
            },
            "expected": "广州医科大学服务合同",
        },
        {
            "name": "文件名不带扩展名",
            "doc_data": {
                "_source": {
                    "bid_doc_name": "旧文件名",
                    "bid_doc_ext": ".doc",
                    "bid_doc_link_out": "https://example.com/file.zip",
                    "bid_doc_link_key": "upload_789",
                    "appendix_info": [
                        {
                            "text": "某医院设备采购招标文件",  # 不带扩展名
                            "file_link_key": "upload_789",
                            "url": "https://example.com/file.zip",
                        }
                    ],
                }
            },
            "expected": "某医院设备采购招标文件",
        },
    ]

    for case in test_cases:
        print(f"\n{case['name']}:")
        updates = clean_doc_name_by_appendix_info(case["doc_data"])

        # 检查bid_doc_name或contract_name的更新
        result_name = updates.get("bid_doc_name") or updates.get("contract_name")

        print(f"  期望结果: {case['expected']}")
        print(f"  实际结果: {result_name}")

        if result_name == case["expected"]:
            print(f"  ✅ 正确：扩展名已正确移除")
        else:
            print(f"  ❌ 错误：扩展名处理有问题")


def test_comprehensive_logic():
    """综合测试清洗逻辑"""
    print("\n=== 综合测试清洗逻辑 ===")

    print("清洗逻辑总结:")
    print("1. ✅ 优先使用appendix_info中匹配的text作为文件名")
    print("2. ✅ 移除文件扩展名，确保文件名字段只包含纯文件名")
    print("3. ✅ 无法匹配且四个字段都有值时，使用默认值")
    print("4. ✅ 字段不完整时不进行更新")
    print("5. ✅ 已经是正确值时不重复更新")
    print("6. ✅ 支持同时处理bid_doc_name和contract_name")

    print("\n预期效果:")
    print("- 数据库中的文件名将更加准确和一致")
    print("- 文件名不包含扩展名，避免信息重复")
    print("- 减少了无意义的文件名")
    print("- 提高了数据质量")


if __name__ == "__main__":
    test_case_1_bid_doc_match_appendix()
    test_case_2_contract_match_appendix()
    test_case_3_bid_doc_no_match_fallback()
    test_case_4_contract_no_match_fallback()
    test_case_5_incomplete_fields()
    test_case_6_no_changes_needed()
    test_extension_removal_in_cleaning()
    test_comprehensive_logic()
