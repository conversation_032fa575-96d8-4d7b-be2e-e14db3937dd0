# 公告类型001字段置空功能说明

## 🎯 功能概述

实现了一个功能，用于筛选出 `markersweb_attachment_analysis_alias` 索引中公告类型 `source_category` 等于 "001" 的数据，并将指定的11个字段设置为 `null`。

## 📋 需要置空的字段

以下11个字段将被设置为 `null`：

```
1. bidder_price                    # 中标金额
2. bidder_name                     # 中标单位名称
3. bidder_contact_person           # 中标单位联系人
4. bidder_contact_phone_number     # 中标单位联系人电话
5. bidder_contract_config_param    # 中标合同配置参数
6. bid_cancelled_flag              # 标段是否废标标记
7. bid_cancelled_reason            # 标段废标原因
8. contract_name                   # 合同文件名称
9. contract_ext                    # 合同文件扩展名
10. contract_link_out              # 合同文件外部链接
11. contract_link_key              # 合同文件上传ID
```

## 🔧 实现方式

### 1. 核心函数

在 `es_deal.py` 中添加了 `nullify_fields_for_announcement_001()` 函数：

```python
def nullify_fields_for_announcement_001(
    es: Elasticsearch, 
    index_name: str,
    batch_size: int = 1000,
    dry_run: bool = False
) -> dict:
```

**参数说明：**
- `es`: Elasticsearch客户端实例
- `index_name`: 索引名称（通常是 `markersweb_attachment_analysis_alias`）
- `batch_size`: 批处理大小，默认1000
- `dry_run`: 试运行模式，`True` 时只统计不实际更新

**返回值：**
包含处理统计信息的字典，包括：
- `total_found`: 找到的文档总数
- `total_processed`: 处理的文档总数
- `total_updated`: 更新的文档总数
- `total_errors`: 错误的文档总数
- `fields_nullified`: 设置为null的字段列表

### 2. 处理流程

1. **查询筛选**：使用 `term` 查询筛选 `source_category` 为 "001" 的文档
2. **批量处理**：使用 `scroll` API 处理大量数据，避免内存溢出
3. **智能更新**：只更新实际需要修改的文档（字段值不为 `null` 的）
4. **批量操作**：使用 `bulk` API 提高更新效率
5. **错误处理**：记录和统计更新失败的文档

## 🚀 使用方法

### 方法1：使用独立脚本（推荐）

#### 试运行模式（安全）
```bash
python nullify_announcement_001_fields.py --dry-run
```

#### 实际更新模式
```bash
python nullify_announcement_001_fields.py
```

#### 指定参数
```bash
# 指定批处理大小
python nullify_announcement_001_fields.py --batch-size 500

# 指定索引名称
python nullify_announcement_001_fields.py --index custom_index_name

# 跳过确认提示
python nullify_announcement_001_fields.py --confirm
```

### 方法2：直接调用函数

```python
from es_deal import init_es_client, nullify_fields_for_announcement_001

# 初始化ES客户端
es = init_es_client()

# 试运行
stats = nullify_fields_for_announcement_001(
    es=es,
    index_name="markersweb_attachment_analysis_alias",
    batch_size=1000,
    dry_run=True
)

# 实际更新
stats = nullify_fields_for_announcement_001(
    es=es,
    index_name="markersweb_attachment_analysis_alias",
    batch_size=1000,
    dry_run=False
)
```

### 方法3：使用测试脚本

```bash
python test_nullify_fields.py
```

测试脚本会：
1. 查询公告类型为"001"的文档数量
2. 显示前5个文档的字段情况
3. 执行试运行
4. 询问是否执行实际更新
5. 验证更新结果

## ⚠️ 安全特性

### 1. 试运行模式
- 使用 `--dry-run` 参数可以安全地预览操作结果
- 不会修改任何实际数据
- 提供详细的统计信息

### 2. 确认机制
- 实际更新前会显示详细的操作信息
- 需要用户明确输入 'yes' 确认
- 可使用 `--confirm` 参数跳过确认（适用于自动化脚本）

### 3. 批量处理
- 使用 `scroll` API 避免内存溢出
- 支持自定义批处理大小
- 自动清理 scroll 上下文

### 4. 错误处理
- 详细的错误日志记录
- 统计更新失败的文档数量
- 异常情况下自动清理资源

## 📊 输出示例

### 试运行输出
```
2025-01-01 12:00:00 | INFO | 开始处理索引 markersweb_attachment_analysis_alias 中公告类型为'001'的数据
2025-01-01 12:00:00 | INFO | 找到 1250 个公告类型为'001'的文档
2025-01-01 12:00:00 | INFO | 试运行统计: 总共找到 1250 个文档，需要更新 856 个文档

============================================================
处理完成统计:
- 找到的文档总数: 1250
- 处理的文档总数: 1250
- 更新的文档总数: 856
- 错误的文档总数: 0
- 设置为null的字段: 11 个
============================================================
```

### 实际更新输出
```
2025-01-01 12:00:00 | INFO | 开始实际更新数据...
2025-01-01 12:00:00 | INFO | 批量更新完成，本批处理 500 个文档
2025-01-01 12:00:00 | INFO | 批量更新完成，本批处理 356 个文档

============================================================
🎉 处理完成！
============================================================
找到的文档总数: 1250
处理的文档总数: 1250
更新的文档总数: 856
错误的文档总数: 0
设置为null的字段数: 11

✅ 已成功更新 856 个文档
============================================================
```

## 🔍 技术特点

1. **高效查询**：使用 `term` 查询精确匹配公告类型
2. **内存友好**：使用 `scroll` API 处理大数据集
3. **批量操作**：使用 `bulk` API 提高更新性能
4. **智能过滤**：只更新实际需要修改的文档
5. **完整日志**：详细的操作日志和统计信息
6. **安全可靠**：试运行模式和确认机制

## 📝 注意事项

1. **备份数据**：在执行实际更新前，建议备份重要数据
2. **测试环境**：建议先在测试环境验证功能
3. **网络稳定**：大批量操作需要稳定的网络连接
4. **权限确认**：确保ES用户具有更新索引的权限
5. **索引名称**：确认目标索引名称正确

## 🎉 总结

该功能完整实现了您的需求：
- ✅ 筛选公告类型为"001"的数据
- ✅ 将11个指定字段设置为null
- ✅ 支持批量处理和试运行
- ✅ 提供完整的统计和日志
- ✅ 具备安全保护机制

可以安全、高效地处理大量数据，确保招标公告(001)类型的文档不包含中标和合同相关信息。
