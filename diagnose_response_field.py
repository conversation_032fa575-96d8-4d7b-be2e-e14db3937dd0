#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断chn_ylcg索引中response字段的情况
"""

import os
from es_deal import init_es_client
from dotenv import load_dotenv
from utils.log_cfg import log


def check_response_field_status(es_client):
    """
    检查chn_ylcg索引中response字段的状态
    """
    try:
        log.info("检查chn_ylcg索引中response字段的状态...")

        # 1. 检查有response字段的文档数量
        query_with_response = {"query": {"exists": {"field": "response"}}}

        with_response_count = es_client.count(
            index="chn_ylcg", body=query_with_response
        )["count"]

        # 2. 检查response字段非空的文档数量
        query_non_empty_response = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "response"}},
                        {"bool": {"must_not": [{"term": {"response": ""}}]}},
                    ]
                }
            }
        }

        non_empty_response_count = es_client.count(
            index="chn_ylcg", body=query_non_empty_response
        )["count"]

        # 3. 总文档数
        total_count = es_client.count(index="chn_ylcg")["count"]

        log.info(f"chn_ylcg索引统计:")
        log.info(f"  总文档数: {total_count}")
        log.info(f"  有response字段的文档数: {with_response_count}")
        log.info(f"  response字段非空的文档数: {non_empty_response_count}")
        log.info(f"  response字段存在率: {with_response_count/total_count*100:.2f}%")
        log.info(
            f"  response字段非空率: {non_empty_response_count/total_count*100:.2f}%"
        )

        return with_response_count, non_empty_response_count, total_count

    except Exception as e:
        log.error(f"检查response字段状态失败: {e}")
        raise


def check_specific_documents(es_client):
    """
    检查特定文档的response字段内容
    """
    try:
        log.info("检查特定文档的response字段内容...")

        # 从markersweb_attachment_analysis_alias获取一些source_id
        analysis_query = {
            "query": {"match_all": {}},
            "_source": ["source_id"],
            "size": 5,
        }

        analysis_response = es_client.search(
            index="markersweb_attachment_analysis_alias", body=analysis_query
        )

        source_ids = []
        for hit in analysis_response.get("hits", {}).get("hits", []):
            source_id = hit["_source"].get("source_id")
            if source_id:
                source_ids.append(source_id)

        log.info(f"检查以下source_id的response字段:")
        for source_id in source_ids:
            try:
                doc = es_client.get(
                    index="chn_ylcg", id=source_id, _source=["response", "title", "url"]
                )

                if doc and doc.get("_source"):
                    source = doc["_source"]
                    response_content = source.get("response", "")
                    title = source.get("title", "N/A")
                    url = source.get("url", "N/A")

                    log.info(f"source_id: {source_id}")
                    log.info(f"  title: {title[:100]}...")
                    log.info(f"  url: {url}")
                    log.info(
                        f"  response字段存在: {'是' if 'response' in source else '否'}"
                    )
                    log.info(
                        f"  response字段长度: {len(response_content) if response_content else 0}"
                    )
                    log.info(
                        f"  response前100字符: {response_content[:100] if response_content else 'N/A'}"
                    )
                    log.info("---")
                else:
                    log.warning(f"未找到文档: {source_id}")

            except Exception as e:
                log.error(f"检查文档 {source_id} 失败: {e}")

    except Exception as e:
        log.error(f"检查特定文档失败: {e}")


def test_batch_get_operation(es_client):
    """
    测试批量获取操作
    """
    try:
        log.info("测试批量获取操作...")

        # 获取一些source_id进行测试
        analysis_query = {
            "query": {"match_all": {}},
            "_source": ["source_id"],
            "size": 3,
        }

        analysis_response = es_client.search(
            index="markersweb_attachment_analysis_alias", body=analysis_query
        )

        source_ids = []
        for hit in analysis_response.get("hits", {}).get("hits", []):
            source_id = hit["_source"].get("source_id")
            if source_id:
                source_ids.append(source_id)

        if not source_ids:
            log.warning("没有找到source_id进行测试")
            return

        log.info(f"测试批量获取这些source_id: {source_ids}")

        # 使用mget批量获取
        mget_body = {
            "docs": [
                {"_index": "chn_ylcg", "_id": source_id, "_source": ["response"]}
                for source_id in source_ids
            ]
        }

        response = es_client.mget(body=mget_body)

        log.info("批量获取结果:")
        success_count = 0
        for doc in response.get("docs", []):
            source_id = doc.get("_id", "unknown")
            found = doc.get("found", False)

            if found and doc.get("_source"):
                response_content = doc["_source"].get("response")
                if response_content:
                    success_count += 1
                    log.info(f"✓ {source_id}: response长度={len(response_content)}")
                else:
                    log.warning(f"✗ {source_id}: response字段为空")
            else:
                log.error(f"✗ {source_id}: 文档未找到")

        log.info(
            f"批量获取成功率: {success_count}/{len(source_ids)} ({success_count/len(source_ids)*100:.1f}%)"
        )

    except Exception as e:
        log.error(f"测试批量获取操作失败: {e}")


def main():
    """
    主函数
    """
    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        es_client = init_es_client()

        log.info("=" * 80)
        log.info("开始诊断response字段情况")
        log.info("=" * 80)

        # 检查response字段状态
        check_response_field_status(es_client)

        log.info("=" * 80)

        # 检查特定文档
        check_specific_documents(es_client)

        log.info("=" * 80)

        # 测试批量获取操作
        test_batch_get_operation(es_client)

        log.info("=" * 80)
        log.info("诊断完成")

    except Exception as e:
        log.error(f"诊断失败: {e}")
        raise


if __name__ == "__main__":
    main()
