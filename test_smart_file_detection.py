#!/usr/bin/env python3
"""
测试智能文件识别功能

验证优化内容：
1. 从文件内容智能识别文件类型
2. 更新文件名包含正确的扩展名
3. 下载到指定目录进行进一步分析
4. 使用实际文件名进行上传
"""

import os
import sys
import json
import logging
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import process_all_attachments

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def test_smart_file_detection():
    """测试智能文件识别功能"""
    log.info("=" * 80)
    log.info("测试智能文件识别功能")
    log.info("=" * 80)

    # 创建测试数据 - 模拟无扩展名的URL
    test_appendix = [
        {
            "text": "点击下载",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=015cc8a6-3763-4fe2-8dea-13ee25",
        },
        {
            "text": "招标文件",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=123456",
        },
        {"text": "合同文档", "url": "http://example.com/file.php?id=789"},
    ]

    def mock_download(url):
        # 模拟不同类型的文件内容
        if "015cc8a6" in url:
            # 模拟PDF文件内容
            return b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nPDF content here..."
        elif "123456" in url:
            # 模拟DOC文件内容
            return b"\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1DOC file content here..."
        elif "789" in url:
            # 模拟DOCX文件内容
            return b"PK\x03\x04DOCX file content here..."
        return b"unknown content"

    def mock_get_file_info(content):
        # 模拟文件类型检测
        if content.startswith(b"%PDF"):
            return {"ext": ".pdf", "mime": "application/pdf"}
        elif content.startswith(b"\xd0\xcf\x11\xe0"):
            return {"ext": ".doc", "mime": "application/msword"}
        elif content.startswith(b"PK\x03\x04"):
            return {
                "ext": ".docx",
                "mime": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            }
        return None

    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id

    def mock_get_ext_from_url(url):
        # 模拟从URL无法获取扩展名
        return ""

    # 创建临时目录用于测试
    test_source_id = "test_doc_123"
    downloads_dir = f"./downloads/{test_source_id}"

    # 确保测试前目录不存在
    if os.path.exists(downloads_dir):
        shutil.rmtree(downloads_dir)

    try:
        with patch("analyse_appendix.download_file", side_effect=mock_download), patch(
            "analyse_appendix.get_file_info_from_content",
            side_effect=mock_get_file_info,
        ), patch(
            "analyse_appendix.upload_attachment_file", side_effect=mock_upload
        ), patch(
            "analyse_appendix.get_file_extension_from_url",
            side_effect=mock_get_ext_from_url,
        ):

            # 调用process_all_attachments
            appendix_info, file_content_cache, file_type_cache = (
                process_all_attachments(
                    appendix_list=test_appendix,
                    source_id=test_source_id,
                    enable_file_upload=True,
                )
            )

            # 验证结果
            assert (
                len(appendix_info) == 3
            ), f"期望处理3个文件，实际处理{len(appendix_info)}个"
            log.info(f"✓ 所有文件都被处理: {len(appendix_info)} 个")

            # 验证文件名和类型识别
            expected_results = [
                {"original": "点击下载", "expected": "点击下载.pdf", "ext": ".pdf"},
                {"original": "招标文件", "expected": "招标文件.doc", "ext": ".doc"},
                {"original": "合同文档", "expected": "合同文档.docx", "ext": ".docx"},
            ]

            for i, (attachment, expected) in enumerate(
                zip(appendix_info, expected_results)
            ):
                log.info(f"\n文件{i+1}验证:")
                log.info(f"  原始名称: {expected['original']}")
                log.info(f"  实际结果: {attachment['text']}")
                log.info(f"  期望结果: {expected['expected']}")
                log.info(f"  文件类型: {attachment['file_ext']}")
                log.info(f"  上传ID: {attachment['file_link_key']}")

                assert (
                    attachment["text"] == expected["expected"]
                ), f"文件{i+1}名称不正确: 期望{expected['expected']}, 实际{attachment['text']}"
                assert (
                    attachment["file_ext"] == expected["ext"]
                ), f"文件{i+1}类型不正确: 期望{expected['ext']}, 实际{attachment['file_ext']}"
                assert (
                    attachment["file_link_key"] is not None
                ), f"文件{i+1}的upload_id不应该为空"

                log.info(f"  ✓ 文件{i+1}验证通过")

            # 验证downloads目录是否创建
            if os.path.exists(downloads_dir):
                log.info(f"✓ Downloads目录已创建: {downloads_dir}")
                # 列出目录内容
                files = os.listdir(downloads_dir)
                log.info(f"  目录内容: {files}")
            else:
                log.info("ℹ Downloads目录未创建（可能文件类型识别成功，无需本地分析）")

            log.info("✓ 智能文件识别测试通过")

            return appendix_info

    finally:
        # 清理测试目录
        if os.path.exists(downloads_dir):
            shutil.rmtree(downloads_dir)
            log.info(f"✓ 清理测试目录: {downloads_dir}")


def test_fallback_to_local_analysis():
    """测试回退到本地分析的情况"""
    log.info("\n" + "=" * 80)
    log.info("测试回退到本地分析的情况")
    log.info("=" * 80)

    # 创建测试数据 - 模拟无法从内容识别类型的文件
    test_appendix = [
        {
            "text": "神秘文件",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=mystery",
        }
    ]

    def mock_download(url):
        # 模拟无法识别的文件内容
        return b"mysterious binary content that cannot be identified"

    def mock_get_file_info_first_call(content):
        # 第一次调用返回None（无法识别）
        return None

    def mock_get_file_info_second_call(content):
        # 第二次调用（本地分析）返回识别结果
        return {"ext": ".pdf", "mime": "application/pdf"}

    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id

    def mock_get_ext_from_url(url):
        return ""

    test_source_id = "test_mystery_123"
    downloads_dir = f"./downloads/{test_source_id}"

    # 确保测试前目录不存在
    if os.path.exists(downloads_dir):
        shutil.rmtree(downloads_dir)

    try:
        # 模拟两次不同的get_file_info_from_content调用
        call_count = 0

        def mock_get_file_info_dynamic(content):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return mock_get_file_info_first_call(content)
            else:
                return mock_get_file_info_second_call(content)

        with patch("analyse_appendix.download_file", side_effect=mock_download), patch(
            "analyse_appendix.get_file_info_from_content",
            side_effect=mock_get_file_info_dynamic,
        ), patch(
            "analyse_appendix.upload_attachment_file", side_effect=mock_upload
        ), patch(
            "analyse_appendix.get_file_extension_from_url",
            side_effect=mock_get_ext_from_url,
        ):

            # 调用process_all_attachments
            appendix_info, file_content_cache, file_type_cache = (
                process_all_attachments(
                    appendix_list=test_appendix,
                    source_id=test_source_id,
                    enable_file_upload=True,
                )
            )

            # 验证结果
            assert (
                len(appendix_info) == 1
            ), f"期望处理1个文件，实际处理{len(appendix_info)}个"

            attachment = appendix_info[0]
            log.info(f"处理结果:")
            log.info(f"  文件名: {attachment['text']}")
            log.info(f"  文件类型: {attachment['file_ext']}")
            log.info(f"  上传ID: {attachment['file_link_key']}")

            # 验证是否通过本地分析识别出了PDF类型
            expected_filename = "神秘文件.pdf"
            assert (
                attachment["text"] == expected_filename
            ), f"期望文件名: {expected_filename}, 实际: {attachment['text']}"
            assert (
                attachment["file_ext"] == ".pdf"
            ), f"期望文件类型: .pdf, 实际: {attachment['file_ext']}"

            log.info("✓ 本地分析回退机制测试通过")

    finally:
        # 清理测试目录
        if os.path.exists(downloads_dir):
            shutil.rmtree(downloads_dir)
            log.info(f"✓ 清理测试目录: {downloads_dir}")


def test_ultimate_fallback():
    """测试最终兜底方案"""
    log.info("\n" + "=" * 80)
    log.info("测试最终兜底方案")
    log.info("=" * 80)

    # 创建测试数据 - 完全无法识别的文件
    test_appendix = [
        {
            "text": "完全未知文件",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=unknown",
        }
    ]

    def mock_download(url):
        return b"completely unknown binary content"

    def mock_get_file_info(content):
        # 始终返回None（无法识别）
        return None

    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id

    def mock_get_ext_from_url(url):
        return ""

    test_source_id = "test_unknown_123"
    downloads_dir = f"./downloads/{test_source_id}"

    # 确保测试前目录不存在
    if os.path.exists(downloads_dir):
        shutil.rmtree(downloads_dir)

    try:
        with patch("analyse_appendix.download_file", side_effect=mock_download), patch(
            "analyse_appendix.get_file_info_from_content",
            side_effect=mock_get_file_info,
        ), patch(
            "analyse_appendix.upload_attachment_file", side_effect=mock_upload
        ), patch(
            "analyse_appendix.get_file_extension_from_url",
            side_effect=mock_get_ext_from_url,
        ):

            # 调用process_all_attachments
            appendix_info, file_content_cache, file_type_cache = (
                process_all_attachments(
                    appendix_list=test_appendix,
                    source_id=test_source_id,
                    enable_file_upload=True,
                )
            )

            # 验证结果
            assert (
                len(appendix_info) == 1
            ), f"期望处理1个文件，实际处理{len(appendix_info)}个"

            attachment = appendix_info[0]
            log.info(f"兜底方案结果:")
            log.info(f"  文件名: {attachment['text']}")
            log.info(f"  文件类型: {attachment['file_ext']}")
            log.info(f"  上传ID: {attachment['file_link_key']}")

            # 验证使用了兜底方案
            assert (
                attachment["file_ext"] == ".bin"
            ), f"期望使用兜底扩展名.bin, 实际: {attachment['file_ext']}"
            assert (
                attachment["text"] == "完全未知文件"
            ), f"期望保持原始文件名, 实际: {attachment['text']}"

            log.info("✓ 最终兜底方案测试通过")

    finally:
        # 清理测试目录
        if os.path.exists(downloads_dir):
            shutil.rmtree(downloads_dir)


def main():
    """主测试函数"""
    log.info("开始智能文件识别功能测试")
    log.info("=" * 100)

    try:
        # 运行所有测试
        test_smart_file_detection()
        test_fallback_to_local_analysis()
        test_ultimate_fallback()

        log.info("\n" + "=" * 100)
        log.info("🎉 所有智能文件识别测试通过！")
        log.info("=" * 100)

        log.info("\n智能文件识别功能总结:")
        log.info("✓ 从文件内容自动识别文件类型")
        log.info("✓ 智能更新文件名包含正确扩展名")
        log.info("✓ 支持本地下载进行进一步分析")
        log.info("✓ 使用实际文件名进行上传")
        log.info("✓ 完善的兜底机制确保所有文件都被处理")
        log.info("✓ 下载目录结构: ./downloads/{source_id}/")

        return True

    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
