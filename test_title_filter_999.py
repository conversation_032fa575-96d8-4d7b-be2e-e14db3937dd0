#!/usr/bin/env python3
"""
测试 analyse_appendix.py 中新增的公告类型999标题内容过滤功能

该脚本用于验证：
1. contains_contract_keywords 函数对标题内容的检查是否正确
2. 标题过滤逻辑是否按预期工作
3. 各种标题内容的处理情况
"""

import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import contains_contract_keywords, CONTRACT_KEYWORDS
from utils.log_cfg import log


def test_title_contract_keywords():
    """测试标题中的合同关键词检查"""
    log.info("测试标题中的合同关键词检查...")
    
    test_cases = [
        # (title, expected_result, description)
        ("绍兴市口腔医院医疗设备采购合同公告", True, "包含'合同'关键词的标题"),
        ("某某医院设备维护服务协议公告", True, "包含'服务协议'关键词的标题"),
        ("医疗设备采购合同签订公告", True, "包含'合同'关键词（中间位置）"),
        ("设备维护服务协议书公告", True, "包含'服务协议'关键词"),
        ("合同公告-医疗设备采购项目", True, "包含'合同'关键词（开头）"),
        ("医疗设备采购项目服务协议", True, "包含'服务协议'关键词（结尾）"),
        
        # 不包含合同关键词的标题
        ("绍兴市口腔医院医疗设备采购招标公告", False, "招标公告（不包含合同关键词）"),
        ("医疗设备采购中标公告", False, "中标公告（不包含合同关键词）"),
        ("设备采购项目磋商公告", False, "磋商公告（不包含合同关键词）"),
        ("医疗器械采购谈判公告", False, "谈判公告（不包含合同关键词）"),
        ("设备技术参数公示", False, "技术公示（不包含合同关键词）"),
        ("采购需求公告", False, "需求公告（不包含合同关键词）"),
        ("供应商资格预审公告", False, "资格预审（不包含合同关键词）"),
        
        # 边界情况
        ("", False, "空标题"),
        (None, False, "None标题"),
        ("合 同公告", False, "关键词被分割"),
        ("服务 协议公告", False, "关键词被分割"),
        ("合同服务协议双重公告", True, "包含多个关键词"),
        ("HETONG公告", False, "拼音不匹配"),
        ("contract agreement", False, "英文不匹配"),
        
        # 复杂标题
        ("绍兴市口腔医院关于医疗设备采购合同签订及履行情况的公告", True, "复杂合同公告"),
        ("某某医院设备维护服务协议变更公告", True, "复杂服务协议公告"),
        ("医疗设备采购项目招标文件澄清公告", False, "复杂招标公告（不包含合同关键词）"),
    ]
    
    success_count = 0
    for i, (title, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_keywords(title)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    标题: '{title}'")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"标题合同关键词测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_real_world_titles():
    """测试真实世界的标题例子"""
    log.info("测试真实世界的标题例子...")
    
    # 应该通过过滤的标题（包含合同关键词）
    contract_titles = [
        "绍兴市口腔医院医疗设备采购合同公告",
        "某某医院关于设备维护服务协议的公告",
        "医疗器械采购合同签订公告",
        "设备保修服务协议书公告",
        "政府采购合同履行情况公告",
        "服务协议变更公告",
        "采购合同终止公告",
        "维护服务协议续签公告",
    ]
    
    # 应该被过滤掉的标题（不包含合同关键词）
    non_contract_titles = [
        "绍兴市口腔医院医疗设备采购招标公告",
        "医疗设备采购中标结果公告",
        "设备采购项目磋商公告",
        "医疗器械采购谈判公告",
        "政府采购需求公告",
        "供应商资格预审公告",
        "采购项目废标公告",
        "招标文件澄清公告",
        "评标结果公示",
        "采购计划公告",
    ]
    
    success_count = 0
    total_tests = len(contract_titles) + len(non_contract_titles)
    
    # 测试应该通过的标题
    for i, title in enumerate(contract_titles, 1):
        try:
            result = contains_contract_keywords(title)
            if result:
                log.info(f"  ✓ 合同标题 {i}: 通过过滤 - '{title[:30]}...'")
                success_count += 1
            else:
                log.error(f"  ✗ 合同标题 {i}: 应该通过但被过滤 - '{title}'")
        except Exception as e:
            log.error(f"  ✗ 合同标题 {i}: 异常 - {e}")
    
    # 测试应该被过滤的标题
    for i, title in enumerate(non_contract_titles, 1):
        try:
            result = contains_contract_keywords(title)
            if not result:
                log.info(f"  ✓ 非合同标题 {i}: 正确过滤 - '{title[:30]}...'")
                success_count += 1
            else:
                log.error(f"  ✗ 非合同标题 {i}: 应该被过滤但通过 - '{title}'")
        except Exception as e:
            log.error(f"  ✗ 非合同标题 {i}: 异常 - {e}")
    
    log.info(f"真实世界标题测试完成: {success_count}/{total_tests} 通过")
    return success_count == total_tests


def test_edge_cases():
    """测试边界情况"""
    log.info("测试边界情况...")
    
    test_cases = [
        # (title, expected_result, description)
        ("合同", True, "仅包含'合同'关键词"),
        ("服务协议", True, "仅包含'服务协议'关键词"),
        ("这是一个合同", True, "简单句子包含关键词"),
        ("关于服务协议的说明", True, "简单句子包含关键词"),
        ("合同合同合同", True, "重复关键词"),
        ("服务协议服务协议", True, "重复关键词"),
        ("合同和服务协议", True, "包含两个关键词"),
        
        # 近似但不匹配的情况
        ("合约", False, "近似词汇但不匹配"),
        ("协议", False, "部分匹配但不完整"),
        ("服务", False, "部分匹配但不完整"),
        ("合作协议", False, "包含'协议'但不是'服务协议'"),
        ("购买合约", False, "包含'合'但不是'合同'"),
        
        # 特殊字符和格式
        ("【合同】公告", True, "包含特殊字符的合同"),
        ("《服务协议》公告", True, "包含特殊字符的服务协议"),
        ("合同-公告", True, "包含连字符"),
        ("服务协议_公告", True, "包含下划线"),
        ("合同\n公告", True, "包含换行符"),
        ("服务协议\t公告", True, "包含制表符"),
    ]
    
    success_count = 0
    for i, (title, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_keywords(title)
            
            if result == expected:
                log.info(f"  ✓ 边界测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 边界测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    标题: '{repr(title)}'")
        except Exception as e:
            log.error(f"  ✗ 边界测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"边界情况测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_constants():
    """测试常量定义"""
    log.info("测试常量定义...")
    
    expected_contract_keywords = ["合同", "服务协议"]
    
    if CONTRACT_KEYWORDS == expected_contract_keywords:
        log.info(f"✓ CONTRACT_KEYWORDS 常量正确: {CONTRACT_KEYWORDS}")
        return True
    else:
        log.error(f"✗ CONTRACT_KEYWORDS 常量错误: 期望 {expected_contract_keywords}, 实际 {CONTRACT_KEYWORDS}")
        return False


def main():
    """主函数"""
    try:
        log.info("开始测试公告类型999标题内容过滤功能...")
        log.info("=" * 60)
        
        all_tests_passed = True
        
        # 1. 测试常量定义
        if not test_constants():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 2. 测试标题合同关键词检查
        if not test_title_contract_keywords():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 3. 测试真实世界的标题
        if not test_real_world_titles():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 4. 测试边界情况
        if not test_edge_cases():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        if all_tests_passed:
            log.info("✓ 所有测试通过！公告类型999标题内容过滤功能正常工作。")
        else:
            log.error("✗ 部分测试失败，请检查代码实现。")
            sys.exit(1)
        
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
