#!/usr/bin/env python3
"""
测试智能融合分析修复
"""

import json


def test_extract_fields_from_content_fix():
    """测试extract_fields_from_content函数对list返回值的处理"""
    print("=== 测试extract_fields_from_content修复 ===")
    
    # 模拟LLM返回list的情况
    def mock_llm_returns_list():
        return '[{"field1": "value1", "field2": "value2"}]'
    
    def mock_llm_returns_dict():
        return '{"field1": "value1", "field2": "value2"}'
    
    def mock_llm_returns_empty_list():
        return '[]'
    
    # 模拟clean_json_data函数
    def clean_json_data(data):
        return data
    
    # 测试list返回值的处理
    print("\n1. 测试LLM返回list的情况:")
    result1 = mock_llm_returns_list()
    cleaned_result1 = clean_json_data(result1)
    extracted_data1 = json.loads(cleaned_result1)
    
    print(f"原始返回: {result1}")
    print(f"解析结果类型: {type(extracted_data1)}")
    
    if isinstance(extracted_data1, list):
        if len(extracted_data1) > 0 and isinstance(extracted_data1[0], dict):
            extracted_data1 = extracted_data1[0]
            print("✓ 成功处理list，取第一个dict")
        else:
            print("✗ list为空或格式无效")
            extracted_data1 = {}
    
    print(f"最终结果: {extracted_data1}")
    
    # 测试dict返回值的处理
    print("\n2. 测试LLM返回dict的情况:")
    result2 = mock_llm_returns_dict()
    cleaned_result2 = clean_json_data(result2)
    extracted_data2 = json.loads(cleaned_result2)
    
    print(f"原始返回: {result2}")
    print(f"解析结果类型: {type(extracted_data2)}")
    print(f"最终结果: {extracted_data2}")
    
    # 测试空list返回值的处理
    print("\n3. 测试LLM返回空list的情况:")
    result3 = mock_llm_returns_empty_list()
    cleaned_result3 = clean_json_data(result3)
    extracted_data3 = json.loads(cleaned_result3)
    
    print(f"原始返回: {result3}")
    print(f"解析结果类型: {type(extracted_data3)}")
    
    if isinstance(extracted_data3, list):
        if len(extracted_data3) > 0 and isinstance(extracted_data3[0], dict):
            extracted_data3 = extracted_data3[0]
            print("✓ 成功处理list，取第一个dict")
        else:
            print("✓ 空list，返回空dict")
            extracted_data3 = {}
    
    print(f"最终结果: {extracted_data3}")


def test_intelligent_merge_logic():
    """测试智能融合分析的LLM调用逻辑"""
    print("\n=== 测试智能融合分析LLM调用逻辑 ===")
    
    # 模拟数据
    current_object_name = "保安服务"
    tender_list = [{"object_name": "后勤保障服务", "field1": "value1"}]
    contract_list = []
    
    # 模拟find_matching_by_object_name函数
    def find_matching_by_object_name(target_name, source_list):
        for item in source_list:
            if item.get("object_name") == target_name:
                return item
        return {}
    
    tender_info = find_matching_by_object_name(current_object_name, tender_list)
    contract_info = find_matching_by_object_name(current_object_name, contract_list)
    
    print(f"当前object_name: {current_object_name}")
    print(f"招标文件列表: {tender_list}")
    print(f"匹配的招标文件信息: {tender_info}")
    print(f"tender_info是否为空: {not tender_info}")
    print(f"tender_list是否存在: {bool(tender_list)}")
    
    # 模拟修复后的逻辑
    remaining_tender_fields = ["field2", "field3"]
    tender_content = "模拟招标文件内容"
    model_apikey = "test_key"
    
    print(f"\n剩余需要提取的字段: {remaining_tender_fields}")
    
    if remaining_tender_fields and tender_content and model_apikey:
        # 修复后的逻辑：检查tender_info或tender_list
        if tender_info or tender_list:
            print("✓ 招标文件已解析，跳过LLM重新提取")
            if not tender_info and tender_list:
                print(f"✓ 注意：object_name不匹配，主体='{current_object_name}'，招标文件={[t.get('object_name') for t in tender_list]}")
        else:
            print("✗ 需要调用LLM提取字段")
    
    # 对比修复前的逻辑
    print("\n修复前的逻辑:")
    if remaining_tender_fields and tender_content and model_apikey:
        if tender_info:  # 只检查tender_info
            print("✓ 招标文件已解析，跳过LLM重新提取")
        else:
            print("✗ 需要调用LLM提取字段（这是问题所在）")


if __name__ == "__main__":
    test_extract_fields_from_content_fix()
    test_intelligent_merge_logic()
