#!/usr/bin/env python3
"""
测试bid_doc_*字段填充修复
"""

def test_file_matching_logic():
    """测试文件匹配逻辑"""
    print("=== 测试文件匹配逻辑 ===")
    
    # 模拟数据
    appendix_url = "https://gdgpo.czt.gd.gov.cn/gpx-bid-file/440101/gpx-tender/2025/7/7/8a7e8bfd97abcb730197e2527cf271de.zip?accessCode=eb2e8db002b2af7861108d7cad5b31f1"
    
    item = {
        "filename": "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.docx",
        "file_ext": ".docx"
    }
    
    appendix_info = [
        {
            "url": "https://gdgpo.czt.gd.gov.cn/gpx-bid-file/440101/gpx-tender/2025/7/7/8a7e8bfd97abcb730197e2527cf271de.zip?accessCode=eb2e8db002b2af7861108d7cad5b31f1",
            "text": "广州医科大学附属第五医院部分后勤保障服务项目招标文件",
            "file_ext": ".zip",
            "file_link_key": "0197e96668e779dfa981b0ef92f3d99c"
        },
        {
            "url": "https://gdgpo.czt.gd.gov.cn/gpx-bid-file/ZF_JGBM_000009/440101/2025/7/4/8a7efa4c97983a820197ab46c2a82070/gpx-tender/8a7ee7c2979fc6860197d4aee95906d7.docx?accessCode=e5564253983e3ab29a87b4a4e4170a79",
            "text": "第二章采购需求",
            "file_ext": ".docx",
            "file_link_key": "0197e96672d87c04a18b8c1c92ed1aa2"
        }
    ]
    
    print(f"压缩包内文件: {item['filename']}")
    print(f"当前处理的URL: {appendix_url}")
    print(f"appendix_info条目数: {len(appendix_info)}")
    
    # 模拟修复后的匹配逻辑
    actual_file_info = None
    
    # 方法1：尝试通过文件名匹配找到对应的附件信息
    print("\n方法1：文件名匹配")
    for attachment in appendix_info:
        if item["filename"] in attachment.get("text", ""):
            actual_file_info = attachment
            print(f"  ✓ 找到匹配: {attachment['text']}")
            break
        else:
            print(f"  ✗ 不匹配: {attachment['text']}")
    
    # 方法2：如果没找到，尝试匹配压缩包本身
    if not actual_file_info:
        print("\n方法2：压缩包匹配")
        for attachment in appendix_info:
            if attachment["url"] == appendix_url:
                actual_file_info = attachment
                print(f"  ✓ 使用压缩包信息: {attachment['text']}")
                break
    
    # 方法3：如果还没找到，尝试模糊匹配
    if not actual_file_info:
        print("\n方法3：关键词匹配")
        for attachment in appendix_info:
            if "招标文件" in attachment.get("text", ""):
                actual_file_info = attachment
                print(f"  ✓ 通过关键词匹配: {attachment['text']}")
                break
    
    # 结果
    if actual_file_info:
        print(f"\n✅ 匹配成功!")
        print(f"  使用的附件信息: {actual_file_info['text']}")
        print(f"  URL: {actual_file_info['url']}")
        print(f"  Link Key: {actual_file_info['file_link_key']}")
        
        # 模拟更新结果
        result = {
            "bid_doc_name": item["filename"],  # 使用实际文件名
            "bid_doc_ext": item.get("file_ext", ""),  # 使用实际文件扩展名
            "bid_doc_link_out": actual_file_info["url"],  # 使用找到的URL
            "bid_doc_link_key": actual_file_info.get("file_link_key")  # 使用找到的key
        }
        
        print(f"\n更新后的字段:")
        for key, value in result.items():
            print(f"  {key}: {value}")
            
        # 验证
        expected_name = "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.docx"
        expected_ext = ".docx"
        expected_key = "0197e96668e779dfa981b0ef92f3d99c"
        
        print(f"\n验证结果:")
        print(f"  bid_doc_name正确: {result['bid_doc_name'] == expected_name}")
        print(f"  bid_doc_ext正确: {result['bid_doc_ext'] == expected_ext}")
        print(f"  bid_doc_link_key正确: {result['bid_doc_link_key'] == expected_key}")
        
    else:
        print("❌ 匹配失败")


def test_edge_cases():
    """测试边缘情况"""
    print("\n=== 测试边缘情况 ===")
    
    # 情况1：完全匹配的文件名
    print("情况1：完全匹配的文件名")
    item1 = {"filename": "第二章采购需求.docx", "file_ext": ".docx"}
    appendix_info1 = [{"text": "第二章采购需求.docx", "file_link_key": "key1"}]
    
    found = False
    for attachment in appendix_info1:
        if item1["filename"] in attachment.get("text", ""):
            found = True
            print(f"  ✓ 匹配成功: {attachment['text']}")
            break
    
    if not found:
        print("  ✗ 匹配失败")
    
    # 情况2：部分匹配的文件名
    print("\n情况2：部分匹配的文件名")
    item2 = {"filename": "招标文件详细版.docx", "file_ext": ".docx"}
    appendix_info2 = [{"text": "招标文件", "file_link_key": "key2"}]
    
    found = False
    for attachment in appendix_info2:
        if "招标文件" in attachment.get("text", ""):
            found = True
            print(f"  ✓ 关键词匹配成功: {attachment['text']}")
            break
    
    if not found:
        print("  ✗ 匹配失败")


if __name__ == "__main__":
    test_file_matching_logic()
    test_edge_cases()
