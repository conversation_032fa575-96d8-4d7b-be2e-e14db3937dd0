#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证索引重建结果
检查新索引的数据完整性和字段移除情况
"""

import os
from es_deal import init_es_client, search_documents
from dotenv import load_dotenv
from utils.log_cfg import log


def verify_reindex_result():
    """验证重建索引的结果"""
    
    # 加载环境变量
    load_dotenv()
    
    # 初始化ES客户端
    es = init_es_client()
    
    old_index = "markersweb_attachment_analysis_v2_20250630_164112"
    new_index = "markersweb_attachment_analysis_v3"
    removed_field = "source_response"
    
    print("=" * 60)
    print("验证索引重建结果")
    print("=" * 60)
    
    try:
        # 1. 检查索引是否存在
        old_exists = es.indices.exists(index=old_index)
        new_exists = es.indices.exists(index=new_index)
        
        print(f"原索引 {old_index} 存在: {old_exists}")
        print(f"新索引 {new_index} 存在: {new_exists}")
        
        if not new_exists:
            print("❌ 新索引不存在，重建失败")
            return False
        
        # 2. 比较文档数量
        old_count = es.count(index=old_index)["count"] if old_exists else 0
        new_count = es.count(index=new_index)["count"]
        
        print(f"\n文档数量对比:")
        print(f"原索引文档数: {old_count}")
        print(f"新索引文档数: {new_count}")
        
        if old_exists and old_count != new_count:
            print("⚠️  文档数量不一致，可能有数据丢失")
        else:
            print("✅ 文档数量一致")
        
        # 3. 检查新索引的mapping
        new_mapping = es.indices.get_mapping(index=new_index)[new_index]["mappings"]
        new_properties = new_mapping.get("properties", {})
        
        print(f"\n新索引字段检查:")
        print(f"总字段数: {len(new_properties)}")
        
        if removed_field in new_properties:
            print(f"❌ 字段 {removed_field} 仍然存在于新索引中")
            return False
        else:
            print(f"✅ 字段 {removed_field} 已成功移除")
        
        # 4. 抽样检查几个文档
        print(f"\n抽样检查文档:")
        sample_query = {
            "size": 3,
            "query": {"match_all": {}}
        }
        
        sample_result = search_documents(es, new_index, query=sample_query)
        
        if sample_result and sample_result.get("hits", {}).get("hits"):
            hits = sample_result["hits"]["hits"]
            
            for i, hit in enumerate(hits):
                doc_id = hit["_id"]
                source = hit["_source"]
                
                print(f"  文档 {i+1} (ID: {doc_id}):")
                print(f"    字段数: {len(source)}")
                
                if removed_field in source:
                    print(f"    ❌ 包含已删除字段 {removed_field}")
                    return False
                else:
                    print(f"    ✅ 不包含已删除字段 {removed_field}")
                
                # 检查一些关键字段是否存在
                key_fields = ["source_id", "prj_name", "tenderee"]
                existing_key_fields = [f for f in key_fields if f in source]
                print(f"    关键字段: {existing_key_fields}")
        
        # 5. 检查索引设置
        print(f"\n索引设置检查:")
        new_settings = es.indices.get_settings(index=new_index)[new_index]["settings"]["index"]
        
        print(f"  分片数: {new_settings.get('number_of_shards', 'N/A')}")
        print(f"  副本数: {new_settings.get('number_of_replicas', 'N/A')}")
        
        # 检查是否包含不应该存在的内部设置
        problematic_settings = ["provided_name", "creation_date", "uuid"]
        found_problematic = [s for s in problematic_settings if s in new_settings]
        
        if found_problematic:
            print(f"  ⚠️  发现内部设置: {found_problematic}")
        else:
            print(f"  ✅ 没有发现内部设置")
        
        print("\n" + "=" * 60)
        print("验证结果汇总")
        print("=" * 60)
        
        print("✅ 新索引创建成功")
        print("✅ 文档数量正确")
        print(f"✅ 字段 {removed_field} 已移除")
        print("✅ 抽样文档检查通过")
        print("✅ 索引设置正确")
        
        print(f"\n🎉 索引重建成功！")
        print(f"原索引: {old_index}")
        print(f"新索引: {new_index}")
        print(f"移除字段: {removed_field}")
        
        if old_exists:
            print(f"\n⚠️  建议:")
            print(f"1. 确认新索引数据无误后，可以删除原索引")
            print(f"2. 如需要，可以为新索引创建别名")
            print(f"3. 更新应用程序配置以使用新索引")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False


def compare_sample_documents():
    """比较原索引和新索引中的样本文档"""
    
    load_dotenv()
    es = init_es_client()
    
    old_index = "markersweb_attachment_analysis_v2_20250630_164112"
    new_index = "markersweb_attachment_analysis_v3"
    removed_field = "source_response"
    
    print("\n" + "=" * 60)
    print("详细文档对比")
    print("=" * 60)
    
    try:
        # 获取一个样本文档ID
        sample_query = {"size": 1, "query": {"match_all": {}}}
        
        old_result = search_documents(es, old_index, query=sample_query)
        new_result = search_documents(es, new_index, query=sample_query)
        
        if (old_result and old_result.get("hits", {}).get("hits") and
            new_result and new_result.get("hits", {}).get("hits")):
            
            old_doc = old_result["hits"]["hits"][0]["_source"]
            new_doc = new_result["hits"]["hits"][0]["_source"]
            
            print(f"原索引文档字段数: {len(old_doc)}")
            print(f"新索引文档字段数: {len(new_doc)}")
            
            # 检查字段差异
            old_fields = set(old_doc.keys())
            new_fields = set(new_doc.keys())
            
            removed_fields = old_fields - new_fields
            added_fields = new_fields - old_fields
            
            print(f"\n字段变化:")
            if removed_fields:
                print(f"  移除的字段: {list(removed_fields)}")
            if added_fields:
                print(f"  新增的字段: {list(added_fields)}")
            
            if removed_fields == {removed_field}:
                print(f"✅ 只移除了目标字段 {removed_field}")
            else:
                print(f"⚠️  字段变化不符合预期")
            
            # 检查共同字段的值是否一致
            common_fields = old_fields & new_fields
            different_values = []
            
            for field in list(common_fields)[:5]:  # 只检查前5个字段
                if old_doc.get(field) != new_doc.get(field):
                    different_values.append(field)
            
            if different_values:
                print(f"⚠️  以下字段的值不一致: {different_values}")
            else:
                print(f"✅ 共同字段的值保持一致")
        
    except Exception as e:
        print(f"❌ 文档对比失败: {e}")


def main():
    """主函数"""
    print("开始验证索引重建结果...")
    
    # 基本验证
    basic_success = verify_reindex_result()
    
    # 详细对比
    if basic_success:
        compare_sample_documents()
    
    print(f"\n验证完成！")


if __name__ == "__main__":
    main()
