#!/usr/bin/env python3
"""
测试完整的文件类型缓存修复

验证修复内容：
1. Phase 1对所有文件都进行类型检测并缓存
2. Phase 2 100%从缓存获取文件类型
3. 不再出现"缓存中未找到文件类型"
4. 不再有重复的get_file_info_from_content调用
"""

import os
import sys
import json
import logging
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import process_all_attachments

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def test_complete_cache_coverage():
    """测试完整的缓存覆盖"""
    log.info("=" * 80)
    log.info("测试完整的文件类型缓存覆盖")
    log.info("=" * 80)
    
    # 创建测试数据 - 包含各种情况的文件
    test_appendix = [
        {
            "text": "有扩展名的文件",
            "url": "http://example.com/document.docx"  # 能从URL确定扩展名
        },
        {
            "text": "无扩展名的文件",
            "url": "http://download.ccgp.gov.cn/oss/download?uuid=123456"  # 无法从URL确定扩展名
        },
        {
            "text": "PDF文件",
            "url": "http://example.com/report.pdf"  # 能从URL确定扩展名
        }
    ]
    
    # 计数器，统计get_file_info_from_content调用次数
    call_count = 0
    call_details = []
    
    def mock_get_file_info(content):
        nonlocal call_count
        call_count += 1
        
        # 记录调用详情
        if content.startswith(b"DOCX"):
            file_type = {"ext": ".docx", "mime": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}
        elif content.startswith(b"PDF"):
            file_type = {"ext": ".pdf", "mime": "application/pdf"}
        elif content.startswith(b"UNKNOWN"):
            file_type = {"ext": ".doc", "mime": "application/msword"}
        else:
            file_type = None
            
        call_details.append({
            "call_number": call_count,
            "content_type": content[:10],
            "result": file_type
        })
        
        log.info(f"get_file_info_from_content 第 {call_count} 次调用: {file_type}")
        return file_type
    
    def mock_download(url):
        # 模拟不同类型的文件内容
        if "document.docx" in url:
            return b"DOCX content here..."
        elif "uuid=123456" in url:
            return b"UNKNOWN content here..."
        elif "report.pdf" in url:
            return b"PDF content here..."
        return b"default content"
    
    def mock_upload(file_content, source_id, original_filename, file_ext):
        upload_id = f"upload_{hash(original_filename)}_{len(file_content)}"
        return True, upload_id
    
    def mock_get_ext_from_url(url):
        # 模拟从URL获取扩展名
        if ".docx" in url:
            return ".docx"
        elif ".pdf" in url:
            return ".pdf"
        return ""  # 无法从URL确定扩展名
    
    test_source_id = "test_complete_cache"
    
    with patch('analyse_appendix.download_file', side_effect=mock_download), \
         patch('analyse_appendix.get_file_info_from_content', side_effect=mock_get_file_info), \
         patch('analyse_appendix.upload_attachment_file', side_effect=mock_upload), \
         patch('analyse_appendix.get_file_extension_from_url', side_effect=mock_get_ext_from_url):
        
        log.info("Phase 1: 执行process_all_attachments")
        # Phase 1: 调用process_all_attachments
        appendix_info, file_content_cache, file_type_cache = process_all_attachments(
            appendix_list=test_appendix,
            source_id=test_source_id,
            enable_file_upload=True
        )
        
        phase1_call_count = call_count
        log.info(f"Phase 1完成，get_file_info_from_content调用次数: {phase1_call_count}")
        
        # 验证Phase 1结果
        assert len(appendix_info) == 3, f"期望处理3个文件，实际处理{len(appendix_info)}个"
        assert len(file_content_cache) == 3, f"期望缓存3个文件内容，实际缓存{len(file_content_cache)}个"
        assert len(file_type_cache) == 3, f"期望缓存3个文件类型，实际缓存{len(file_type_cache)}个"
        
        log.info("✓ Phase 1验证通过")
        log.info(f"  文件内容缓存: {len(file_content_cache)} 个")
        log.info(f"  文件类型缓存: {len(file_type_cache)} 个")
        
        # 验证所有文件都被缓存
        for i, appendix_item in enumerate(test_appendix):
            appendix_url = appendix_item["url"]
            appendix_text = appendix_item["text"]
            
            assert appendix_url in file_type_cache, f"文件 {appendix_text} 的类型未被缓存"
            file_info = file_type_cache[appendix_url]
            log.info(f"  缓存 {appendix_text}: {file_info}")
        
        # 模拟Phase 2的文件类型使用
        log.info("\nPhase 2: 模拟使用缓存的文件类型")
        
        cache_hits = 0
        cache_misses = 0
        
        for appendix_item in test_appendix:
            appendix_url = appendix_item.get("url")
            appendix_text = appendix_item.get("text", "")
            
            # 模拟Phase 2中的文件类型获取逻辑
            file_info = file_type_cache.get(appendix_url)
            if file_info:
                detected_ext = file_info.get("ext", "")
                log.info(f"✓ 从缓存获取文件类型: {detected_ext} for {appendix_text}")
                cache_hits += 1
            else:
                # 这种情况不应该发生
                log.error(f"❌ 缓存中未找到文件类型: {appendix_text}")
                cache_misses += 1
                
                # 如果真的缓存未命中，需要重新检测
                file_content = file_content_cache.get(appendix_url)
                if file_content:
                    file_info = mock_get_file_info(file_content)
                    detected_ext = file_info.get("ext", "") if file_info else ""
                    log.info(f"重新检测到文件类型: {detected_ext} for {appendix_text}")
        
        phase2_call_count = call_count - phase1_call_count
        log.info(f"\nPhase 2完成，额外的get_file_info_from_content调用次数: {phase2_call_count}")
        
        # 验证优化效果
        log.info("\n" + "=" * 60)
        log.info("完整缓存覆盖验证")
        log.info("=" * 60)
        
        expected_calls = len(test_appendix)  # 每个文件在Phase 1中检测一次
        actual_calls = call_count
        cache_hit_rate = (cache_hits / len(test_appendix)) * 100
        
        log.info(f"期望的get_file_info_from_content调用次数: {expected_calls}")
        log.info(f"实际的get_file_info_from_content调用次数: {actual_calls}")
        log.info(f"缓存命中次数: {cache_hits}")
        log.info(f"缓存未命中次数: {cache_misses}")
        log.info(f"缓存命中率: {cache_hit_rate:.1f}%")
        
        # 验证结果
        success = True
        if actual_calls == expected_calls:
            log.info("✅ 优化成功！没有重复的文件类型检测")
        else:
            log.error(f"❌ 仍有重复检测，多调用了 {actual_calls - expected_calls} 次")
            success = False
        
        if cache_hit_rate == 100.0:
            log.info("✅ 所有文件类型都被成功缓存，100%命中率")
        else:
            log.error(f"❌ 缓存覆盖不完整，命中率仅 {cache_hit_rate:.1f}%")
            success = False
        
        if cache_misses == 0:
            log.info("✅ 没有出现'缓存中未找到文件类型'的情况")
        else:
            log.error(f"❌ 仍有 {cache_misses} 次缓存未命中")
            success = False
        
        # 显示调用详情
        log.info("\n调用详情:")
        for detail in call_details:
            log.info(f"  第{detail['call_number']}次: {detail['content_type']} -> {detail['result']}")
        
        return {
            "total_calls": actual_calls,
            "expected_calls": expected_calls,
            "cache_hit_rate": cache_hit_rate,
            "cache_misses": cache_misses,
            "optimization_success": success
        }

def main():
    """主测试函数"""
    log.info("开始完整的文件类型缓存修复测试")
    log.info("=" * 100)
    
    try:
        # 测试完整的缓存覆盖
        result = test_complete_cache_coverage()
        
        log.info("\n" + "=" * 100)
        if result["optimization_success"]:
            log.info("🎉 文件类型缓存修复测试完全成功！")
        else:
            log.error("❌ 文件类型缓存修复测试失败！")
        log.info("=" * 100)
        
        log.info("\n修复效果总结:")
        log.info(f"✓ Phase 1对所有文件进行类型检测并缓存")
        log.info(f"✓ Phase 2缓存命中率: {result['cache_hit_rate']:.1f}%")
        log.info(f"✓ 缓存未命中次数: {result['cache_misses']}")
        log.info(f"✓ 总检测次数: {result['total_calls']} (期望: {result['expected_calls']})")
        log.info(f"✓ 修复成功: {result['optimization_success']}")
        
        return result["optimization_success"]
        
    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
