# 删除旧文档工具使用说明

## 功能描述

删除 `markersweb_attachment_analysis_alias` 索引中符合以下条件的文档：
1. `insert_time` 在 `2025-07-04 12:00:00` 之前
2. `source_appendix` 不等于 `[]`（即有附件的文档）

## 安装依赖

```bash
pip install elasticsearch python-dotenv
```

## 配置环境变量

1. 复制配置文件模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置您的Elasticsearch连接信息：
```
ES_HOST=your_elasticsearch_host:9200
ES_USERNAME=your_username
ES_PASSWORD=your_password
ES_INDEX_ANALYSIS_ALIAS=markersweb_attachment_analysis_alias
```

## 使用方法

### 1. 干运行模式（推荐先执行）

查看会删除哪些文档，但不实际删除：

```bash
python delete_old_documents.py --dry-run
```

### 2. 执行实际删除

```bash
python delete_old_documents.py
```

### 3. 自定义参数

```bash
# 自定义截止时间
python delete_old_documents.py --cutoff-time "2025-07-04 10:00:00"

# 自定义批量大小
python delete_old_documents.py --batch-size 500

# 组合使用
python delete_old_documents.py --dry-run --cutoff-time "2025-07-04 10:00:00"
```

## 参数说明

- `--dry-run`: 干运行模式，只查看会删除的文档，不实际删除
- `--batch-size`: 批量删除的大小，默认1000
- `--cutoff-time`: 截止时间，删除此时间之前的文档，默认"2025-07-04 12:00:00"

## 查询条件详解

程序会构建以下Elasticsearch查询：

```json
{
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "insert_time": {
              "lt": "2025-07-04 12:00:00"
            }
          }
        },
        {
          "bool": {
            "must_not": [
              {
                "term": {
                  "source_appendix.keyword": "[]"
                }
              }
            ]
          }
        }
      ],
      "filter": [
        {
          "exists": {
            "field": "source_appendix"
          }
        }
      ]
    }
  }
}
```

这个查询的含义：
1. `insert_time` 小于指定时间
2. `source_appendix` 字段存在
3. `source_appendix` 不等于空数组 `[]`

## 安全特性

1. **干运行模式**：先使用 `--dry-run` 查看会删除哪些文档
2. **批量处理**：大量文档分批删除，避免超时
3. **详细日志**：所有操作都有详细日志记录
4. **错误处理**：单个文档删除失败不影响整体流程
5. **进度显示**：实时显示删除进度

## 日志文件

程序会生成 `delete_old_documents.log` 日志文件，记录所有操作详情。

## 示例输出

### 干运行模式输出：
```
2025-07-04 15:00:00,123 - INFO - 连接到Elasticsearch: localhost:9200
2025-07-04 15:00:00,124 - INFO - 目标索引: markersweb_attachment_analysis_alias
2025-07-04 15:00:00,125 - INFO - ============================================================
2025-07-04 15:00:00,125 - INFO - DRY RUN 模式 - 不会实际删除文档
2025-07-04 15:00:00,125 - INFO - ============================================================
2025-07-04 15:00:01,234 - INFO - 找到符合删除条件的文档数量: 1500
2025-07-04 15:00:01,235 - INFO - 在DRY RUN模式下，将会删除 1500 个文档
2025-07-04 15:00:01,345 - INFO - 示例文档（前10个）:
2025-07-04 15:00:01,345 - INFO -   1. ID: doc_id_1
2025-07-04 15:00:01,345 - INFO -      标题: 某某招标公告
2025-07-04 15:00:01,345 - INFO -      插入时间: 2025-07-04 10:30:00
2025-07-04 15:00:01,345 - INFO -      附件数量: 3
```

### 实际删除输出：
```
2025-07-04 15:05:00,123 - INFO - ============================================================
2025-07-04 15:05:00,123 - INFO - 执行实际删除操作
2025-07-04 15:05:00,123 - INFO - ============================================================
2025-07-04 15:05:01,234 - INFO - 找到符合删除条件的文档数量: 1500
2025-07-04 15:05:01,235 - WARNING - 即将删除 1500 个文档，此操作不可逆！
2025-07-04 15:05:02,345 - INFO - 总共找到 1500 个需要删除的文档
2025-07-04 15:05:02,346 - INFO - 开始批量删除 1500 个文档，批量大小: 1000
2025-07-04 15:05:02,346 - INFO - 处理第 1/2 批，包含 1000 个文档
2025-07-04 15:05:05,123 - INFO - 第 1 批删除完成: 成功 1000, 失败 0
2025-07-04 15:05:05,124 - INFO - 处理第 2/2 批，包含 500 个文档
2025-07-04 15:05:07,456 - INFO - 第 2 批删除完成: 成功 500, 失败 0
2025-07-04 15:05:07,456 - INFO - ============================================================
2025-07-04 15:05:07,456 - INFO - 删除操作完成
2025-07-04 15:05:07,456 - INFO - 总文档数: 1500
2025-07-04 15:05:07,456 - INFO - 成功删除: 1500
2025-07-04 15:05:07,456 - INFO - 删除失败: 0
2025-07-04 15:05:07,456 - INFO - ============================================================
```

## 注意事项

1. **备份数据**：删除操作不可逆，建议先备份重要数据
2. **先干运行**：务必先使用 `--dry-run` 模式确认删除范围
3. **网络稳定**：确保网络连接稳定，避免删除过程中断
4. **权限检查**：确保有删除目标索引文档的权限
5. **时间格式**：时间格式必须为 `YYYY-MM-DD HH:MM:SS`

## 故障排除

1. **连接失败**：检查ES_HOST、ES_USERNAME、ES_PASSWORD配置
2. **权限错误**：确保用户有删除文档的权限
3. **索引不存在**：检查ES_INDEX_ANALYSIS_ALIAS配置
4. **查询超时**：可以减小batch_size参数
