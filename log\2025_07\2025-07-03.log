2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1204 - 主体标的物 'CT设备' 匹配结果:
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1205 -   - 招标文件匹配: 是
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1206 -   - 合同文件匹配: 是
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1204 - 主体标的物 'MRI设备' 匹配结果:
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1205 -   - 招标文件匹配: 是
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1206 -   - 合同文件匹配: 是
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1204 - 主体标的物 'CT扫描仪' 匹配结果:
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1205 -   - 招标文件匹配: 否
2025-07-03 09:07:45.154 | INFO     | analyse_appendix:merge_analysis_by_object_name:1206 -   - 合同文件匹配: 否
2025-07-03 09:07:45.159 | INFO     | analyse_appendix:merge_analysis_by_object_name:1158 - 招标文件标的物 '超声设备' 与合同文件匹配: 是
2025-07-03 09:07:45.159 | INFO     | analyse_appendix:merge_analysis_by_object_name:1158 - 招标文件标的物 '心电图机' 与合同文件匹配: 否
2025-07-03 09:07:45.159 | INFO     | analyse_appendix:merge_analysis_by_object_name:1177 - 合同文件标的物 '血压计' 没有匹配的招标文件，单独处理
2025-07-03 09:07:45.162 | INFO     | analyse_appendix:intelligent_merge_analysis:1246 - 开始基于object_name的匹配融合分析
2025-07-03 09:07:45.162 | INFO     | analyse_appendix:merge_analysis_by_object_name:1204 - 主体标的物 '呼吸机' 匹配结果:
2025-07-03 09:07:45.162 | INFO     | analyse_appendix:merge_analysis_by_object_name:1205 -   - 招标文件匹配: 是
2025-07-03 09:07:45.162 | INFO     | analyse_appendix:merge_analysis_by_object_name:1206 -   - 合同文件匹配: 是
2025-07-03 09:07:45.163 | INFO     | analyse_appendix:intelligent_merge_analysis:1254 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 09:07:45.163 | INFO     | analyse_appendix:intelligent_merge_analysis:1383 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 09:09:36.524 | INFO     | analyse_appendix:merge_analysis_by_object_name:1245 - 主体标的物 'CT设备' 匹配结果:
2025-07-03 09:09:36.524 | INFO     | analyse_appendix:merge_analysis_by_object_name:1246 -   - 招标文件匹配: 是
2025-07-03 09:09:36.529 | INFO     | analyse_appendix:merge_analysis_by_object_name:1247 -   - 合同文件匹配: 是
2025-07-03 09:09:36.529 | INFO     | analyse_appendix:merge_analysis_by_object_name:1245 - 主体标的物 'MRI设备' 匹配结果:
2025-07-03 09:09:36.529 | INFO     | analyse_appendix:merge_analysis_by_object_name:1246 -   - 招标文件匹配: 是
2025-07-03 09:09:36.529 | INFO     | analyse_appendix:merge_analysis_by_object_name:1247 -   - 合同文件匹配: 是
2025-07-03 09:09:36.534 | INFO     | analyse_appendix:merge_analysis_by_object_name:1245 - 主体标的物 'CT扫描仪' 匹配结果:
2025-07-03 09:09:36.534 | INFO     | analyse_appendix:merge_analysis_by_object_name:1246 -   - 招标文件匹配: 是
2025-07-03 09:09:36.535 | INFO     | analyse_appendix:merge_analysis_by_object_name:1247 -   - 合同文件匹配: 是
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:merge_analysis_by_object_name:1199 - 招标文件标的物 '超声设备' 与合同文件匹配: 是
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:merge_analysis_by_object_name:1199 - 招标文件标的物 '心电图机' 与合同文件匹配: 否
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:merge_analysis_by_object_name:1218 - 合同文件标的物 '血压计' 没有匹配的招标文件，单独处理
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:intelligent_merge_analysis:1287 - 开始基于object_name的匹配融合分析
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:merge_analysis_by_object_name:1245 - 主体标的物 '呼吸机' 匹配结果:
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:merge_analysis_by_object_name:1246 -   - 招标文件匹配: 是
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:merge_analysis_by_object_name:1247 -   - 合同文件匹配: 是
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:intelligent_merge_analysis:1295 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 09:09:36.538 | INFO     | analyse_appendix:intelligent_merge_analysis:1424 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 09:17:15.914 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:17:15.919 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:17:16.186 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3270 条结果
2025-07-03 09:17:16.186 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:17:16.870 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6824 条结果
2025-07-03 09:17:16.870 | INFO     | __main__:process_one_record:1874 - ID: -1kMqpcBsUtJ06NfAuOo 开始分析公告主体
2025-07-03 09:17:16.870 | INFO     | __main__:process_one_record:1875 - 公告标题: 北京大学人民医院诊疗能力提升项目公开招标公告
2025-07-03 09:17:16.870 | INFO     | __main__:process_one_record:1876 - 公告链接: http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm
2025-07-03 09:17:17.908 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:17:18.711 | ERROR    | __main__:llm:579 - LLM API调用失败 (尝试 1/3): Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'param': None, 'type': 'invalid_request_error'}, 'request_id': '19182f99-edcc-474a-ac16-28773707ad48'}
2025-07-03 09:17:18.712 | ERROR    | __main__:process_one_record:2406 - 处理过程中发生异常: LLM API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'param': None, 'type': 'invalid_request_error'}, 'request_id': '19182f99-edcc-474a-ac16-28773707ad48'}
2025-07-03 09:17:18.713 | WARNING  | __main__:process_one_record:2407 - 尝试保存已处理的主体解析结果...
2025-07-03 09:17:18.713 | ERROR    | __main__:process_one_record:2425 - 保存主体解析结果也失败: cannot access local variable 'main_result_list' where it is not associated with a value
2025-07-03 09:20:30.678 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:20:30.678 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:20:31.011 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3270 条结果
2025-07-03 09:20:31.011 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:20:31.749 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6824 条结果
2025-07-03 09:20:31.749 | INFO     | __main__:process_one_record:1874 - ID: -1kMqpcBsUtJ06NfAuOo 开始分析公告主体
2025-07-03 09:20:31.749 | INFO     | __main__:process_one_record:1875 - 公告标题: 北京大学人民医院诊疗能力提升项目公开招标公告
2025-07-03 09:20:31.749 | INFO     | __main__:process_one_record:1876 - 公告链接: http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm
2025-07-03 09:20:32.677 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:20:32.998 | ERROR    | __main__:llm:579 - LLM API调用失败 (尝试 1/3): Completions.create() got an unexpected keyword argument 'enable_thinking'
2025-07-03 09:20:32.998 | ERROR    | __main__:process_one_record:2406 - 处理过程中发生异常: LLM API调用失败: Completions.create() got an unexpected keyword argument 'enable_thinking'
2025-07-03 09:20:32.998 | WARNING  | __main__:process_one_record:2407 - 尝试保存已处理的主体解析结果...
2025-07-03 09:20:33.002 | WARNING  | __main__:process_one_record:2424 - 没有主体解析结果可保存
2025-07-03 09:21:23.549 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:21:23.549 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:21:23.982 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3270 条结果
2025-07-03 09:21:23.982 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:21:24.683 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6824 条结果
2025-07-03 09:21:24.683 | INFO     | __main__:process_one_record:1873 - ID: -1kMqpcBsUtJ06NfAuOo 开始分析公告主体
2025-07-03 09:21:24.683 | INFO     | __main__:process_one_record:1874 - 公告标题: 北京大学人民医院诊疗能力提升项目公开招标公告
2025-07-03 09:21:24.683 | INFO     | __main__:process_one_record:1875 - 公告链接: http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm
2025-07-03 09:21:25.755 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:21:26.534 | ERROR    | __main__:llm:578 - LLM API调用失败 (尝试 1/3): Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'param': None, 'type': 'invalid_request_error'}, 'request_id': '25e0d4c8-71af-465d-9d6e-2da596d08a6e'}
2025-07-03 09:21:26.534 | ERROR    | __main__:process_one_record:2405 - 处理过程中发生异常: LLM API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'param': None, 'type': 'invalid_request_error'}, 'request_id': '25e0d4c8-71af-465d-9d6e-2da596d08a6e'}
2025-07-03 09:21:26.534 | WARNING  | __main__:process_one_record:2406 - 尝试保存已处理的主体解析结果...
2025-07-03 09:21:26.534 | WARNING  | __main__:process_one_record:2423 - 没有主体解析结果可保存
2025-07-03 09:22:19.659 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:22:19.659 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:22:20.426 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3270 条结果
2025-07-03 09:22:20.431 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:22:21.227 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6824 条结果
2025-07-03 09:22:21.227 | INFO     | __main__:process_one_record:1884 - ID: -1kMqpcBsUtJ06NfAuOo 开始分析公告主体
2025-07-03 09:22:21.227 | INFO     | __main__:process_one_record:1885 - 公告标题: 北京大学人民医院诊疗能力提升项目公开招标公告
2025-07-03 09:22:21.227 | INFO     | __main__:process_one_record:1886 - 公告链接: http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm
2025-07-03 09:22:22.267 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:22:40.073 | INFO     | __main__:llm:584 - LLM API调用成功
2025-07-03 09:22:40.074 | INFO     | __main__:analyze_content:1828 - [
    {
        "bid_name": null,
        "bid_number": null,
        "bid_budget": 13000000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "北京市西城区西直门南大街11号",
        "prj_name": "北京大学人民医院诊疗能力提升项目",
        "prj_number": "0686-2511CC121414Z",
        "prj_type": "货物",
        "release_time": "2025-06-25 12:07:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "北京大学人民医院",
        "bid_submission_deadline": "2025-07-16 13:30:00",
        "trade_platform": "中国政府采购网",
        "procurement_method": "公开招标",
        "prj_sub_type": "手术器械",
        "province": "北京市",
        "city": "北京市",
        "county": null,
        "announcement_type": "001",
        "object_name": null,
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": null,
        "object_unit": null,
        "object_price": null,
        "object_total_price": null,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "北京国际贸易有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 09:22:40.074 | INFO     | __main__:process_one_record:1957 - ID: -1kMqpcBsUtJ06NfAuOo 发现附件，开始分析附件
2025-07-03 09:22:40.074 | INFO     | __main__:process_one_record:1958 - 附件列表: [{'url': 'http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076', 'text': '采购需求'}, {'url': 'http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96', 'text': '招标公告'}]
2025-07-03 09:22:40.074 | INFO     | __main__:process_one_record:1969 - 正在下载附件: http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076
2025-07-03 09:22:40.074 | INFO     | __main__:download_file:624 - 正在下载文件 (尝试 1/3): http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076
2025-07-03 09:22:40.414 | INFO     | __main__:download_file:655 - 文件下载成功: http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076
2025-07-03 09:22:40.414 | WARNING  | __main__:process_one_record:1977 - 无法从URL中确定文件类型, 尝试从文件内容中获取: http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076
2025-07-03 09:22:40.414 | INFO     | __main__:get_file_info_from_content:969 - Guessed file type: MIME=application/pdf, EXT=.pdf
2025-07-03 09:22:40.414 | INFO     | __main__:process_one_record:2011 - 附件已保存到: downloads\采购需求.pdf
2025-07-03 09:22:40.414 | INFO     | __main__:process_one_record:2035 - 使用markitdown转换文件: 采购需求.pdf
2025-07-03 09:22:40.658 | DEBUG    | __main__:convert_to_markdown_with_markitdown:763 - markitdown成功转换.pdf文件，生成1889字符的Markdown
2025-07-03 09:22:40.681 | INFO     | __main__:process_one_record:2077 - 正在分析文件: download
2025-07-03 09:22:40.851 | DEBUG    | __main__:extract_preview_text:999 - Successfully extracted PDF preview with pdfplumber, 1776 characters
2025-07-03 09:22:40.851 | INFO     | __main__:detect_file_type:1044 - 此文件是其他文件: 采购需求
2025-07-03 09:22:40.851 | INFO     | __main__:process_one_record:2161 - 跳过非招标/合同文件: download
2025-07-03 09:22:40.851 | INFO     | __main__:process_one_record:1969 - 正在下载附件: http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96
2025-07-03 09:22:40.851 | INFO     | __main__:download_file:624 - 正在下载文件 (尝试 1/3): http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96
2025-07-03 09:22:40.880 | ERROR    | __main__:download_file:659 - 文件下载失败 (尝试 1/3): 403 Client Error: Forbidden for url: http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96
2025-07-03 09:22:40.880 | INFO     | __main__:download_file:670 - 检测到403错误，尝试使用不同的请求策略...
2025-07-03 09:22:42.882 | INFO     | __main__:download_file:624 - 正在下载文件 (尝试 2/3): http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96
2025-07-03 09:22:43.165 | INFO     | __main__:download_file:655 - 文件下载成功: http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96
2025-07-03 09:22:43.165 | WARNING  | __main__:process_one_record:1977 - 无法从URL中确定文件类型, 尝试从文件内容中获取: http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96
2025-07-03 09:22:43.165 | INFO     | __main__:get_file_info_from_content:969 - Guessed file type: MIME=application/pdf, EXT=.pdf
2025-07-03 09:22:43.170 | INFO     | __main__:process_one_record:2011 - 附件已保存到: downloads\招标公告.pdf
2025-07-03 09:22:43.170 | INFO     | __main__:process_one_record:2035 - 使用markitdown转换文件: 招标公告.pdf
2025-07-03 09:22:43.355 | DEBUG    | __main__:convert_to_markdown_with_markitdown:763 - markitdown成功转换.pdf文件，生成1756字符的Markdown
2025-07-03 09:22:43.376 | INFO     | __main__:process_one_record:2077 - 正在分析文件: download
2025-07-03 09:22:43.524 | DEBUG    | __main__:extract_preview_text:999 - Successfully extracted PDF preview with pdfplumber, 1327 characters
2025-07-03 09:22:43.524 | INFO     | __main__:detect_file_type:1039 - 此文件是招标文件: 招标公告
2025-07-03 09:22:43.524 | INFO     | __main__:process_one_record:2088 - 存储招标文件内容: download
2025-07-03 09:22:43.525 | INFO     | __main__:process_one_record:2106 - 开始上传招标文件: download
2025-07-03 09:22:43.525 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-1kMqpcBsUtJ06NfAuOo.pdf
2025-07-03 09:22:43.525 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-1kMqpcBsUtJ06NfAuOo.pdf, 大小: 109057 字节
2025-07-03 09:22:43.525 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=109057&objectName=招标文件_-1kMqpcBsUtJ06NfAuOo.pdf
2025-07-03 09:22:43.627 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197cde101e372f4857826df54ad2b59', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197cde101e372f4857826df54ad2b59/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-1kMqpcBsUtJ06NfAuOo.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T012244Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=01dc47eea55483b918cddbb6498a23b913a4b0a1c1a6f131064caa782fefb849'}, 'timestamp': 1751505764854, 'callId': None}
2025-07-03 09:22:43.628 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 109057 字节
2025-07-03 09:22:43.699 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 09:22:43.700 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197cde101e372f4857826df54ad2b59
2025-07-03 09:22:44.007 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 09:22:44.007 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-1kMqpcBsUtJ06NfAuOo.pdf
2025-07-03 09:22:44.007 | INFO     | __main__:process_one_record:2119 - ✓ 招标文件上传成功: 招标文件_-1kMqpcBsUtJ06NfAuOo.pdf, upload_id: 0197cde101e372f4857826df54ad2b59
2025-07-03 09:22:44.981 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:23:05.643 | INFO     | __main__:llm:584 - LLM API调用成功
2025-07-03 09:23:05.643 | INFO     | __main__:analyze_content:1828 - [
    {
        "bid_name": "北京大学人民医院诊疗能力提升项目",
        "bid_number": "01",
        "bid_budget": 13000000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "北京市西城区西直门南大街11号",
        "prj_name": "北京大学人民医院诊疗能力提升项目",
        "prj_number": "0686-2511CC121414Z",
        "prj_type": "货物",
        "release_time": "2025-06-26 00:00:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "北京大学人民医院",
        "bid_submission_deadline": "2025-07-16 13:30:00",
        "trade_platform": null,
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "北京市",
        "city": "北京市",
        "county": null,
        "announcement_type": "001",
        "object_name": "骨科手术机器人",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台/套",
        "object_price": 13000000.0,
        "object_total_price": 13000000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": "2",
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "北京国际贸易有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 09:23:05.643 | INFO     | __main__:process_one_record:2240 - 已找到并分析招标文件: download
2025-07-03 09:23:05.643 | INFO     | __main__:process_one_record:2340 - ================================================================================
2025-07-03 09:23:05.643 | INFO     | __main__:process_one_record:2341 - 开始融合分析结果
2025-07-03 09:23:05.643 | INFO     | __main__:process_one_record:2342 - ================================================================================
2025-07-03 09:23:05.643 | INFO     | __main__:process_one_record:2343 - 主体解析结果数量: 1
2025-07-03 09:23:05.648 | INFO     | __main__:process_one_record:2344 - 招标文件解析结果数量: 1
2025-07-03 09:23:05.648 | INFO     | __main__:process_one_record:2345 - 合同文件解析结果数量: 0
2025-07-03 09:23:05.648 | INFO     | __main__:process_one_record:2348 - 主体解析结果:
2025-07-03 09:23:05.648 | INFO     | __main__:process_one_record:2350 - 主体结果 1:
2025-07-03 09:23:05.648 | INFO     | __main__:process_one_record:2351 - {
  "bid_name": null,
  "bid_number": null,
  "bid_budget": 13000000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "北京市西城区西直门南大街11号",
  "prj_name": "北京大学人民医院诊疗能力提升项目",
  "prj_number": "0686-2511CC121414Z",
  "prj_type": "货物",
  "release_time": "2025-06-25 12:07:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "北京大学人民医院",
  "bid_submission_deadline": "2025-07-16 13:30:00",
  "trade_platform": "中国政府采购网",
  "procurement_method": "公开招标",
  "prj_sub_type": "手术器械",
  "province": "北京市",
  "city": "北京市",
  "county": null,
  "announcement_type": "001",
  "object_name": null,
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": null,
  "object_unit": null,
  "object_price": null,
  "object_total_price": null,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "北京国际贸易有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1kMqpcBsUtJ06NfAuOo",
  "source_title": "北京大学人民医院诊疗能力提升项目公开招标公告",
  "source_create_time": "2025-06-26",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm",
  "source_appendix": [
    {
      "url": "http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076",
      "text": "采购需求"
    },
    {
      "url": "http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96",
      "text": "招标公告"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 09:22:40"
}
2025-07-03 09:23:05.651 | INFO     | __main__:process_one_record:2356 - 招标文件解析结果:
2025-07-03 09:23:05.651 | INFO     | __main__:process_one_record:2358 - 招标文件结果 1:
2025-07-03 09:23:05.652 | INFO     | __main__:process_one_record:2359 - {
  "bid_name": "北京大学人民医院诊疗能力提升项目",
  "bid_number": "01",
  "bid_budget": 13000000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "北京市西城区西直门南大街11号",
  "prj_name": "北京大学人民医院诊疗能力提升项目",
  "prj_number": "0686-2511CC121414Z",
  "prj_type": "货物",
  "release_time": "2025-06-26 00:00:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "北京大学人民医院",
  "bid_submission_deadline": "2025-07-16 13:30:00",
  "trade_platform": null,
  "procurement_method": null,
  "prj_sub_type": "设备",
  "province": "北京市",
  "city": "北京市",
  "county": null,
  "announcement_type": "001",
  "object_name": "骨科手术机器人",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台/套",
  "object_price": 13000000.0,
  "object_total_price": 13000000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": "2",
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "北京国际贸易有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1kMqpcBsUtJ06NfAuOo",
  "source_title": "北京大学人民医院诊疗能力提升项目公开招标公告",
  "source_create_time": "2025-06-26",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/zygg/gkzb/202506/t20250625_24842659.htm",
  "source_appendix": [
    {
      "url": "http://download.ccgp.gov.cn/oss/download?uuid=275665AB8B92F616F1C560AED59076",
      "text": "采购需求"
    },
    {
      "url": "http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96",
      "text": "招标公告"
    }
  ],
  "insert_time": "2025-07-03 09:23:05",
  "bid_doc_name": "招标公告.pdf",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "http://download.ccgp.gov.cn/oss/download?uuid=2013A8E64D724B47CC4C90DB9A5D96",
  "bid_doc_link_key": "0197cde101e372f4857826df54ad2b59",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null
}
2025-07-03 09:23:05.653 | INFO     | __main__:process_one_record:2375 - 使用智能融合分析
2025-07-03 09:23:05.653 | INFO     | __main__:intelligent_merge_analysis:1297 - 开始基于object_name的匹配融合分析
2025-07-03 09:23:05.653 | INFO     | __main__:merge_analysis_by_object_name:1255 - 主体标的物 'None' 匹配结果:
2025-07-03 09:23:05.654 | INFO     | __main__:merge_analysis_by_object_name:1256 -   - 招标文件匹配: 否
2025-07-03 09:23:05.655 | INFO     | __main__:merge_analysis_by_object_name:1257 -   - 合同文件匹配: 否
2025-07-03 09:23:05.655 | INFO     | __main__:intelligent_merge_analysis:1305 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 09:23:05.655 | INFO     | __main__:intelligent_merge_analysis:1314 - 开始对融合结果进行智能补充
2025-07-03 09:23:05.655 | INFO     | __main__:intelligent_merge_analysis:1318 - 处理第1个融合结果的智能补充
2025-07-03 09:23:05.655 | INFO     | __main__:intelligent_merge_analysis:1331 - 发现空缺字段: ['bid_name', 'bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'county', 'object_name', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_conf', 'object_oem', 'object_amount', 'object_unit', 'object_price', 'object_total_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_price', 'bidder_name', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 09:23:05.655 | ERROR    | __main__:process_one_record:2416 - 处理过程中发生异常: cannot access local variable 'other_missing_fields' where it is not associated with a value
2025-07-03 09:23:05.655 | WARNING  | __main__:process_one_record:2417 - 尝试保存已处理的主体解析结果...
2025-07-03 09:23:05.655 | INFO     | __main__:process_one_record:2423 - 保存1个主体解析结果
2025-07-03 09:23:05.655 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1kMqpcBsUtJ06NfAuOo_main_0
2025-07-03 09:23:05.700 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1kMqpcBsUtJ06NfAuOo_main_0
2025-07-03 09:23:05.700 | INFO     | __main__:process_one_record:2432 - 成功插入主体解析结果 -1kMqpcBsUtJ06NfAuOo_main_0
2025-07-03 09:27:18.019 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:27:18.021 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:27:19.262 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3271 条结果
2025-07-03 09:27:19.263 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:27:20.309 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6823 条结果
2025-07-03 09:27:20.309 | INFO     | __main__:process_one_record:1921 - ID: -1o4r5cBsUtJ06NfkR31 开始分析公告主体
2025-07-03 09:27:20.309 | INFO     | __main__:process_one_record:1922 - 公告标题: 巴楚县中医医院中草药饮片采购项目中标(成交)结果公告
2025-07-03 09:27:20.309 | INFO     | __main__:process_one_record:1923 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250626_24849935.htm
2025-07-03 09:27:21.442 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:30:55.936 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:30:55.938 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:30:56.218 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3271 条结果
2025-07-03 09:30:56.218 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:30:57.031 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6823 条结果
2025-07-03 09:30:57.031 | INFO     | __main__:process_one_record:1884 - ID: -1o4r5cBsUtJ06NfkR31 开始分析公告主体
2025-07-03 09:30:57.031 | INFO     | __main__:process_one_record:1885 - 公告标题: 巴楚县中医医院中草药饮片采购项目中标(成交)结果公告
2025-07-03 09:30:57.036 | INFO     | __main__:process_one_record:1886 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250626_24849935.htm
2025-07-03 09:30:58.087 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:35:08.385 | INFO     | analyse_appendix:intelligent_merge_analysis:1297 - 开始基于object_name的匹配融合分析
2025-07-03 09:35:08.385 | INFO     | analyse_appendix:merge_analysis_by_object_name:1255 - 主体标的物 'None' 匹配结果:
2025-07-03 09:35:08.385 | INFO     | analyse_appendix:merge_analysis_by_object_name:1256 -   - 招标文件匹配: 否
2025-07-03 09:35:08.385 | INFO     | analyse_appendix:merge_analysis_by_object_name:1257 -   - 合同文件匹配: 否
2025-07-03 09:35:08.385 | INFO     | analyse_appendix:intelligent_merge_analysis:1305 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 09:35:08.385 | INFO     | analyse_appendix:intelligent_merge_analysis:1434 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 09:35:08.389 | INFO     | analyse_appendix:intelligent_merge_analysis:1297 - 开始基于object_name的匹配融合分析
2025-07-03 09:35:08.391 | INFO     | analyse_appendix:merge_analysis_by_object_name:1255 - 主体标的物 'MRI设备' 匹配结果:
2025-07-03 09:35:08.391 | INFO     | analyse_appendix:merge_analysis_by_object_name:1256 -   - 招标文件匹配: 是
2025-07-03 09:35:08.391 | INFO     | analyse_appendix:merge_analysis_by_object_name:1257 -   - 合同文件匹配: 是
2025-07-03 09:35:08.391 | INFO     | analyse_appendix:intelligent_merge_analysis:1305 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 09:35:08.392 | INFO     | analyse_appendix:intelligent_merge_analysis:1434 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 09:37:09.920 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:37:09.920 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:37:11.148 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3271 条结果
2025-07-03 09:37:11.148 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:37:11.885 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 6823 条结果
2025-07-03 09:37:11.885 | INFO     | __main__:process_one_record:1884 - ID: -1o4r5cBsUtJ06NfkR31 开始分析公告主体
2025-07-03 09:37:11.885 | INFO     | __main__:process_one_record:1885 - 公告标题: 巴楚县中医医院中草药饮片采购项目中标(成交)结果公告
2025-07-03 09:37:11.885 | INFO     | __main__:process_one_record:1886 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250626_24849935.htm
2025-07-03 09:37:12.917 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:43:14.739 | ERROR    | __main__:llm:589 - LLM API调用失败 (尝试 1/3): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 09:43:14.739 | INFO     | __main__:llm:603 - 检测到网络错误，等待 10 秒后重试...
2025-07-03 09:43:25.663 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 2/3)...
2025-07-03 09:49:27.180 | ERROR    | __main__:llm:589 - LLM API调用失败 (尝试 2/3): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 09:49:27.181 | INFO     | __main__:llm:603 - 检测到网络错误，等待 20 秒后重试...
2025-07-03 09:49:48.577 | INFO     | __main__:llm:562 - 正在调用LLM API (尝试 3/3)...
2025-07-03 09:51:23.892 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:51:23.894 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:51:24.337 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3271 条结果
2025-07-03 09:51:24.338 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:51:25.583 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 24 条结果
2025-07-03 09:51:25.583 | INFO     | __main__:process_one_record:739 - ID: 0KH5zZcBfqDgtia4UG4l的附件链接为空
2025-07-03 09:51:25.583 | INFO     | __main__:process_one_record:740 - 公告标题: ［江西省公共资源交易平台］赣州正辉项目管理有限公司关于宁都县传染病医院二期负压病房装修采购项目（项目编号：GZZH2025-ND-G002）的电子化公开招标采购公告
2025-07-03 09:51:25.584 | INFO     | __main__:process_one_record:741 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202507/t20250702_24894649.htm
2025-07-03 09:51:25.584 | INFO     | __main__:process_one_record:742 - 公告类型: 001
2025-07-03 09:51:25.584 | INFO     | __main__:process_one_record:743 - 附件链接: []
2025-07-03 09:51:26.469 | INFO     | __main__:llm:303 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:51:27.246 | ERROR    | __main__:llm:321 - LLM API调用失败 (尝试 1/3): Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'param': None, 'type': 'invalid_request_error'}, 'request_id': 'f1a0f333-21f7-4361-90f8-9d1e431608c8'}
2025-07-03 09:51:27.246 | ERROR    | __main__:process_one_record:749 - 文档解析失败: LLM API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'param': None, 'type': 'invalid_request_error'}, 'request_id': 'f1a0f333-21f7-4361-90f8-9d1e431608c8'}
2025-07-03 09:51:27.246 | WARNING  | __main__:process_one_record:750 - 跳过文档解析: 0KH5zZcBfqDgtia4UG4l
2025-07-03 09:55:50.042 | ERROR    | __main__:llm:589 - LLM API调用失败 (尝试 3/3): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 09:55:50.043 | ERROR    | __main__:process_one_record:2416 - 处理过程中发生异常: LLM API调用失败 (已重试3次): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 09:55:50.044 | WARNING  | __main__:process_one_record:2417 - 尝试保存已处理的主体解析结果...
2025-07-03 09:55:50.045 | WARNING  | __main__:process_one_record:2434 - 没有主体解析结果可保存
2025-07-03 09:57:12.141 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 09:57:12.142 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 09:57:12.862 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3271 条结果
2025-07-03 09:57:12.862 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 09:57:14.407 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 37 条结果
2025-07-03 09:57:14.407 | INFO     | __main__:process_one_record:751 - ID: 0KH5zZcBfqDgtia4UG4l的附件链接为空
2025-07-03 09:57:14.407 | INFO     | __main__:process_one_record:752 - 公告标题: ［江西省公共资源交易平台］赣州正辉项目管理有限公司关于宁都县传染病医院二期负压病房装修采购项目（项目编号：GZZH2025-ND-G002）的电子化公开招标采购公告
2025-07-03 09:57:14.407 | INFO     | __main__:process_one_record:753 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202507/t20250702_24894649.htm
2025-07-03 09:57:14.407 | INFO     | __main__:process_one_record:754 - 公告类型: 001
2025-07-03 09:57:14.407 | INFO     | __main__:process_one_record:755 - 附件链接: []
2025-07-03 09:57:15.293 | INFO     | __main__:llm:303 - 正在调用LLM API (尝试 1/3)...
2025-07-03 09:57:15.294 | INFO     | __main__:llm:318 - enable_thinking=False
2025-07-03 09:57:15.548 | INFO     | __main__:llm:324 - extra_body={'enable_thinking': False}
2025-07-03 09:57:30.701 | INFO     | __main__:llm:328 - LLM API调用成功
2025-07-03 09:57:30.701 | INFO     | __main__:analyze_document:686 - [
    {
        "bid_name": null,
        "bid_number": "GZZH2025-ND-G002",
        "bid_budget": 7500000.0,
        "fiscal_delegation_number": "宁财购2025F001374870",
        "prj_addr": "宁都县",
        "prj_name": "宁都县传染病医院二期负压病房装修采购项目",
        "prj_number": "GZZH2025-ND-G002",
        "prj_type": "工程",
        "release_time": "2025-07-02 18:35:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "宁都县卫生健康委员会",
        "bid_submission_deadline": "2025-07-24 09:00:00",
        "trade_platform": "江西省公共资源交易平台",
        "procurement_method": "公开招标",
        "prj_sub_type": "其他",
        "province": "江西省",
        "city": "赣州市",
        "county": "宁都县",
        "announcement_type": "001",
        "object_name": "宁都县传染病医院二期负压病房装修",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "详见公告附件",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": 7500000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": "1",
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "赣州正辉项目管理有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 09:57:30.701 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 0KH5zZcBfqDgtia4UG4l_0
2025-07-03 09:57:30.854 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 0KH5zZcBfqDgtia4UG4l_0
2025-07-03 09:57:30.854 | INFO     | __main__:process_one_record:778 - 成功插入文档 0KH5zZcBfqDgtia4UG4l_0
2025-07-03 10:03:35.330 | INFO     | __main__:main:28 - 定时任务已启动，每1秒执行一次...
2025-07-03 10:03:35.330 | INFO     | __main__:main:29 - 按 Ctrl+C 可以停止程序
2025-07-03 10:03:36.333 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 10:03:36
2025-07-03 10:03:36.335 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 10:03:36.336 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 10:03:36.631 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3272 条结果
2025-07-03 10:03:36.631 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 10:03:37.742 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 54 条结果
2025-07-03 10:03:37.742 | INFO     | analyse_noappendix:process_one_record:751 - ID: 0PL-zZcBGyYixO6vxNP-的附件链接为空
2025-07-03 10:03:37.742 | INFO     | analyse_noappendix:process_one_record:752 - 公告标题: 滁州市第一人民医院口腔消毒设备采购项目中标结果公告
2025-07-03 10:03:37.742 | INFO     | analyse_noappendix:process_one_record:753 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202507/t20250702_24893602.htm
2025-07-03 10:03:37.743 | INFO     | analyse_noappendix:process_one_record:754 - 公告类型: 004
2025-07-03 10:03:37.743 | INFO     | analyse_noappendix:process_one_record:755 - 附件链接: []
2025-07-03 10:03:38.811 | INFO     | analyse_noappendix:llm:303 - 正在调用LLM API (尝试 1/3)...
2025-07-03 10:03:38.811 | INFO     | analyse_noappendix:llm:318 - enable_thinking=False
2025-07-03 10:03:39.086 | INFO     | analyse_noappendix:llm:324 - extra_body={'enable_thinking': False}
2025-07-03 10:03:52.469 | INFO     | analyse_noappendix:llm:328 - LLM API调用成功
2025-07-03 10:03:52.469 | INFO     | analyse_noappendix:analyze_document:686 - [
    {
        "bid_name": null,
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "安徽省滁州市南谯区醉翁西路369号",
        "prj_name": "滁州市第一人民医院口腔消毒设备采购项目",
        "prj_number": "czsjcg202505-049",
        "prj_type": "货物",
        "release_time": "2025-07-02 17:30:00",
        "prj_approval_authority": null,
        "superintendent_office": "滁州市公共资源交易监督管理局监督科",
        "superintendent_office_code": null,
        "tenderee": "滁州市第一人民医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "安徽省",
        "city": "滁州市",
        "county": "南谯区",
        "announcement_type": "004",
        "object_name": "口腔消毒设备",
        "object_brand": null,
        "object_model": null,
        "object_supplier": "滁州互通贸易有限公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": null,
        "object_unit": null,
        "object_price": null,
        "object_total_price": 733800.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 733800.0,
        "bidder_name": "滁州互通贸易有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "安徽锐新工程项目管理有限公司",
        "service_fee": 7900.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 10:03:52.469 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 0PL-zZcBGyYixO6vxNP-_0
2025-07-03 10:03:52.479 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 0PL-zZcBGyYixO6vxNP-_0
2025-07-03 10:03:52.479 | INFO     | analyse_noappendix:process_one_record:778 - 成功插入文档 0PL-zZcBGyYixO6vxNP-_0
2025-07-03 10:03:52.479 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 10:03:36
2025-07-03 10:03:53.482 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 10:03:53
2025-07-03 10:03:53.485 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 10:03:53.486 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 10:03:53.677 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3273 条结果
2025-07-03 10:03:53.677 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 10:03:54.566 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 54 条结果
2025-07-03 10:03:54.566 | INFO     | analyse_noappendix:process_one_record:751 - ID: 1lsFzpcBsUtJ06NfCnI_的附件链接为空
2025-07-03 10:03:54.567 | INFO     | analyse_noappendix:process_one_record:752 - 公告标题: ［江西省公共资源交易平台］高安市中医院物业和保安、洗衣房服务管理采购项目高安中心-GA2025-029结果公示
2025-07-03 10:03:54.567 | INFO     | analyse_noappendix:process_one_record:753 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202507/t20250702_24892165.htm
2025-07-03 10:03:54.568 | INFO     | analyse_noappendix:process_one_record:754 - 公告类型: 004
2025-07-03 10:03:54.568 | INFO     | analyse_noappendix:process_one_record:755 - 附件链接: []
2025-07-03 10:03:55.561 | INFO     | analyse_noappendix:llm:303 - 正在调用LLM API (尝试 1/3)...
2025-07-03 10:03:55.561 | INFO     | analyse_noappendix:llm:318 - enable_thinking=False
2025-07-03 10:03:55.561 | INFO     | analyse_noappendix:llm:324 - extra_body={'enable_thinking': False}
2025-07-03 10:04:09.319 | INFO     | analyse_noappendix:llm:328 - LLM API调用成功
2025-07-03 10:04:09.319 | INFO     | analyse_noappendix:analyze_document:686 - [
    {
        "bid_name": null,
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "高安市中山路28号",
        "prj_name": "高安市中医院物业和保安、洗衣房服务管理采购项目",
        "prj_number": "高安中心-GA2025-029",
        "prj_type": "服务",
        "release_time": "2025-07-02 16:36:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "高安市中医院",
        "bid_submission_deadline": null,
        "trade_platform": "江西省公共资源交易平台",
        "procurement_method": null,
        "prj_sub_type": "其他",
        "province": "江西省",
        "city": "宜春市",
        "county": "高安市",
        "announcement_type": "004",
        "object_name": "高安市中医院物业和保安、洗衣房服务",
        "object_brand": null,
        "object_model": null,
        "object_supplier": "金科智慧服务集团股份有限公司",
        "object_produce_area": null,
        "object_conf": "详见招标文件",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": 2453370.00,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 2453370.00,
        "bidder_name": "金科智慧服务集团股份有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": "15079611545",
        "bidder_contract_config_param": null,
        "agent": "高安市政府采购中心",
        "service_fee": 21302.00,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 10:04:09.319 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 1lsFzpcBsUtJ06NfCnI__0
2025-07-03 10:04:09.338 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 1lsFzpcBsUtJ06NfCnI__0
2025-07-03 10:04:09.338 | INFO     | analyse_noappendix:process_one_record:778 - 成功插入文档 1lsFzpcBsUtJ06NfCnI__0
2025-07-03 10:04:09.338 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 10:03:53
2025-07-03 10:04:10.340 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 10:04:10
2025-07-03 10:04:10.343 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 10:04:10.343 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 10:04:10.747 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3274 条结果
2025-07-03 10:04:10.747 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 10:04:11.790 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 54 条结果
2025-07-03 10:04:11.790 | INFO     | analyse_noappendix:process_one_record:751 - ID: -FsGzpcBsUtJ06NfvXJ2的附件链接为空
2025-07-03 10:04:11.790 | INFO     | analyse_noappendix:process_one_record:752 - 公告标题: 山东大学齐鲁医院移动推车工作站、PDA终端维修服务成交公告
2025-07-03 10:04:11.790 | INFO     | analyse_noappendix:process_one_record:753 - 公告链接: http://www.ccgp.gov.cn/cggg/zygg/cjgg/202507/t20250702_24892445.htm
2025-07-03 10:04:11.790 | INFO     | analyse_noappendix:process_one_record:754 - 公告类型: 004
2025-07-03 10:04:11.790 | INFO     | analyse_noappendix:process_one_record:755 - 附件链接: []
2025-07-03 10:04:12.622 | INFO     | analyse_noappendix:llm:303 - 正在调用LLM API (尝试 1/3)...
2025-07-03 10:04:12.627 | INFO     | analyse_noappendix:llm:318 - enable_thinking=False
2025-07-03 10:04:12.627 | INFO     | analyse_noappendix:llm:324 - extra_body={'enable_thinking': False}
2025-07-03 10:04:27.336 | INFO     | analyse_noappendix:llm:328 - LLM API调用成功
2025-07-03 10:04:27.336 | INFO     | analyse_noappendix:analyze_document:686 - [
    {
        "bid_name": null,
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "济南市文化西路107号",
        "prj_name": "山东大学齐鲁医院移动推车工作站、PDA终端维修服务",
        "prj_number": "0627-25082591105",
        "prj_type": "服务",
        "release_time": "2025-07-02 16:24:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "山东大学齐鲁医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": "竞争性磋商",
        "prj_sub_type": "维保",
        "province": "山东省",
        "city": "济南市",
        "county": null,
        "announcement_type": "004",
        "object_name": "移动推车工作站、PDA终端维修服务",
        "object_brand": null,
        "object_model": null,
        "object_supplier": "山东博克森科贸有限公司",
        "object_produce_area": null,
        "object_conf": "详见竞争性磋商文件",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": 487000.0,
        "object_maintenance_period": "1年",
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 487000.0,
        "bidder_name": "山东博克森科贸有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "山东招标股份有限公司",
        "service_fee": 5114.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 10:04:27.341 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -FsGzpcBsUtJ06NfvXJ2_0
2025-07-03 10:04:27.351 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -FsGzpcBsUtJ06NfvXJ2_0
2025-07-03 10:04:27.351 | INFO     | analyse_noappendix:process_one_record:778 - 成功插入文档 -FsGzpcBsUtJ06NfvXJ2_0
2025-07-03 10:04:27.351 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 10:04:10
2025-07-03 10:04:28.355 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 10:04:28
2025-07-03 10:04:28.359 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 10:04:28.360 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 10:04:28.623 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3275 条结果
2025-07-03 10:04:28.623 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 10:04:28.685 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 55 条结果
2025-07-03 10:04:28.685 | INFO     | analyse_noappendix:process_one_record:751 - ID: 2PL5zZcBGyYixO6vgc13的附件链接为空
2025-07-03 10:04:28.685 | INFO     | analyse_noappendix:process_one_record:752 - 公告标题: ［江西省公共资源交易平台］宜春市人民医院采购创伤全碳素钢手术床（牵引床、手外台）项目
2025-07-03 10:04:28.685 | INFO     | analyse_noappendix:process_one_record:753 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/xjgg/202507/t20250702_24894590.htm
2025-07-03 10:04:28.685 | INFO     | analyse_noappendix:process_one_record:754 - 公告类型: 001
2025-07-03 10:04:28.685 | INFO     | analyse_noappendix:process_one_record:755 - 附件链接: []
2025-07-03 10:04:29.611 | INFO     | analyse_noappendix:llm:303 - 正在调用LLM API (尝试 1/3)...
2025-07-03 10:04:29.616 | INFO     | analyse_noappendix:llm:318 - enable_thinking=False
2025-07-03 10:04:29.616 | INFO     | analyse_noappendix:llm:324 - extra_body={'enable_thinking': False}
2025-07-03 10:04:44.399 | INFO     | analyse_noappendix:llm:328 - LLM API调用成功
2025-07-03 10:04:44.399 | INFO     | analyse_noappendix:analyze_document:686 - [
    {
        "bid_name": null,
        "bid_number": null,
        "bid_budget": 1200000.0,
        "fiscal_delegation_number": "宜购2023F000981024",
        "prj_addr": "宜春市宜阳新区锦绣大道1061号",
        "prj_name": "宜春市人民医院采购创伤全碳素钢手术床（牵引床、手外台）项目",
        "prj_number": "机电-YC2025-11-01",
        "prj_type": "货物",
        "release_time": "2025-07-02 18:27:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "宜春市人民医院",
        "bid_submission_deadline": "2025-07-15 09:00:00",
        "trade_platform": "江西省公共资源交易平台",
        "procurement_method": "询价",
        "prj_sub_type": "设备",
        "province": "江西省",
        "city": "宜春市",
        "county": null,
        "announcement_type": "001",
        "object_name": "创伤全碳素钢手术床（牵引床、手外台）",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "详见公告附件",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "套",
        "object_price": null,
        "object_total_price": 1200000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": "1",
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "江西省机电设备招标有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 10:04:44.404 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 2PL5zZcBGyYixO6vgc13_0
2025-07-03 10:04:44.417 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 2PL5zZcBGyYixO6vgc13_0
2025-07-03 10:04:44.418 | INFO     | analyse_noappendix:process_one_record:778 - 成功插入文档 2PL5zZcBGyYixO6vgc13_0
2025-07-03 10:04:44.420 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 10:04:28
2025-07-03 10:04:44.953 | INFO     | __main__:main:37 - 程序被用户中断
2025-07-03 10:18:34.050 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 10:18:34.054 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 10:18:35.071 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3276 条结果
2025-07-03 10:18:35.071 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 10:18:37.559 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7255 条结果
2025-07-03 10:18:37.559 | INFO     | __main__:process_one_record:1907 - ID: -1o4r5cBsUtJ06NfkR31 开始分析公告主体
2025-07-03 10:18:37.559 | INFO     | __main__:process_one_record:1908 - 公告标题: 巴楚县中医医院中草药饮片采购项目中标(成交)结果公告
2025-07-03 10:18:37.559 | INFO     | __main__:process_one_record:1909 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250626_24849935.htm
2025-07-03 10:18:38.695 | INFO     | __main__:llm:563 - 正在调用LLM API (尝试 1/3)...
2025-07-03 10:18:38.695 | INFO     | __main__:llm:578 - enable_thinking=False
2025-07-03 10:18:39.038 | INFO     | __main__:llm:584 - extra_body={'enable_thinking': False}
2025-07-03 11:10:22.440 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 11:10:22.450 | INFO     | __main__:__init__:1487 - 黑名单中有 1 个文档将被跳过
2025-07-03 11:10:22.451 | INFO     | __main__:__init__:1489 - 今日新增黑名单文档: 1 个
2025-07-03 11:10:22.451 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 11:10:22.631 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3276 条结果
2025-07-03 11:10:22.631 | INFO     | __main__:process_one_record:1895 - 排除 1 个黑名单文档
2025-07-03 11:10:22.631 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 11:10:23.256 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7350 条结果
2025-07-03 11:10:23.256 | INFO     | __main__:process_one_record:1907 - ID: -1pfuZcBsUtJ06NfZI4t 开始分析公告主体
2025-07-03 11:10:23.256 | INFO     | __main__:process_one_record:1908 - 公告标题: 新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告
2025-07-03 11:10:23.256 | INFO     | __main__:process_one_record:1909 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm
2025-07-03 11:10:24.176 | INFO     | __main__:llm:563 - 正在调用LLM API (尝试 1/3)...
2025-07-03 11:10:24.181 | INFO     | __main__:llm:578 - enable_thinking=False
2025-07-03 11:10:24.471 | INFO     | __main__:llm:584 - extra_body={'enable_thinking': False}
2025-07-03 11:10:40.287 | INFO     | __main__:llm:588 - LLM API调用成功
2025-07-03 11:10:40.292 | INFO     | __main__:analyze_content:1842 - [
    {
        "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "bid_number": "XJDH-YKD2025-076",
        "bid_budget": 50000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐河南西路118号",
        "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "prj_number": "XJDH-YKD2025-076",
        "prj_type": "货物",
        "release_time": "2025-06-28 18:44:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "新疆医科大学第五附属医院",
        "bid_submission_deadline": "2025-07-22 11:00:00",
        "trade_platform": "政采云平台",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": null,
        "announcement_type": "001",
        "object_name": "可调式支撑喉镜",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 50000.0,
        "object_total_price": 50000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "新疆德鸿项目管理咨询有限公司",
        "service_fee": 0.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 11:10:40.292 | INFO     | __main__:process_one_record:1980 - ID: -1pfuZcBsUtJ06NfZI4t 发现附件，开始分析附件
2025-07-03 11:10:40.292 | INFO     | __main__:process_one_record:1981 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc', 'text': '公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜'}]
2025-07-03 11:10:40.292 | INFO     | __main__:process_one_record:1992 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:10:40.292 | INFO     | __main__:download_file:628 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:10:40.496 | INFO     | __main__:download_file:659 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:10:40.498 | INFO     | __main__:process_one_record:2034 - 附件已保存到: downloads\公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:10:40.498 | INFO     | __main__:process_one_record:2058 - 使用markitdown转换文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:10:40.591 | ERROR    | __main__:convert_to_markdown_with_markitdown:783 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 11:10:40.592 | INFO     | __main__:process_one_record:2363 - ================================================================================
2025-07-03 11:10:40.592 | INFO     | __main__:process_one_record:2364 - 开始融合分析结果
2025-07-03 11:10:40.593 | INFO     | __main__:process_one_record:2365 - ================================================================================
2025-07-03 11:10:40.593 | INFO     | __main__:process_one_record:2366 - 主体解析结果数量: 1
2025-07-03 11:10:40.593 | INFO     | __main__:process_one_record:2367 - 招标文件解析结果数量: 0
2025-07-03 11:10:40.594 | INFO     | __main__:process_one_record:2368 - 合同文件解析结果数量: 0
2025-07-03 11:10:40.594 | INFO     | __main__:process_one_record:2371 - 主体解析结果:
2025-07-03 11:10:40.594 | INFO     | __main__:process_one_record:2373 - 主体结果 1:
2025-07-03 11:10:40.595 | INFO     | __main__:process_one_record:2374 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": 0.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:10:40"
}
2025-07-03 11:10:40.597 | INFO     | __main__:process_one_record:2398 - 使用智能融合分析
2025-07-03 11:10:40.597 | INFO     | __main__:intelligent_merge_analysis:1301 - 开始基于object_name的匹配融合分析
2025-07-03 11:10:40.598 | INFO     | __main__:merge_analysis_by_object_name:1259 - 主体标的物 '可调式支撑喉镜' 匹配结果:
2025-07-03 11:10:40.599 | INFO     | __main__:merge_analysis_by_object_name:1260 -   - 招标文件匹配: 否
2025-07-03 11:10:40.599 | INFO     | __main__:merge_analysis_by_object_name:1261 -   - 合同文件匹配: 否
2025-07-03 11:10:40.599 | INFO     | __main__:intelligent_merge_analysis:1309 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 11:10:40.599 | INFO     | __main__:intelligent_merge_analysis:1438 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 11:10:40.601 | INFO     | __main__:process_one_record:2413 - ================================================================================
2025-07-03 11:10:40.601 | INFO     | __main__:process_one_record:2414 - 最终融合后的JSON结果列表:
2025-07-03 11:10:40.602 | INFO     | __main__:process_one_record:2415 - ================================================================================
2025-07-03 11:10:40.602 | INFO     | __main__:process_one_record:2417 - 融合结果 1:
2025-07-03 11:10:40.602 | INFO     | __main__:process_one_record:2418 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": 0.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:10:40"
}
2025-07-03 11:10:40.603 | INFO     | __main__:process_one_record:2419 - ------------------------------------------------------------
2025-07-03 11:10:40.603 | INFO     | __main__:process_one_record:2420 - ================================================================================
2025-07-03 11:10:40.604 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:10:40.626 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:10:40.627 | INFO     | __main__:process_one_record:2431 - 成功插入融合文档 -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:18:19.776 | DEBUG    | analyse_appendix:parse_doc:852 - docx2txt不可用，尝试其他方法
2025-07-03 11:18:19.785 | DEBUG    | analyse_appendix:parse_doc:888 - antiword命令未找到，跳过此方法
2025-07-03 11:18:19.785 | INFO     | analyse_appendix:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:18:19.906 | DEBUG    | analyse_appendix:convert_to_markdown_with_markitdown:769 - markitdown成功转换.doc文件，生成54字符的Markdown
2025-07-03 11:18:19.977 | DEBUG    | analyse_appendix:convert_to_markdown_with_markitdown:769 - markitdown成功转换.pdf文件，生成12字符的Markdown
2025-07-03 11:18:20.043 | DEBUG    | analyse_appendix:convert_to_markdown_with_markitdown:769 - markitdown成功转换.docx文件，生成12字符的Markdown
2025-07-03 11:18:20.096 | DEBUG    | analyse_appendix:convert_to_markdown_with_markitdown:769 - markitdown成功转换.doc文件，生成12字符的Markdown
2025-07-03 11:18:20.143 | DEBUG    | analyse_appendix:convert_to_markdown_with_markitdown:769 - markitdown成功转换.txt文件，生成12字符的Markdown
2025-07-03 11:18:20.206 | DEBUG    | analyse_appendix:convert_to_markdown_with_markitdown:769 - markitdown成功转换.unknown文件，生成12字符的Markdown
2025-07-03 11:19:04.962 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 11:19:04.966 | INFO     | __main__:__init__:1559 - 黑名单中有 1 个文档将被跳过
2025-07-03 11:19:04.966 | INFO     | __main__:__init__:1561 - 今日新增黑名单文档: 1 个
2025-07-03 11:19:04.967 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 11:19:06.167 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3276 条结果
2025-07-03 11:19:06.170 | INFO     | __main__:process_one_record:1967 - 排除 1 个黑名单文档
2025-07-03 11:19:06.170 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 11:19:07.068 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7350 条结果
2025-07-03 11:19:07.068 | INFO     | __main__:process_one_record:1979 - ID: -1pfuZcBsUtJ06NfZI4t 开始分析公告主体
2025-07-03 11:19:07.068 | INFO     | __main__:process_one_record:1980 - 公告标题: 新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告
2025-07-03 11:19:07.069 | INFO     | __main__:process_one_record:1981 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm
2025-07-03 11:19:08.319 | INFO     | __main__:llm:563 - 正在调用LLM API (尝试 1/3)...
2025-07-03 11:19:08.319 | INFO     | __main__:llm:578 - enable_thinking=False
2025-07-03 11:19:08.647 | INFO     | __main__:llm:584 - extra_body={'enable_thinking': False}
2025-07-03 11:19:26.411 | INFO     | __main__:llm:588 - LLM API调用成功
2025-07-03 11:19:26.411 | INFO     | __main__:analyze_content:1914 - [
    {
        "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "bid_number": "XJDH-YKD2025-076",
        "bid_budget": 50000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐河南西路118号",
        "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "prj_number": "XJDH-YKD2025-076",
        "prj_type": "货物",
        "release_time": "2025-06-28 18:44:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "新疆医科大学第五附属医院",
        "bid_submission_deadline": "2025-07-22 11:00:00",
        "trade_platform": "政采云平台",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": null,
        "announcement_type": "001",
        "object_name": "可调式支撑喉镜",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 50000.0,
        "object_total_price": 50000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "新疆德鸿项目管理咨询有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 11:19:26.413 | INFO     | __main__:process_one_record:2052 - ID: -1pfuZcBsUtJ06NfZI4t 发现附件，开始分析附件
2025-07-03 11:19:26.413 | INFO     | __main__:process_one_record:2053 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc', 'text': '公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜'}]
2025-07-03 11:19:26.413 | INFO     | __main__:process_one_record:2064 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:19:26.414 | INFO     | __main__:download_file:628 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:19:26.584 | INFO     | __main__:download_file:659 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:19:26.586 | INFO     | __main__:process_one_record:2106 - 附件已保存到: downloads\公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:19:26.586 | INFO     | __main__:process_one_record:2130 - 使用markitdown转换文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:19:26.704 | ERROR    | __main__:convert_to_markdown_with_markitdown:785 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 11:19:26.705 | INFO     | __main__:convert_to_markdown_with_markitdown:787 - 回退到传统方法解析.doc文件
2025-07-03 11:19:26.712 | WARNING  | __main__:parse_doc:854 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 11:19:26.724 | DEBUG    | __main__:parse_doc:888 - antiword命令未找到，跳过此方法
2025-07-03 11:19:26.752 | INFO     | __main__:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:19:26.754 | INFO     | __main__:process_one_record:2172 - 正在分析文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:19:26.757 | WARNING  | __main__:parse_doc:854 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 11:19:26.766 | DEBUG    | __main__:parse_doc:888 - antiword命令未找到，跳过此方法
2025-07-03 11:19:26.797 | INFO     | __main__:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:19:26.799 | INFO     | __main__:detect_file_type:1120 - 此文件是其他文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜
2025-07-03 11:19:26.800 | INFO     | __main__:process_one_record:2256 - 跳过非招标/合同文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:19:26.800 | INFO     | __main__:process_one_record:2435 - ================================================================================
2025-07-03 11:19:26.800 | INFO     | __main__:process_one_record:2436 - 开始融合分析结果
2025-07-03 11:19:26.800 | INFO     | __main__:process_one_record:2437 - ================================================================================
2025-07-03 11:19:26.802 | INFO     | __main__:process_one_record:2438 - 主体解析结果数量: 1
2025-07-03 11:19:26.802 | INFO     | __main__:process_one_record:2439 - 招标文件解析结果数量: 0
2025-07-03 11:19:26.802 | INFO     | __main__:process_one_record:2440 - 合同文件解析结果数量: 0
2025-07-03 11:19:26.803 | INFO     | __main__:process_one_record:2443 - 主体解析结果:
2025-07-03 11:19:26.803 | INFO     | __main__:process_one_record:2445 - 主体结果 1:
2025-07-03 11:19:26.803 | INFO     | __main__:process_one_record:2446 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:19:26"
}
2025-07-03 11:19:26.805 | INFO     | __main__:process_one_record:2470 - 使用智能融合分析
2025-07-03 11:19:26.805 | INFO     | __main__:intelligent_merge_analysis:1373 - 开始基于object_name的匹配融合分析
2025-07-03 11:19:26.805 | INFO     | __main__:merge_analysis_by_object_name:1331 - 主体标的物 '可调式支撑喉镜' 匹配结果:
2025-07-03 11:19:26.806 | INFO     | __main__:merge_analysis_by_object_name:1332 -   - 招标文件匹配: 否
2025-07-03 11:19:26.806 | INFO     | __main__:merge_analysis_by_object_name:1333 -   - 合同文件匹配: 否
2025-07-03 11:19:26.807 | INFO     | __main__:intelligent_merge_analysis:1381 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 11:19:26.807 | INFO     | __main__:intelligent_merge_analysis:1510 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 11:19:26.807 | INFO     | __main__:process_one_record:2485 - ================================================================================
2025-07-03 11:19:26.807 | INFO     | __main__:process_one_record:2486 - 最终融合后的JSON结果列表:
2025-07-03 11:19:26.809 | INFO     | __main__:process_one_record:2487 - ================================================================================
2025-07-03 11:19:26.809 | INFO     | __main__:process_one_record:2489 - 融合结果 1:
2025-07-03 11:19:26.809 | INFO     | __main__:process_one_record:2490 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:19:26"
}
2025-07-03 11:19:26.810 | INFO     | __main__:process_one_record:2491 - ------------------------------------------------------------
2025-07-03 11:19:26.810 | INFO     | __main__:process_one_record:2492 - ================================================================================
2025-07-03 11:19:26.811 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:19:26.833 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:19:26.834 | INFO     | __main__:process_one_record:2503 - 成功插入融合文档 -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:21:38.109 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 11:21:38.113 | INFO     | __main__:__init__:1559 - 黑名单中有 1 个文档将被跳过
2025-07-03 11:21:38.113 | INFO     | __main__:__init__:1561 - 今日新增黑名单文档: 1 个
2025-07-03 11:21:38.114 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 11:21:38.497 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3277 条结果
2025-07-03 11:21:38.500 | INFO     | __main__:process_one_record:1967 - 排除 1 个黑名单文档
2025-07-03 11:21:38.500 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 11:21:39.171 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7349 条结果
2025-07-03 11:21:39.171 | INFO     | __main__:process_one_record:1979 - ID: -1v4zZcBsUtJ06Nf1XEk 开始分析公告主体
2025-07-03 11:21:39.172 | INFO     | __main__:process_one_record:1980 - 公告标题: 榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告
2025-07-03 11:21:39.172 | INFO     | __main__:process_one_record:1981 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm
2025-07-03 11:21:40.169 | INFO     | __main__:llm:563 - 正在调用LLM API (尝试 1/3)...
2025-07-03 11:21:40.169 | INFO     | __main__:llm:578 - enable_thinking=False
2025-07-03 11:21:40.491 | INFO     | __main__:llm:584 - extra_body={'enable_thinking': False}
2025-07-03 11:22:30.203 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 11:22:30.208 | INFO     | __main__:__init__:1559 - 黑名单中有 1 个文档将被跳过
2025-07-03 11:22:30.208 | INFO     | __main__:__init__:1561 - 今日新增黑名单文档: 1 个
2025-07-03 11:22:30.208 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 11:22:30.530 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3276 条结果
2025-07-03 11:22:30.532 | INFO     | __main__:process_one_record:1967 - 排除 1 个黑名单文档
2025-07-03 11:22:30.533 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 11:22:31.424 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7350 条结果
2025-07-03 11:22:31.425 | INFO     | __main__:process_one_record:1979 - ID: -1pfuZcBsUtJ06NfZI4t 开始分析公告主体
2025-07-03 11:22:31.425 | INFO     | __main__:process_one_record:1980 - 公告标题: 新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告
2025-07-03 11:22:31.425 | INFO     | __main__:process_one_record:1981 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm
2025-07-03 11:22:32.336 | INFO     | __main__:llm:563 - 正在调用LLM API (尝试 1/3)...
2025-07-03 11:22:32.336 | INFO     | __main__:llm:578 - enable_thinking=False
2025-07-03 11:22:32.646 | INFO     | __main__:llm:584 - extra_body={'enable_thinking': False}
2025-07-03 11:22:48.993 | INFO     | __main__:llm:588 - LLM API调用成功
2025-07-03 11:22:48.993 | INFO     | __main__:analyze_content:1914 - [
    {
        "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "bid_number": "XJDH-YKD2025-076",
        "bid_budget": 50000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐河南西路118号",
        "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "prj_number": "XJDH-YKD2025-076",
        "prj_type": "货物",
        "release_time": "2025-06-28 18:44:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "新疆医科大学第五附属医院",
        "bid_submission_deadline": "2025-07-22 11:00:00",
        "trade_platform": "政采云平台",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": null,
        "announcement_type": "001",
        "object_name": "可调式支撑喉镜",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 50000.0,
        "object_total_price": 50000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "新疆德鸿项目管理咨询有限公司",
        "service_fee": 0.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 11:22:48.994 | INFO     | __main__:process_one_record:2052 - ID: -1pfuZcBsUtJ06NfZI4t 发现附件，开始分析附件
2025-07-03 11:22:48.994 | INFO     | __main__:process_one_record:2053 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc', 'text': '公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜'}]
2025-07-03 11:22:48.995 | INFO     | __main__:process_one_record:2064 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:22:48.995 | INFO     | __main__:download_file:628 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:22:49.309 | INFO     | __main__:download_file:659 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:22:49.311 | INFO     | __main__:process_one_record:2106 - 附件已保存到: downloads\公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:22:49.312 | INFO     | __main__:process_one_record:2130 - 使用markitdown转换文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:22:49.411 | ERROR    | __main__:convert_to_markdown_with_markitdown:785 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 11:22:49.412 | INFO     | __main__:convert_to_markdown_with_markitdown:787 - 回退到传统方法解析.doc文件
2025-07-03 11:22:49.418 | WARNING  | __main__:parse_doc:854 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 11:22:49.425 | DEBUG    | __main__:parse_doc:888 - antiword命令未找到，跳过此方法
2025-07-03 11:22:49.464 | INFO     | __main__:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:22:49.466 | INFO     | __main__:process_one_record:2172 - 正在分析文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:22:49.468 | WARNING  | __main__:parse_doc:854 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 11:22:49.475 | DEBUG    | __main__:parse_doc:888 - antiword命令未找到，跳过此方法
2025-07-03 11:22:49.500 | INFO     | __main__:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:22:49.502 | INFO     | __main__:detect_file_type:1120 - 此文件是其他文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜
2025-07-03 11:22:49.502 | INFO     | __main__:process_one_record:2256 - 跳过非招标/合同文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:22:49.502 | INFO     | __main__:process_one_record:2435 - ================================================================================
2025-07-03 11:22:49.503 | INFO     | __main__:process_one_record:2436 - 开始融合分析结果
2025-07-03 11:22:49.503 | INFO     | __main__:process_one_record:2437 - ================================================================================
2025-07-03 11:22:49.503 | INFO     | __main__:process_one_record:2438 - 主体解析结果数量: 1
2025-07-03 11:22:49.503 | INFO     | __main__:process_one_record:2439 - 招标文件解析结果数量: 0
2025-07-03 11:22:49.504 | INFO     | __main__:process_one_record:2440 - 合同文件解析结果数量: 0
2025-07-03 11:22:49.504 | INFO     | __main__:process_one_record:2443 - 主体解析结果:
2025-07-03 11:22:49.504 | INFO     | __main__:process_one_record:2445 - 主体结果 1:
2025-07-03 11:22:49.505 | INFO     | __main__:process_one_record:2446 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": 0.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:22:48"
}
2025-07-03 11:22:49.505 | INFO     | __main__:process_one_record:2470 - 使用智能融合分析
2025-07-03 11:22:49.506 | INFO     | __main__:intelligent_merge_analysis:1373 - 开始基于object_name的匹配融合分析
2025-07-03 11:22:49.506 | INFO     | __main__:merge_analysis_by_object_name:1331 - 主体标的物 '可调式支撑喉镜' 匹配结果:
2025-07-03 11:22:49.506 | INFO     | __main__:merge_analysis_by_object_name:1332 -   - 招标文件匹配: 否
2025-07-03 11:22:49.507 | INFO     | __main__:merge_analysis_by_object_name:1333 -   - 合同文件匹配: 否
2025-07-03 11:22:49.507 | INFO     | __main__:intelligent_merge_analysis:1381 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 11:22:49.507 | INFO     | __main__:intelligent_merge_analysis:1510 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 11:22:49.508 | INFO     | __main__:process_one_record:2485 - ================================================================================
2025-07-03 11:22:49.508 | INFO     | __main__:process_one_record:2486 - 最终融合后的JSON结果列表:
2025-07-03 11:22:49.508 | INFO     | __main__:process_one_record:2487 - ================================================================================
2025-07-03 11:22:49.508 | INFO     | __main__:process_one_record:2489 - 融合结果 1:
2025-07-03 11:22:49.509 | INFO     | __main__:process_one_record:2490 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": 0.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:22:48"
}
2025-07-03 11:22:49.509 | INFO     | __main__:process_one_record:2491 - ------------------------------------------------------------
2025-07-03 11:22:49.510 | INFO     | __main__:process_one_record:2492 - ================================================================================
2025-07-03 11:22:49.510 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:22:49.618 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:22:49.619 | INFO     | __main__:process_one_record:2503 - 成功插入融合文档 -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:23:56.385 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 11:23:56.389 | INFO     | __main__:__init__:1559 - 黑名单中有 1 个文档将被跳过
2025-07-03 11:23:56.389 | INFO     | __main__:__init__:1561 - 今日新增黑名单文档: 1 个
2025-07-03 11:23:56.389 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 11:23:56.691 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3276 条结果
2025-07-03 11:23:56.693 | INFO     | __main__:process_one_record:1967 - 排除 1 个黑名单文档
2025-07-03 11:23:56.693 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 11:23:57.392 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7350 条结果
2025-07-03 11:23:57.392 | INFO     | __main__:process_one_record:1979 - ID: -1pfuZcBsUtJ06NfZI4t 开始分析公告主体
2025-07-03 11:23:57.392 | INFO     | __main__:process_one_record:1980 - 公告标题: 新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告
2025-07-03 11:23:57.393 | INFO     | __main__:process_one_record:1981 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm
2025-07-03 11:23:58.464 | INFO     | __main__:llm:563 - 正在调用LLM API (尝试 1/3)...
2025-07-03 11:23:58.464 | INFO     | __main__:llm:578 - enable_thinking=False
2025-07-03 11:23:58.765 | INFO     | __main__:llm:584 - extra_body={'enable_thinking': False}
2025-07-03 11:24:12.956 | INFO     | __main__:llm:588 - LLM API调用成功
2025-07-03 11:24:12.957 | INFO     | __main__:analyze_content:1914 - [
    {
        "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "bid_number": "XJDH-YKD2025-076",
        "bid_budget": 50000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐河南西路118号",
        "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "prj_number": "XJDH-YKD2025-076",
        "prj_type": "货物",
        "release_time": "2025-06-28 18:44:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "新疆医科大学第五附属医院",
        "bid_submission_deadline": "2025-07-22 11:00:00",
        "trade_platform": "政采云平台",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": null,
        "announcement_type": "001",
        "object_name": "可调式支撑喉镜",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 50000.0,
        "object_total_price": 50000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "新疆德鸿项目管理咨询有限公司",
        "service_fee": 0.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 11:24:12.957 | INFO     | __main__:process_one_record:2052 - ID: -1pfuZcBsUtJ06NfZI4t 发现附件，开始分析附件
2025-07-03 11:24:12.959 | INFO     | __main__:process_one_record:2053 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc', 'text': '公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜'}]
2025-07-03 11:24:12.959 | INFO     | __main__:process_one_record:2064 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:24:12.959 | INFO     | __main__:download_file:628 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:24:13.136 | INFO     | __main__:download_file:659 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:24:13.139 | INFO     | __main__:process_one_record:2106 - 附件已保存到: downloads\公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:24:13.139 | INFO     | __main__:process_one_record:2130 - 使用markitdown转换文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 11:24:13.253 | ERROR    | __main__:convert_to_markdown_with_markitdown:785 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 11:24:13.254 | INFO     | __main__:convert_to_markdown_with_markitdown:787 - 回退到传统方法解析.doc文件
2025-07-03 11:24:13.258 | WARNING  | __main__:parse_doc:854 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 11:24:14.583 | WARNING  | __main__:parse_doc:883 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -m C:\Users\<USER>\AppData\Local\Temp\tmpmt_wdpf2.doc

2025-07-03 11:24:14.607 | INFO     | __main__:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:24:14.608 | INFO     | __main__:process_one_record:2172 - 正在分析文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:24:14.610 | WARNING  | __main__:parse_doc:854 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 11:24:14.883 | WARNING  | __main__:parse_doc:883 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -m C:\Users\<USER>\AppData\Local\Temp\tmp0bxrvyhx.doc

2025-07-03 11:24:14.914 | INFO     | __main__:parse_doc:913 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:24:14.916 | INFO     | __main__:detect_file_type:1120 - 此文件是其他文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜
2025-07-03 11:24:14.916 | INFO     | __main__:process_one_record:2256 - 跳过非招标/合同文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 11:24:14.916 | INFO     | __main__:process_one_record:2435 - ================================================================================
2025-07-03 11:24:14.916 | INFO     | __main__:process_one_record:2436 - 开始融合分析结果
2025-07-03 11:24:14.918 | INFO     | __main__:process_one_record:2437 - ================================================================================
2025-07-03 11:24:14.918 | INFO     | __main__:process_one_record:2438 - 主体解析结果数量: 1
2025-07-03 11:24:14.918 | INFO     | __main__:process_one_record:2439 - 招标文件解析结果数量: 0
2025-07-03 11:24:14.918 | INFO     | __main__:process_one_record:2440 - 合同文件解析结果数量: 0
2025-07-03 11:24:14.919 | INFO     | __main__:process_one_record:2443 - 主体解析结果:
2025-07-03 11:24:14.919 | INFO     | __main__:process_one_record:2445 - 主体结果 1:
2025-07-03 11:24:14.919 | INFO     | __main__:process_one_record:2446 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": 0.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:24:12"
}
2025-07-03 11:24:14.920 | INFO     | __main__:process_one_record:2470 - 使用智能融合分析
2025-07-03 11:24:14.920 | INFO     | __main__:intelligent_merge_analysis:1373 - 开始基于object_name的匹配融合分析
2025-07-03 11:24:14.921 | INFO     | __main__:merge_analysis_by_object_name:1331 - 主体标的物 '可调式支撑喉镜' 匹配结果:
2025-07-03 11:24:14.921 | INFO     | __main__:merge_analysis_by_object_name:1332 -   - 招标文件匹配: 否
2025-07-03 11:24:14.921 | INFO     | __main__:merge_analysis_by_object_name:1333 -   - 合同文件匹配: 否
2025-07-03 11:24:14.921 | INFO     | __main__:intelligent_merge_analysis:1381 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 11:24:14.921 | INFO     | __main__:intelligent_merge_analysis:1510 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 11:24:14.923 | INFO     | __main__:process_one_record:2485 - ================================================================================
2025-07-03 11:24:14.923 | INFO     | __main__:process_one_record:2486 - 最终融合后的JSON结果列表:
2025-07-03 11:24:14.923 | INFO     | __main__:process_one_record:2487 - ================================================================================
2025-07-03 11:24:14.924 | INFO     | __main__:process_one_record:2489 - 融合结果 1:
2025-07-03 11:24:14.924 | INFO     | __main__:process_one_record:2490 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": 0.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 11:24:12"
}
2025-07-03 11:24:14.925 | INFO     | __main__:process_one_record:2491 - ------------------------------------------------------------
2025-07-03 11:24:14.925 | INFO     | __main__:process_one_record:2492 - ================================================================================
2025-07-03 11:24:14.925 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:24:15.055 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:24:15.055 | INFO     | __main__:process_one_record:2503 - 成功插入融合文档 -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 11:29:10.888 | WARNING  | analyse_appendix:parse_doc:869 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 11:29:11.633 | WARNING  | analyse_appendix:parse_doc:913 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmp9wwzhvpz.doc

2025-07-03 11:29:11.633 | INFO     | analyse_appendix:parse_doc:945 - 使用基本文本提取方法解析.doc文件
2025-07-03 11:29:11.636 | INFO     | analyse_appendix:detect_file_type:1151 - 此文件是招标文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜 (匹配关键词: ['公开文件'])
2025-07-03 11:29:11.637 | INFO     | analyse_appendix:detect_file_type:1151 - 此文件是招标文件: 招标文件_设备采购 (匹配关键词: ['招标文件', '技术规范书'])
2025-07-03 11:29:11.637 | INFO     | analyse_appendix:detect_file_type:1159 - 此文件是合同文件: 采购合同_医疗设备 (匹配关键词: ['合同', '服务协议'])
2025-07-03 11:29:11.638 | INFO     | analyse_appendix:detect_file_type:1151 - 此文件是招标文件: 医疗器械采购需求清单 (匹配关键词: ['采购需求'])
2025-07-03 11:29:11.638 | INFO     | analyse_appendix:detect_file_type:1159 - 此文件是合同文件: 中标通知书及合同 (匹配关键词: ['合同'])
2025-07-03 11:29:11.639 | WARNING  | analyse_appendix:detect_file_type:1166 - 文件内容解析失败或内容过少，基于文件名进行判断: 用户手册说明书
2025-07-03 11:29:11.639 | INFO     | analyse_appendix:detect_file_type:1215 - 此文件是其他文件: 用户手册说明书
2025-07-03 12:31:46.293 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:31:46.299 | INFO     | __main__:__init__:1647 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:31:46.299 | INFO     | __main__:__init__:1649 - 今日新增黑名单文档: 1 个
2025-07-03 12:31:46.299 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:31:46.499 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3277 条结果
2025-07-03 12:31:46.502 | INFO     | __main__:process_one_record:2055 - 排除 1 个黑名单文档
2025-07-03 12:31:46.502 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:31:47.994 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7349 条结果
2025-07-03 12:31:47.994 | INFO     | __main__:process_one_record:2067 - ID: -1v4zZcBsUtJ06Nf1XEk 开始分析公告主体
2025-07-03 12:31:47.994 | INFO     | __main__:process_one_record:2068 - 公告标题: 榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告
2025-07-03 12:31:47.994 | INFO     | __main__:process_one_record:2069 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm
2025-07-03 12:31:50.112 | INFO     | __main__:llm:585 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:31:50.112 | INFO     | __main__:llm:600 - enable_thinking=False
2025-07-03 12:31:50.759 | INFO     | __main__:llm:606 - extra_body={'enable_thinking': False}
2025-07-03 12:32:42.670 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:32:42.675 | INFO     | __main__:__init__:1647 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:32:42.675 | INFO     | __main__:__init__:1649 - 今日新增黑名单文档: 1 个
2025-07-03 12:32:42.675 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:32:42.841 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3276 条结果
2025-07-03 12:32:42.844 | INFO     | __main__:process_one_record:2055 - 排除 1 个黑名单文档
2025-07-03 12:32:42.844 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:32:43.404 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7350 条结果
2025-07-03 12:32:43.404 | INFO     | __main__:process_one_record:2067 - ID: -1pfuZcBsUtJ06NfZI4t 开始分析公告主体
2025-07-03 12:32:43.404 | INFO     | __main__:process_one_record:2068 - 公告标题: 新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告
2025-07-03 12:32:43.404 | INFO     | __main__:process_one_record:2069 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm
2025-07-03 12:32:44.392 | INFO     | __main__:llm:585 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:32:44.392 | INFO     | __main__:llm:600 - enable_thinking=False
2025-07-03 12:32:44.668 | INFO     | __main__:llm:606 - extra_body={'enable_thinking': False}
2025-07-03 12:32:59.187 | INFO     | __main__:llm:610 - LLM API调用成功
2025-07-03 12:32:59.187 | INFO     | __main__:analyze_content:2002 - [
    {
        "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "bid_number": "XJDH-YKD2025-076",
        "bid_budget": 50000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "乌鲁木齐河南西路118号",
        "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
        "prj_number": "XJDH-YKD2025-076",
        "prj_type": "货物",
        "release_time": "2025-06-28 18:44:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "新疆医科大学第五附属医院",
        "bid_submission_deadline": "2025-07-22 11:00:00",
        "trade_platform": "政采云平台",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": null,
        "announcement_type": "001",
        "object_name": "可调式支撑喉镜",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 50000.0,
        "object_total_price": 50000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "新疆德鸿项目管理咨询有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 12:32:59.187 | INFO     | __main__:process_one_record:2140 - ID: -1pfuZcBsUtJ06NfZI4t 发现附件，开始分析附件
2025-07-03 12:32:59.187 | INFO     | __main__:process_one_record:2141 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc', 'text': '公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜'}]
2025-07-03 12:32:59.187 | INFO     | __main__:process_one_record:2152 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:32:59.187 | INFO     | __main__:download_file:650 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:32:59.495 | INFO     | __main__:download_file:681 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:32:59.500 | INFO     | __main__:process_one_record:2194 - 附件已保存到: downloads\公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 12:32:59.505 | INFO     | __main__:process_one_record:2218 - 使用markitdown转换文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc
2025-07-03 12:32:59.610 | ERROR    | __main__:convert_to_markdown_with_markitdown:807 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 12:32:59.611 | INFO     | __main__:convert_to_markdown_with_markitdown:809 - 回退到传统方法解析.doc文件
2025-07-03 12:32:59.616 | WARNING  | __main__:parse_doc:876 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 12:33:00.788 | WARNING  | __main__:parse_doc:920 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpryd34xb3.doc

2025-07-03 12:33:00.810 | INFO     | __main__:parse_doc:952 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:33:00.812 | INFO     | __main__:process_one_record:2260 - 正在分析文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:33:00.812 | WARNING  | __main__:parse_doc:876 - docx2txt解析.doc文件失败: "There is no item named 'word/document.xml' in the archive"，尝试其他方法
2025-07-03 12:33:01.654 | WARNING  | __main__:parse_doc:920 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpcouhrtr1.doc

2025-07-03 12:33:01.669 | INFO     | __main__:parse_doc:952 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:33:01.674 | INFO     | __main__:detect_file_type:1158 - 此文件是招标文件: 公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜 (匹配关键词: ['公开文件', '设备', '医疗'])
2025-07-03 12:33:01.674 | INFO     | __main__:process_one_record:2271 - 存储招标文件内容: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:33:01.675 | INFO     | __main__:process_one_record:2289 - 开始上传招标文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:33:01.675 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-1pfuZcBsUtJ06NfZI4t.doc
2025-07-03 12:33:01.675 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-1pfuZcBsUtJ06NfZI4t.doc, 大小: 406317 字节
2025-07-03 12:33:01.676 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=406317&objectName=招标文件_-1pfuZcBsUtJ06NfZI4t.doc
2025-07-03 12:33:01.699 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197ce8f3c5c7a0f82ce4f83c10309a6', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197ce8f3c5c7a0f82ce4f83c10309a6/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-1pfuZcBsUtJ06NfZI4t.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T043303Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=3d63c8c4325e4a94042b68b292cf777d3dc510962d42f2d347e12dbf17f33db9'}, 'timestamp': 1751517183078, 'callId': None}
2025-07-03 12:33:01.700 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 406317 字节
2025-07-03 12:33:01.772 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 12:33:01.773 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197ce8f3c5c7a0f82ce4f83c10309a6
2025-07-03 12:33:01.868 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 12:33:01.869 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-1pfuZcBsUtJ06NfZI4t.doc
2025-07-03 12:33:01.869 | INFO     | __main__:process_one_record:2302 - ✓ 招标文件上传成功: 招标文件_-1pfuZcBsUtJ06NfZI4t.doc, upload_id: 0197ce8f3c5c7a0f82ce4f83c10309a6
2025-07-03 12:33:01.869 | WARNING  | __main__:analyze_content:1978 - 文档内容过长(139222字符)，截取前131072字符
2025-07-03 12:33:02.930 | INFO     | __main__:llm:585 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:33:02.930 | INFO     | __main__:llm:600 - enable_thinking=False
2025-07-03 12:33:02.930 | INFO     | __main__:llm:606 - extra_body={'enable_thinking': False}
2025-07-03 12:33:44.725 | INFO     | __main__:llm:610 - LLM API调用成功
2025-07-03 12:33:44.725 | INFO     | __main__:analyze_content:2002 - [
    {
        "bid_name": null,
        "bid_number": "SXJDH-YKD2025-076",
        "bid_budget": 50000.0,
        "fiscal_delegation_number": null,
        "prj_addr": null,
        "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
        "prj_number": "SXJDH-YKD2025-076",
        "prj_type": "货物",
        "release_time": "2025-06-28 00:00:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "新疆医科大学第五附属医院",
        "bid_submission_deadline": "2025-07-22 11:00:00",
        "trade_platform": "中国政府采购网",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "新疆维吾尔自治区",
        "city": "乌鲁木齐市",
        "county": null,
        "announcement_type": "001",
        "object_name": "可调式支撑喉镜",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": 50000.0,
        "object_total_price": 50000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": null,
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2423 - 已找到并分析招标文件: 273f959e-7126-4d43-b275-99aecb309b25.doc
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2523 - ================================================================================
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2524 - 开始融合分析结果
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2525 - ================================================================================
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2526 - 主体解析结果数量: 1
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2527 - 招标文件解析结果数量: 1
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2528 - 合同文件解析结果数量: 0
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2531 - 主体解析结果:
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2533 - 主体结果 1:
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2534 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 12:32:59"
}
2025-07-03 12:33:44.725 | INFO     | __main__:process_one_record:2539 - 招标文件解析结果:
2025-07-03 12:33:44.731 | INFO     | __main__:process_one_record:2541 - 招标文件结果 1:
2025-07-03 12:33:44.731 | INFO     | __main__:process_one_record:2542 - {
  "bid_name": null,
  "bid_number": "SXJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": null,
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "prj_number": "SXJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 00:00:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "中国政府采购网",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": null,
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "insert_time": "2025-07-03 12:33:44",
  "bid_doc_name": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc",
  "bid_doc_ext": ".doc",
  "bid_doc_link_out": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
  "bid_doc_link_key": "0197ce8f3c5c7a0f82ce4f83c10309a6",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null
}
2025-07-03 12:33:44.731 | INFO     | __main__:process_one_record:2558 - 使用智能融合分析
2025-07-03 12:33:44.731 | INFO     | __main__:intelligent_merge_analysis:1461 - 开始基于object_name的匹配融合分析
2025-07-03 12:33:44.731 | INFO     | __main__:merge_analysis_by_object_name:1419 - 主体标的物 '可调式支撑喉镜' 匹配结果:
2025-07-03 12:33:44.731 | INFO     | __main__:merge_analysis_by_object_name:1420 -   - 招标文件匹配: 是
2025-07-03 12:33:44.731 | INFO     | __main__:merge_analysis_by_object_name:1421 -   - 合同文件匹配: 否
2025-07-03 12:33:44.731 | INFO     | __main__:intelligent_merge_analysis:1469 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 12:33:44.731 | INFO     | __main__:intelligent_merge_analysis:1478 - 开始对融合结果进行智能补充
2025-07-03 12:33:44.731 | INFO     | __main__:intelligent_merge_analysis:1482 - 处理第1个融合结果的智能补充
2025-07-03 12:33:44.731 | INFO     | __main__:intelligent_merge_analysis:1495 - 发现空缺字段: ['fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'county', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_conf', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_price', 'bidder_name', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 12:33:44.734 | INFO     | __main__:intelligent_merge_analysis:1540 - 从招标文件解析结果中获取字段: ['fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'county', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_conf', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 12:33:44.734 | INFO     | __main__:intelligent_merge_analysis:1556 - 招标文件已解析但缺少字段 ['fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'county', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_conf', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']，跳过LLM重新提取
2025-07-03 12:33:44.734 | INFO     | __main__:intelligent_merge_analysis:1559 - 建议：检查字段定义或文档内容是否包含这些信息
2025-07-03 12:33:44.734 | INFO     | __main__:intelligent_merge_analysis:1593 - 第1个融合结果智能补充完成
2025-07-03 12:33:44.735 | INFO     | __main__:intelligent_merge_analysis:1595 - 智能补充完成，返回1个增强结果
2025-07-03 12:33:44.735 | INFO     | __main__:process_one_record:2573 - ================================================================================
2025-07-03 12:33:44.735 | INFO     | __main__:process_one_record:2574 - 最终融合后的JSON结果列表:
2025-07-03 12:33:44.735 | INFO     | __main__:process_one_record:2575 - ================================================================================
2025-07-03 12:33:44.735 | INFO     | __main__:process_one_record:2577 - 融合结果 1:
2025-07-03 12:33:44.735 | INFO     | __main__:process_one_record:2578 - {
  "bid_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "bid_number": "XJDH-YKD2025-076",
  "bid_budget": 50000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "乌鲁木齐河南西路118号",
  "prj_name": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜",
  "prj_number": "XJDH-YKD2025-076",
  "prj_type": "货物",
  "release_time": "2025-06-28 18:44:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "新疆医科大学第五附属医院",
  "bid_submission_deadline": "2025-07-22 11:00:00",
  "trade_platform": "政采云平台",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "新疆维吾尔自治区",
  "city": "乌鲁木齐市",
  "county": null,
  "announcement_type": "001",
  "object_name": "可调式支撑喉镜",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 50000.0,
  "object_total_price": 50000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "新疆德鸿项目管理咨询有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1pfuZcBsUtJ06NfZI4t",
  "source_title": "新疆医科大学第五附属医院2025年医疗设备第一批-可调式支撑喉镜公开招标公告",
  "source_create_time": "2025-06-29",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250628_24870011.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
      "text": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜"
    }
  ],
  "bid_doc_name": "公开文件新疆医科大学第五附属医院年医疗设备第一批可调式支撑喉镜.doc",
  "bid_doc_ext": ".doc",
  "bid_doc_link_out": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/650102/10007490920/20256/273f959e-7126-4d43-b275-99aecb309b25.doc",
  "bid_doc_link_key": "0197ce8f3c5c7a0f82ce4f83c10309a6",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 12:32:59"
}
2025-07-03 12:33:44.736 | INFO     | __main__:process_one_record:2579 - ------------------------------------------------------------
2025-07-03 12:33:44.736 | INFO     | __main__:process_one_record:2580 - ================================================================================
2025-07-03 12:33:44.736 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 12:33:44.748 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 12:33:44.748 | INFO     | __main__:process_one_record:2591 - 成功插入融合文档 -1pfuZcBsUtJ06NfZI4t_0
2025-07-03 12:48:23.677 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:48:23.682 | INFO     | __main__:__init__:1631 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:48:23.682 | INFO     | __main__:__init__:1633 - 今日新增黑名单文档: 1 个
2025-07-03 12:48:23.682 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:48:24.204 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3277 条结果
2025-07-03 12:48:24.204 | INFO     | __main__:process_one_record:2039 - 排除 1 个黑名单文档
2025-07-03 12:48:24.204 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:48:24.831 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7349 条结果
2025-07-03 12:48:24.831 | INFO     | __main__:process_one_record:2051 - ID: -1v4zZcBsUtJ06Nf1XEk 开始分析公告主体
2025-07-03 12:48:24.831 | INFO     | __main__:process_one_record:2052 - 公告标题: 榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告
2025-07-03 12:48:24.832 | INFO     | __main__:process_one_record:2053 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm
2025-07-03 12:48:25.852 | INFO     | __main__:llm:569 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:48:25.852 | INFO     | __main__:llm:584 - enable_thinking=False
2025-07-03 12:48:26.236 | INFO     | __main__:llm:590 - extra_body={'enable_thinking': False}
2025-07-03 12:49:08.296 | INFO     | __main__:llm:594 - LLM API调用成功
2025-07-03 12:49:08.296 | INFO     | __main__:analyze_content:1986 - [
    {
        "bid_name": "过敏性鼻炎防治香囊袋",
        "bid_number": "FSCS-**********",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "榆林市榆阳区金沙路街道",
        "prj_name": "过敏性鼻炎健康包货物采购项目",
        "prj_number": "FSCS-**********",
        "prj_type": "货物",
        "release_time": "2025-07-02 18:47:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "榆林市中医医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "耗材",
        "province": "陕西省",
        "city": "榆林市",
        "county": "榆阳区",
        "announcement_type": "004",
        "object_name": "过敏性鼻炎防治香囊袋",
        "object_brand": "羊老大",
        "object_model": "8cm*9cm",
        "object_supplier": "榆林羊老大品牌服饰运营有限责任公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 130000,
        "object_unit": "个",
        "object_price": 4.95,
        "object_total_price": 643500.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 643500.0,
        "bidder_name": "榆林羊老大品牌服饰运营有限责任公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "陕西福盛昌顺项目管理有限公司",
        "service_fee": 9653.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    },
    {
        "bid_name": "外包装袋",
        "bid_number": "FSCS-**********",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "榆林市榆阳区金沙路街道",
        "prj_name": "过敏性鼻炎健康包货物采购项目",
        "prj_number": "FSCS-**********",
        "prj_type": "货物",
        "release_time": "2025-07-02 18:47:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "榆林市中医医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "耗材",
        "province": "陕西省",
        "city": "榆林市",
        "county": "榆阳区",
        "announcement_type": "004",
        "object_name": "外包装袋",
        "object_brand": "高能",
        "object_model": "长：30CM 高：25CM 底：6CM",
        "object_supplier": "榆林市广德商贸有限公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "批",
        "object_price": 547270.0,
        "object_total_price": 547270.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 547270.0,
        "bidder_name": "榆林市广德商贸有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "陕西福盛昌顺项目管理有限公司",
        "service_fee": 8209.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    },
    {
        "bid_name": "宣传册",
        "bid_number": "FSCS-**********",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "榆林市榆阳区金沙路街道",
        "prj_name": "过敏性鼻炎健康包货物采购项目",
        "prj_number": "FSCS-**********",
        "prj_type": "货物",
        "release_time": "2025-07-02 18:47:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "榆林市中医医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "其他",
        "province": "陕西省",
        "city": "榆林市",
        "county": "榆阳区",
        "announcement_type": "004",
        "object_name": "宣传册",
        "object_brand": null,
        "object_model": "21*14cm",
        "object_supplier": "西安松林森彩印有限公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "批",
        "object_price": 413500.0,
        "object_total_price": 413500.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 413500.0,
        "bidder_name": "西安松林森彩印有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "陕西福盛昌顺项目管理有限公司",
        "service_fee": 6203.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 12:49:08.296 | INFO     | __main__:process_one_record:2124 - ID: -1v4zZcBsUtJ06Nf1XEk 发现附件，开始分析附件
2025-07-03 12:49:08.296 | INFO     | __main__:process_one_record:2125 - 附件列表: [{'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff', 'text': '榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细'}, {'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9', 'text': '过敏性鼻炎防治香囊袋'}, {'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af', 'text': '外包装袋'}, {'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113', 'text': '宣传册'}, {'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964', 'text': '合同包中小企业声明函'}, {'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56', 'text': '合同包中小企业声明函'}, {'url': 'https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4', 'text': '合同包中小企业声明函'}]
2025-07-03 12:49:08.296 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff
2025-07-03 12:49:08.296 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff
2025-07-03 12:49:08.549 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff
2025-07-03 12:49:08.554 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf
2025-07-03 12:49:08.554 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf
2025-07-03 12:49:08.771 | DEBUG    | __main__:convert_to_markdown_with_markitdown:775 - markitdown成功转换.pdf文件，生成1115字符的Markdown
2025-07-03 12:49:08.788 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c68597c133b60197ca72b1e9487f.pdf
2025-07-03 12:49:08.934 | DEBUG    | __main__:extract_preview_text:1098 - Successfully extracted PDF preview with pdfplumber, 1032 characters
2025-07-03 12:49:08.934 | INFO     | __main__:detect_file_type:1150 - 此文件是合同文件: 榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细 (匹配关键词: ['合同'])
2025-07-03 12:49:08.934 | INFO     | __main__:process_one_record:2260 - 存储合同文件内容: 8a69c68597c133b60197ca72b1e9487f.pdf
2025-07-03 12:49:08.934 | INFO     | __main__:process_one_record:2273 - 开始上传合同文件: 8a69c68597c133b60197ca72b1e9487f.pdf
2025-07-03 12:49:08.934 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 合同文件_-1v4zZcBsUtJ06Nf1XEk.pdf
2025-07-03 12:49:08.934 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 合同文件_-1v4zZcBsUtJ06Nf1XEk.pdf, 大小: 44557 字节
2025-07-03 12:49:08.934 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=44557&objectName=合同文件_-1v4zZcBsUtJ06Nf1XEk.pdf
2025-07-03 12:49:08.999 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197ce9dfedb718a8550761f2578c6ed', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197ce9dfedb718a8550761f2578c6ed/%E5%90%88%E5%90%8C%E6%96%87%E4%BB%B6_-1v4zZcBsUtJ06Nf1XEk.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T044910Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=e41d734cc6ea1935a0b2fef15bac7231a9247dbc02e500b5f4fc819e0360ed68'}, 'timestamp': 1751518150370, 'callId': None}
2025-07-03 12:49:09.000 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 44557 字节
2025-07-03 12:49:09.067 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 12:49:09.067 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197ce9dfedb718a8550761f2578c6ed
2025-07-03 12:49:09.195 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 12:49:09.195 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.pdf
2025-07-03 12:49:09.195 | INFO     | __main__:process_one_record:2286 - ✓ 合同文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.pdf, upload_id: 0197ce9dfedb718a8550761f2578c6ed
2025-07-03 12:49:10.185 | INFO     | __main__:llm:569 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:49:10.185 | INFO     | __main__:llm:584 - enable_thinking=False
2025-07-03 12:49:10.190 | INFO     | __main__:llm:590 - extra_body={'enable_thinking': False}
2025-07-03 12:49:46.284 | INFO     | __main__:llm:594 - LLM API调用成功
2025-07-03 12:49:46.285 | INFO     | __main__:analyze_content:1986 - [
    {
        "bid_name": "合同包1 （过敏性鼻炎防治香囊袋）",
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": null,
        "prj_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目",
        "prj_number": "FSCS-**********",
        "prj_type": "货物",
        "release_time": null,
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "榆林市中医医院",
        "bid_submission_deadline": null,
        "trade_platform": null,
        "procurement_method": null,
        "prj_sub_type": "其他",
        "province": "陕西省",
        "city": "榆林市",
        "county": null,
        "announcement_type": "004",
        "object_name": "过敏性鼻炎防治香囊袋",
        "object_brand": "羊老大",
        "object_model": "8cm*9cm",
        "object_supplier": "榆林羊老大品牌服饰运营有限责任公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 130000,
        "object_unit": "个",
        "object_price": 4.95,
        "object_total_price": 643500.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 643500.0,
        "bidder_name": "榆林羊老大品牌服饰运营有限责任公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": null,
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    },
    {
        "bid_name": "合同包2 （外包装袋）",
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": null,
        "prj_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目",
        "prj_number": "FSCS-**********",
        "prj_type": "货物",
        "release_time": null,
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "榆林市中医医院",
        "bid_submission_deadline": null,
        "trade_platform": null,
        "procurement_method": null,
        "prj_sub_type": "其他",
        "province": "陕西省",
        "city": "榆林市",
        "county": null,
        "announcement_type": "004",
        "object_name": "外包装袋",
        "object_brand": "高能",
        "object_model": "长：30CM 高：25CM 底：6CM",
        "object_supplier": "榆林市广德商贸有限公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "批",
        "object_price": 547270.0,
        "object_total_price": 547270.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 547270.0,
        "bidder_name": "榆林市广德商贸有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": null,
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    },
    {
        "bid_name": "合同包3 （宣传册）",
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": null,
        "prj_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目",
        "prj_number": "FSCS-**********",
        "prj_type": "货物",
        "release_time": null,
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "榆林市中医医院",
        "bid_submission_deadline": null,
        "trade_platform": null,
        "procurement_method": null,
        "prj_sub_type": "其他",
        "province": "陕西省",
        "city": "榆林市",
        "county": null,
        "announcement_type": "004",
        "object_name": "宣传册",
        "object_brand": null,
        "object_model": "21*14cm",
        "object_supplier": "西安松林森彩印有限公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "批",
        "object_price": 413500.0,
        "object_total_price": 413500.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 413500.0,
        "bidder_name": "西安松林森彩印有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": null,
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 12:49:46.286 | INFO     | __main__:process_one_record:2417 - 已找到并分析合同文件: 8a69c68597c133b60197ca72b1e9487f.pdf
2025-07-03 12:49:46.286 | INFO     | __main__:process_one_record:2417 - 已找到并分析合同文件: 8a69c68597c133b60197ca72b1e9487f.pdf
2025-07-03 12:49:46.286 | INFO     | __main__:process_one_record:2417 - 已找到并分析合同文件: 8a69c68597c133b60197ca72b1e9487f.pdf
2025-07-03 12:49:46.287 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9
2025-07-03 12:49:46.287 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9
2025-07-03 12:49:46.821 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9
2025-07-03 12:49:46.823 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\过敏性鼻炎防治香囊袋.doc
2025-07-03 12:49:46.824 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 过敏性鼻炎防治香囊袋.doc
2025-07-03 12:49:46.887 | ERROR    | __main__:convert_to_markdown_with_markitdown:791 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 12:49:46.888 | INFO     | __main__:convert_to_markdown_with_markitdown:793 - 回退到传统方法解析.doc文件
2025-07-03 12:49:46.894 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:47.733 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpnm9cyyoe.doc

2025-07-03 12:49:47.818 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:47.820 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c0b897c135de0197ca769bbe15c8.doc
2025-07-03 12:49:47.822 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:48.633 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmp34qzu4wu.doc

2025-07-03 12:49:48.699 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:48.722 | INFO     | __main__:detect_file_type:1192 - 此文件是其他文件: 过敏性鼻炎防治香囊袋
2025-07-03 12:49:48.722 | INFO     | __main__:process_one_record:2328 - 跳过非招标/合同文件: 8a69c0b897c135de0197ca769bbe15c8.doc
2025-07-03 12:49:48.723 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af
2025-07-03 12:49:48.723 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af
2025-07-03 12:49:49.114 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af
2025-07-03 12:49:49.119 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\外包装袋.doc
2025-07-03 12:49:49.119 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 外包装袋.doc
2025-07-03 12:49:49.186 | ERROR    | __main__:convert_to_markdown_with_markitdown:791 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 12:49:49.187 | INFO     | __main__:convert_to_markdown_with_markitdown:793 - 回退到传统方法解析.doc文件
2025-07-03 12:49:49.189 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:49.998 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpiner1xdy.doc

2025-07-03 12:49:50.080 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:50.102 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c68597c133b60197ca76b2fc4917.doc
2025-07-03 12:49:50.104 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:50.970 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpamqb2ecs.doc

2025-07-03 12:49:51.059 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:51.064 | INFO     | __main__:detect_file_type:1192 - 此文件是其他文件: 外包装袋
2025-07-03 12:49:51.064 | INFO     | __main__:process_one_record:2328 - 跳过非招标/合同文件: 8a69c68597c133b60197ca76b2fc4917.doc
2025-07-03 12:49:51.064 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113
2025-07-03 12:49:51.064 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113
2025-07-03 12:49:51.450 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113
2025-07-03 12:49:51.453 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\宣传册.doc
2025-07-03 12:49:51.453 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 宣传册.doc
2025-07-03 12:49:51.497 | ERROR    | __main__:convert_to_markdown_with_markitdown:791 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 12:49:51.498 | INFO     | __main__:convert_to_markdown_with_markitdown:793 - 回退到传统方法解析.doc文件
2025-07-03 12:49:51.500 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:52.278 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpui0gpx17.doc

2025-07-03 12:49:52.360 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:52.382 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c68597c133b60197ca76bc23491e.doc
2025-07-03 12:49:52.384 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:53.231 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmp6g9qy56p.doc

2025-07-03 12:49:53.313 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:53.315 | INFO     | __main__:detect_file_type:1192 - 此文件是其他文件: 宣传册
2025-07-03 12:49:53.316 | INFO     | __main__:process_one_record:2328 - 跳过非招标/合同文件: 8a69c68597c133b60197ca76bc23491e.doc
2025-07-03 12:49:53.316 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964
2025-07-03 12:49:53.316 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964
2025-07-03 12:49:53.519 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964
2025-07-03 12:49:53.522 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\合同包中小企业声明函.docx
2025-07-03 12:49:53.522 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 合同包中小企业声明函.docx
2025-07-03 12:49:53.584 | DEBUG    | __main__:convert_to_markdown_with_markitdown:775 - markitdown成功转换.docx文件，生成29字符的Markdown
2025-07-03 12:49:53.587 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c68597c133b60197ca76cae74922.docx
2025-07-03 12:49:53.589 | INFO     | __main__:detect_file_type:1150 - 此文件是合同文件: 合同包中小企业声明函 (匹配关键词: ['合同'])
2025-07-03 12:49:53.590 | INFO     | __main__:process_one_record:2273 - 开始上传合同文件: 8a69c68597c133b60197ca76cae74922.docx
2025-07-03 12:49:53.590 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 合同文件_-1v4zZcBsUtJ06Nf1XEk.docx
2025-07-03 12:49:53.590 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 合同文件_-1v4zZcBsUtJ06Nf1XEk.docx, 大小: 145654 字节
2025-07-03 12:49:53.591 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=145654&objectName=合同文件_-1v4zZcBsUtJ06Nf1XEk.docx
2025-07-03 12:49:53.630 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197ce9ead3a7b749e27da46f15175d6', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197ce9ead3a7b749e27da46f15175d6/%E5%90%88%E5%90%8C%E6%96%87%E4%BB%B6_-1v4zZcBsUtJ06Nf1XEk.docx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T044955Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=6d5d19c36e115183f1ced9198167f9d6753344300b0309d98d1cb8b30b4e6054'}, 'timestamp': 1751518195010, 'callId': None}
2025-07-03 12:49:53.631 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 145654 字节
2025-07-03 12:49:53.670 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 12:49:53.671 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197ce9ead3a7b749e27da46f15175d6
2025-07-03 12:49:53.721 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 12:49:53.721 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.docx
2025-07-03 12:49:53.721 | INFO     | __main__:process_one_record:2286 - ✓ 合同文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.docx, upload_id: 0197ce9ead3a7b749e27da46f15175d6
2025-07-03 12:49:53.721 | INFO     | __main__:process_one_record:2323 - 已找到合同文件，跳过后续合同文件: 8a69c68597c133b60197ca76cae74922.docx
2025-07-03 12:49:53.722 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56
2025-07-03 12:49:53.722 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56
2025-07-03 12:49:53.961 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56
2025-07-03 12:49:53.966 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\合同包中小企业声明函.doc
2025-07-03 12:49:53.967 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 合同包中小企业声明函.doc
2025-07-03 12:49:54.026 | ERROR    | __main__:convert_to_markdown_with_markitdown:791 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 12:49:54.027 | INFO     | __main__:convert_to_markdown_with_markitdown:793 - 回退到传统方法解析.doc文件
2025-07-03 12:49:54.028 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:54.891 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpy8tlsjoq.doc

2025-07-03 12:49:54.895 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:54.896 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c68597c133b60197ca76d810492c.doc
2025-07-03 12:49:54.897 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:55.742 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpxm282rrv.doc

2025-07-03 12:49:55.747 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:55.748 | INFO     | __main__:detect_file_type:1150 - 此文件是合同文件: 合同包中小企业声明函 (匹配关键词: ['合同'])
2025-07-03 12:49:55.748 | INFO     | __main__:process_one_record:2273 - 开始上传合同文件: 8a69c68597c133b60197ca76d810492c.doc
2025-07-03 12:49:55.749 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc
2025-07-03 12:49:55.749 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc, 大小: 123904 字节
2025-07-03 12:49:55.749 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=123904&objectName=合同文件_-1v4zZcBsUtJ06Nf1XEk.doc
2025-07-03 12:49:55.815 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197ce9eb5b775a8929eb5f5d742bf95', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197ce9eb5b775a8929eb5f5d742bf95/%E5%90%88%E5%90%8C%E6%96%87%E4%BB%B6_-1v4zZcBsUtJ06Nf1XEk.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T044957Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=baa8dd67a6c5fc0dca91f81740bf81378e746435da80c68fed548b6fd5f56256'}, 'timestamp': 1751518197185, 'callId': None}
2025-07-03 12:49:55.816 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 123904 字节
2025-07-03 12:49:55.873 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 12:49:55.874 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197ce9eb5b775a8929eb5f5d742bf95
2025-07-03 12:49:55.933 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 12:49:55.934 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc
2025-07-03 12:49:55.934 | INFO     | __main__:process_one_record:2286 - ✓ 合同文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc, upload_id: 0197ce9eb5b775a8929eb5f5d742bf95
2025-07-03 12:49:55.934 | INFO     | __main__:process_one_record:2323 - 已找到合同文件，跳过后续合同文件: 8a69c68597c133b60197ca76d810492c.doc
2025-07-03 12:49:55.935 | INFO     | __main__:process_one_record:2136 - 正在下载附件: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4
2025-07-03 12:49:55.936 | INFO     | __main__:download_file:634 - 正在下载文件 (尝试 1/3): https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4
2025-07-03 12:49:56.135 | INFO     | __main__:download_file:665 - 文件下载成功: https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4
2025-07-03 12:49:56.138 | INFO     | __main__:process_one_record:2178 - 附件已保存到: downloads\合同包中小企业声明函.doc
2025-07-03 12:49:56.139 | INFO     | __main__:process_one_record:2202 - 使用markitdown转换文件: 合同包中小企业声明函.doc
2025-07-03 12:49:56.186 | ERROR    | __main__:convert_to_markdown_with_markitdown:791 - markitdown转换.doc文件失败: Could not convert stream to Markdown. No converter attempted a conversion, suggesting that the filetype is simply not supported.
2025-07-03 12:49:56.186 | INFO     | __main__:convert_to_markdown_with_markitdown:793 - 回退到传统方法解析.doc文件
2025-07-03 12:49:56.189 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:57.084 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpu_zm9dfi.doc

2025-07-03 12:49:57.088 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:57.088 | INFO     | __main__:process_one_record:2244 - 正在分析文件: 8a69c68597c133b60197ca76e6934936.doc
2025-07-03 12:49:57.090 | WARNING  | __main__:parse_doc:860 - docx2txt解析.doc文件失败: File is not a zip file，尝试其他方法
2025-07-03 12:49:57.846 | WARNING  | __main__:parse_doc:904 - antiword解析失败，退出码: 2, 错误: usage: antiword [-h] IN
antiword: error: unrecognized arguments: -w C:\Users\<USER>\AppData\Local\Temp\tmpmak_fevg.doc

2025-07-03 12:49:57.850 | INFO     | __main__:parse_doc:936 - 使用基本文本提取方法解析.doc文件
2025-07-03 12:49:57.860 | INFO     | __main__:detect_file_type:1150 - 此文件是合同文件: 合同包中小企业声明函 (匹配关键词: ['合同'])
2025-07-03 12:49:57.860 | INFO     | __main__:process_one_record:2273 - 开始上传合同文件: 8a69c68597c133b60197ca76e6934936.doc
2025-07-03 12:49:57.861 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc
2025-07-03 12:49:57.861 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc, 大小: 122880 字节
2025-07-03 12:49:57.861 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=122880&objectName=合同文件_-1v4zZcBsUtJ06Nf1XEk.doc
2025-07-03 12:49:57.907 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197ce9ebdeb7d5fbfb1030e0dbe8ee6', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197ce9ebdeb7d5fbfb1030e0dbe8ee6/%E5%90%88%E5%90%8C%E6%96%87%E4%BB%B6_-1v4zZcBsUtJ06Nf1XEk.doc?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T044959Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=e59a2b881ab3893e2dcc7367edf072e644ece342098d5b3045208da9174d0099'}, 'timestamp': 1751518199288, 'callId': None}
2025-07-03 12:49:57.908 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 122880 字节
2025-07-03 12:49:57.954 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 12:49:57.954 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197ce9ebdeb7d5fbfb1030e0dbe8ee6
2025-07-03 12:49:58.008 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 12:49:58.009 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc
2025-07-03 12:49:58.009 | INFO     | __main__:process_one_record:2286 - ✓ 合同文件上传成功: 合同文件_-1v4zZcBsUtJ06Nf1XEk.doc, upload_id: 0197ce9ebdeb7d5fbfb1030e0dbe8ee6
2025-07-03 12:49:58.009 | INFO     | __main__:process_one_record:2323 - 已找到合同文件，跳过后续合同文件: 8a69c68597c133b60197ca76e6934936.doc
2025-07-03 12:49:58.009 | INFO     | __main__:process_one_record:2507 - ================================================================================
2025-07-03 12:49:58.011 | INFO     | __main__:process_one_record:2508 - 开始融合分析结果
2025-07-03 12:49:58.011 | INFO     | __main__:process_one_record:2509 - ================================================================================
2025-07-03 12:49:58.011 | INFO     | __main__:process_one_record:2510 - 主体解析结果数量: 3
2025-07-03 12:49:58.011 | INFO     | __main__:process_one_record:2511 - 招标文件解析结果数量: 0
2025-07-03 12:49:58.012 | INFO     | __main__:process_one_record:2512 - 合同文件解析结果数量: 3
2025-07-03 12:49:58.013 | INFO     | __main__:process_one_record:2515 - 主体解析结果:
2025-07-03 12:49:58.013 | INFO     | __main__:process_one_record:2517 - 主体结果 1:
2025-07-03 12:49:58.013 | INFO     | __main__:process_one_record:2518 - {
  "bid_name": "过敏性鼻炎防治香囊袋",
  "bid_number": "FSCS-**********",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "榆林市榆阳区金沙路街道",
  "prj_name": "过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": "2025-07-02 18:47:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "耗材",
  "province": "陕西省",
  "city": "榆林市",
  "county": "榆阳区",
  "announcement_type": "004",
  "object_name": "过敏性鼻炎防治香囊袋",
  "object_brand": "羊老大",
  "object_model": "8cm*9cm",
  "object_supplier": "榆林羊老大品牌服饰运营有限责任公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 130000,
  "object_unit": "个",
  "object_price": 4.95,
  "object_total_price": 643500.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 643500.0,
  "bidder_name": "榆林羊老大品牌服饰运营有限责任公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "陕西福盛昌顺项目管理有限公司",
  "service_fee": 9653.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 12:49:08"
}
2025-07-03 12:49:58.014 | INFO     | __main__:process_one_record:2517 - 主体结果 2:
2025-07-03 12:49:58.015 | INFO     | __main__:process_one_record:2518 - {
  "bid_name": "外包装袋",
  "bid_number": "FSCS-**********",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "榆林市榆阳区金沙路街道",
  "prj_name": "过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": "2025-07-02 18:47:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "耗材",
  "province": "陕西省",
  "city": "榆林市",
  "county": "榆阳区",
  "announcement_type": "004",
  "object_name": "外包装袋",
  "object_brand": "高能",
  "object_model": "长：30CM 高：25CM 底：6CM",
  "object_supplier": "榆林市广德商贸有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "批",
  "object_price": 547270.0,
  "object_total_price": 547270.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 547270.0,
  "bidder_name": "榆林市广德商贸有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "陕西福盛昌顺项目管理有限公司",
  "service_fee": 8209.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 12:49:08"
}
2025-07-03 12:49:58.016 | INFO     | __main__:process_one_record:2517 - 主体结果 3:
2025-07-03 12:49:58.016 | INFO     | __main__:process_one_record:2518 - {
  "bid_name": "宣传册",
  "bid_number": "FSCS-**********",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "榆林市榆阳区金沙路街道",
  "prj_name": "过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": "2025-07-02 18:47:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "其他",
  "province": "陕西省",
  "city": "榆林市",
  "county": "榆阳区",
  "announcement_type": "004",
  "object_name": "宣传册",
  "object_brand": null,
  "object_model": "21*14cm",
  "object_supplier": "西安松林森彩印有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "批",
  "object_price": 413500.0,
  "object_total_price": 413500.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 413500.0,
  "bidder_name": "西安松林森彩印有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "陕西福盛昌顺项目管理有限公司",
  "service_fee": 6203.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 12:49:08"
}
2025-07-03 12:49:58.017 | INFO     | __main__:process_one_record:2531 - 合同文件解析结果:
2025-07-03 12:49:58.017 | INFO     | __main__:process_one_record:2533 - 合同文件结果 1:
2025-07-03 12:49:58.018 | INFO     | __main__:process_one_record:2534 - {
  "bid_name": "合同包1 （过敏性鼻炎防治香囊袋）",
  "bid_number": null,
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": null,
  "prj_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": null,
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": null,
  "procurement_method": null,
  "prj_sub_type": "其他",
  "province": "陕西省",
  "city": "榆林市",
  "county": null,
  "announcement_type": "004",
  "object_name": "过敏性鼻炎防治香囊袋",
  "object_brand": "羊老大",
  "object_model": "8cm*9cm",
  "object_supplier": "榆林羊老大品牌服饰运营有限责任公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 130000,
  "object_unit": "个",
  "object_price": 4.95,
  "object_total_price": 643500.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 643500.0,
  "bidder_name": "榆林羊老大品牌服饰运营有限责任公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": null,
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "insert_time": "2025-07-03 12:49:46",
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf",
  "contract_ext": ".pdf",
  "contract_link_out": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
  "contract_link_key": "0197ce9dfedb718a8550761f2578c6ed"
}
2025-07-03 12:49:58.019 | INFO     | __main__:process_one_record:2533 - 合同文件结果 2:
2025-07-03 12:49:58.019 | INFO     | __main__:process_one_record:2534 - {
  "bid_name": "合同包2 （外包装袋）",
  "bid_number": null,
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": null,
  "prj_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": null,
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": null,
  "procurement_method": null,
  "prj_sub_type": "其他",
  "province": "陕西省",
  "city": "榆林市",
  "county": null,
  "announcement_type": "004",
  "object_name": "外包装袋",
  "object_brand": "高能",
  "object_model": "长：30CM 高：25CM 底：6CM",
  "object_supplier": "榆林市广德商贸有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "批",
  "object_price": 547270.0,
  "object_total_price": 547270.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 547270.0,
  "bidder_name": "榆林市广德商贸有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": null,
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "insert_time": "2025-07-03 12:49:46",
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf",
  "contract_ext": ".pdf",
  "contract_link_out": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
  "contract_link_key": "0197ce9dfedb718a8550761f2578c6ed"
}
2025-07-03 12:49:58.021 | INFO     | __main__:process_one_record:2533 - 合同文件结果 3:
2025-07-03 12:49:58.022 | INFO     | __main__:process_one_record:2534 - {
  "bid_name": "合同包3 （宣传册）",
  "bid_number": null,
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": null,
  "prj_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": null,
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": null,
  "procurement_method": null,
  "prj_sub_type": "其他",
  "province": "陕西省",
  "city": "榆林市",
  "county": null,
  "announcement_type": "004",
  "object_name": "宣传册",
  "object_brand": null,
  "object_model": "21*14cm",
  "object_supplier": "西安松林森彩印有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "批",
  "object_price": 413500.0,
  "object_total_price": 413500.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 413500.0,
  "bidder_name": "西安松林森彩印有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": null,
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "insert_time": "2025-07-03 12:49:46",
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf",
  "contract_ext": ".pdf",
  "contract_link_out": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
  "contract_link_key": "0197ce9dfedb718a8550761f2578c6ed"
}
2025-07-03 12:49:58.023 | INFO     | __main__:process_one_record:2542 - 使用智能融合分析
2025-07-03 12:49:58.023 | INFO     | __main__:intelligent_merge_analysis:1445 - 开始基于object_name的匹配融合分析
2025-07-03 12:49:58.025 | INFO     | __main__:merge_analysis_by_object_name:1403 - 主体标的物 '过敏性鼻炎防治香囊袋' 匹配结果:
2025-07-03 12:49:58.025 | INFO     | __main__:merge_analysis_by_object_name:1404 -   - 招标文件匹配: 否
2025-07-03 12:49:58.025 | INFO     | __main__:merge_analysis_by_object_name:1405 -   - 合同文件匹配: 是
2025-07-03 12:49:58.026 | INFO     | __main__:merge_analysis_by_object_name:1403 - 主体标的物 '外包装袋' 匹配结果:
2025-07-03 12:49:58.026 | INFO     | __main__:merge_analysis_by_object_name:1404 -   - 招标文件匹配: 否
2025-07-03 12:49:58.026 | INFO     | __main__:merge_analysis_by_object_name:1405 -   - 合同文件匹配: 是
2025-07-03 12:49:58.026 | INFO     | __main__:merge_analysis_by_object_name:1403 - 主体标的物 '宣传册' 匹配结果:
2025-07-03 12:49:58.027 | INFO     | __main__:merge_analysis_by_object_name:1404 -   - 招标文件匹配: 否
2025-07-03 12:49:58.027 | INFO     | __main__:merge_analysis_by_object_name:1405 -   - 合同文件匹配: 是
2025-07-03 12:49:58.027 | INFO     | __main__:intelligent_merge_analysis:1453 - 基于object_name的匹配融合完成，产生3个结果
2025-07-03 12:49:58.027 | INFO     | __main__:intelligent_merge_analysis:1462 - 开始对融合结果进行智能补充
2025-07-03 12:49:58.028 | INFO     | __main__:intelligent_merge_analysis:1466 - 处理第1个融合结果的智能补充
2025-07-03 12:49:58.028 | INFO     | __main__:intelligent_merge_analysis:1479 - 发现空缺字段: ['bid_budget', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'bid_submission_deadline', 'procurement_method', 'object_produce_area', 'object_conf', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 12:49:58.028 | INFO     | __main__:intelligent_merge_analysis:1495 - 从合同文件解析结果中获取字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:49:58.028 | INFO     | __main__:intelligent_merge_analysis:1508 - 从合同文件内容中检索剩余字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:49:59.094 | INFO     | __main__:llm:569 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:49:59.094 | INFO     | __main__:llm:584 - enable_thinking=False
2025-07-03 12:49:59.095 | INFO     | __main__:llm:590 - extra_body={'enable_thinking': False}
2025-07-03 12:50:01.023 | INFO     | __main__:llm:594 - LLM API调用成功
2025-07-03 12:50:01.023 | INFO     | __main__:extract_fields_from_content:270 - 从文档内容中提取到字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:01.023 | INFO     | __main__:intelligent_merge_analysis:1577 - 第1个融合结果智能补充完成
2025-07-03 12:50:01.024 | INFO     | __main__:intelligent_merge_analysis:1466 - 处理第2个融合结果的智能补充
2025-07-03 12:50:01.024 | INFO     | __main__:intelligent_merge_analysis:1479 - 发现空缺字段: ['bid_budget', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'bid_submission_deadline', 'procurement_method', 'object_produce_area', 'object_conf', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 12:50:01.024 | INFO     | __main__:intelligent_merge_analysis:1495 - 从合同文件解析结果中获取字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:01.024 | INFO     | __main__:intelligent_merge_analysis:1508 - 从合同文件内容中检索剩余字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:02.112 | INFO     | __main__:llm:569 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:50:02.112 | INFO     | __main__:llm:584 - enable_thinking=False
2025-07-03 12:50:02.113 | INFO     | __main__:llm:590 - extra_body={'enable_thinking': False}
2025-07-03 12:50:04.389 | INFO     | __main__:llm:594 - LLM API调用成功
2025-07-03 12:50:04.389 | INFO     | __main__:extract_fields_from_content:270 - 从文档内容中提取到字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:04.390 | INFO     | __main__:intelligent_merge_analysis:1577 - 第2个融合结果智能补充完成
2025-07-03 12:50:04.390 | INFO     | __main__:intelligent_merge_analysis:1466 - 处理第3个融合结果的智能补充
2025-07-03 12:50:04.390 | INFO     | __main__:intelligent_merge_analysis:1479 - 发现空缺字段: ['bid_budget', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'bid_submission_deadline', 'procurement_method', 'object_brand', 'object_produce_area', 'object_conf', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 12:50:04.391 | INFO     | __main__:intelligent_merge_analysis:1495 - 从合同文件解析结果中获取字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:04.391 | INFO     | __main__:intelligent_merge_analysis:1508 - 从合同文件内容中检索剩余字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:05.498 | INFO     | __main__:llm:569 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:50:05.499 | INFO     | __main__:llm:584 - enable_thinking=False
2025-07-03 12:50:05.499 | INFO     | __main__:llm:590 - extra_body={'enable_thinking': False}
2025-07-03 12:50:06.899 | INFO     | __main__:llm:594 - LLM API调用成功
2025-07-03 12:50:06.900 | INFO     | __main__:extract_fields_from_content:270 - 从文档内容中提取到字段: ['bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param']
2025-07-03 12:50:06.900 | INFO     | __main__:intelligent_merge_analysis:1577 - 第3个融合结果智能补充完成
2025-07-03 12:50:06.900 | INFO     | __main__:intelligent_merge_analysis:1579 - 智能补充完成，返回3个增强结果
2025-07-03 12:50:06.901 | INFO     | __main__:process_one_record:2557 - ================================================================================
2025-07-03 12:50:06.901 | INFO     | __main__:process_one_record:2558 - 最终融合后的JSON结果列表:
2025-07-03 12:50:06.901 | INFO     | __main__:process_one_record:2559 - ================================================================================
2025-07-03 12:50:06.901 | INFO     | __main__:process_one_record:2561 - 融合结果 1:
2025-07-03 12:50:06.902 | INFO     | __main__:process_one_record:2562 - {
  "bid_name": "过敏性鼻炎防治香囊袋",
  "bid_number": "FSCS-**********",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "榆林市榆阳区金沙路街道",
  "prj_name": "过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": "2025-07-02 18:47:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "耗材",
  "province": "陕西省",
  "city": "榆林市",
  "county": "榆阳区",
  "announcement_type": "004",
  "object_name": "过敏性鼻炎防治香囊袋",
  "object_brand": "羊老大",
  "object_model": "8cm*9cm",
  "object_supplier": "榆林羊老大品牌服饰运营有限责任公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 130000,
  "object_unit": "个",
  "object_price": 4.95,
  "object_total_price": 643500.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 643500.0,
  "bidder_name": "榆林羊老大品牌服饰运营有限责任公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "陕西福盛昌顺项目管理有限公司",
  "service_fee": 9653.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf",
  "contract_ext": ".pdf",
  "contract_link_out": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
  "contract_link_key": "0197ce9dfedb718a8550761f2578c6ed",
  "insert_time": "2025-07-03 12:49:08"
}
2025-07-03 12:50:06.902 | INFO     | __main__:process_one_record:2563 - ------------------------------------------------------------
2025-07-03 12:50:06.902 | INFO     | __main__:process_one_record:2561 - 融合结果 2:
2025-07-03 12:50:06.903 | INFO     | __main__:process_one_record:2562 - {
  "bid_name": "外包装袋",
  "bid_number": "FSCS-**********",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "榆林市榆阳区金沙路街道",
  "prj_name": "过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": "2025-07-02 18:47:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "耗材",
  "province": "陕西省",
  "city": "榆林市",
  "county": "榆阳区",
  "announcement_type": "004",
  "object_name": "外包装袋",
  "object_brand": "高能",
  "object_model": "长：30CM 高：25CM 底：6CM",
  "object_supplier": "榆林市广德商贸有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "批",
  "object_price": 547270.0,
  "object_total_price": 547270.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 547270.0,
  "bidder_name": "榆林市广德商贸有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "陕西福盛昌顺项目管理有限公司",
  "service_fee": 8209.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf",
  "contract_ext": ".pdf",
  "contract_link_out": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
  "contract_link_key": "0197ce9dfedb718a8550761f2578c6ed",
  "insert_time": "2025-07-03 12:49:08"
}
2025-07-03 12:50:06.904 | INFO     | __main__:process_one_record:2563 - ------------------------------------------------------------
2025-07-03 12:50:06.904 | INFO     | __main__:process_one_record:2561 - 融合结果 3:
2025-07-03 12:50:06.904 | INFO     | __main__:process_one_record:2562 - {
  "bid_name": "宣传册",
  "bid_number": "FSCS-**********",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "榆林市榆阳区金沙路街道",
  "prj_name": "过敏性鼻炎健康包货物采购项目",
  "prj_number": "FSCS-**********",
  "prj_type": "货物",
  "release_time": "2025-07-02 18:47:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "榆林市中医医院",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "其他",
  "province": "陕西省",
  "city": "榆林市",
  "county": "榆阳区",
  "announcement_type": "004",
  "object_name": "宣传册",
  "object_brand": null,
  "object_model": "21*14cm",
  "object_supplier": "西安松林森彩印有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "批",
  "object_price": 413500.0,
  "object_total_price": 413500.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 413500.0,
  "bidder_name": "西安松林森彩印有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "陕西福盛昌顺项目管理有限公司",
  "service_fee": 6203.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-1v4zZcBsUtJ06Nf1XEk",
  "source_title": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标（成交）结果公告",
  "source_create_time": "2025-07-03",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/cjgg/202507/t20250702_24894708.htm",
  "source_appendix": [
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
      "text": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c0b897c135de0197ca769bbe15c8.doc?accessCode=c4a50b5a8d07407124de921ae69a93f9",
      "text": "过敏性鼻炎防治香囊袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76b2fc4917.doc?accessCode=32bb0d45ed6997dcb4c1c33885bb71af",
      "text": "外包装袋"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76bc23491e.doc?accessCode=0d1463c7fe98db02e4bf485f961ce113",
      "text": "宣传册"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76cae74922.docx?accessCode=a725fd34eb67620aa4efdf9abb553964",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76d810492c.doc?accessCode=3acf089a8f96fbb97b3c098706073c56",
      "text": "合同包中小企业声明函"
    },
    {
      "url": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/zone/2025/1/12/project/gpx-template/8a69c68597c133b60197ca76e6934936.doc?accessCode=c8d18bf61ad9f1875b0e72bac401dab4",
      "text": "合同包中小企业声明函"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": "榆林市中医医院过敏性鼻炎健康包货物采购项目中标成交明细.pdf",
  "contract_ext": ".pdf",
  "contract_link_out": "https://www.ccgp-shaanxi.gov.cn/gpx-bid-file/ZF_JGBM_000003/610801/2025/7/2/8a69c7c696720ce70197a05a99484acb/gpe-evaluation/8a69c68597c133b60197ca72b1e9487f.pdf?accessCode=2fb6dcc6d74f1c92b177346afeb0d7ff",
  "contract_link_key": "0197ce9dfedb718a8550761f2578c6ed",
  "insert_time": "2025-07-03 12:49:08"
}
2025-07-03 12:50:06.905 | INFO     | __main__:process_one_record:2563 - ------------------------------------------------------------
2025-07-03 12:50:06.905 | INFO     | __main__:process_one_record:2564 - ================================================================================
2025-07-03 12:50:06.905 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1v4zZcBsUtJ06Nf1XEk_0
2025-07-03 12:50:07.026 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1v4zZcBsUtJ06Nf1XEk_0
2025-07-03 12:50:07.027 | INFO     | __main__:process_one_record:2575 - 成功插入融合文档 -1v4zZcBsUtJ06Nf1XEk_0
2025-07-03 12:50:07.028 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1v4zZcBsUtJ06Nf1XEk_1
2025-07-03 12:50:07.068 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1v4zZcBsUtJ06Nf1XEk_1
2025-07-03 12:50:07.068 | INFO     | __main__:process_one_record:2575 - 成功插入融合文档 -1v4zZcBsUtJ06Nf1XEk_1
2025-07-03 12:50:07.068 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -1v4zZcBsUtJ06Nf1XEk_2
2025-07-03 12:50:07.093 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -1v4zZcBsUtJ06Nf1XEk_2
2025-07-03 12:50:07.094 | INFO     | __main__:process_one_record:2575 - 成功插入融合文档 -1v4zZcBsUtJ06Nf1XEk_2
2025-07-03 12:50:25.482 | INFO     | __main__:main:28 - 定时任务已启动，每1秒执行一次...
2025-07-03 12:50:25.482 | INFO     | __main__:main:29 - 按 Ctrl+C 可以停止程序
2025-07-03 12:50:26.484 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 12:50:26
2025-07-03 12:50:26.486 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:50:26.489 | INFO     | analyse_noappendix:__init__:392 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:50:26.489 | INFO     | analyse_noappendix:__init__:394 - 今日新增黑名单文档: 1 个
2025-07-03 12:50:26.490 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:50:27.430 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3280 条结果
2025-07-03 12:50:27.432 | INFO     | analyse_noappendix:process_one_record:753 - 排除 1 个黑名单文档
2025-07-03 12:50:27.433 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:50:27.910 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 119 条结果
2025-07-03 12:50:27.910 | INFO     | analyse_noappendix:process_one_record:771 - ID: -VsVzpcBsUtJ06Nf2nOH的附件链接为空
2025-07-03 12:50:27.910 | INFO     | analyse_noappendix:process_one_record:772 - 公告标题: 中医院门诊病房楼物资采购项目（二次）结果公告
2025-07-03 12:50:27.910 | INFO     | analyse_noappendix:process_one_record:773 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202507/t20250702_24888944.htm
2025-07-03 12:50:27.911 | INFO     | analyse_noappendix:process_one_record:774 - 公告类型: 004
2025-07-03 12:50:27.911 | INFO     | analyse_noappendix:process_one_record:775 - 附件链接: []
2025-07-03 12:50:28.972 | INFO     | analyse_noappendix:llm:304 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:50:28.973 | INFO     | analyse_noappendix:llm:319 - enable_thinking=False
2025-07-03 12:50:29.273 | INFO     | analyse_noappendix:llm:325 - extra_body={'enable_thinking': False}
2025-07-03 12:50:42.097 | INFO     | analyse_noappendix:llm:329 - LLM API调用成功
2025-07-03 12:50:42.097 | INFO     | analyse_noappendix:analyze_document:697 - [
    {
        "bid_name": "中医院门诊病房楼物资采购项目（1包）电脑设备",
        "bid_number": "001",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "海城市兴海大街195号",
        "prj_name": "中医院门诊病房楼物资采购项目（二次）",
        "prj_number": "JH25-210381-00819",
        "prj_type": "货物",
        "release_time": "2025-07-02 11:31:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "海城市中医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "辽宁省",
        "city": "鞍山市",
        "county": "海城市",
        "announcement_type": "004",
        "object_name": null,
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": null,
        "object_unit": null,
        "object_price": null,
        "object_total_price": null,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "海城市政务服务中心（海城市公共资源交易中心、海城市政府采购中心）",
        "service_fee": 0.0,
        "bid_cancelled_flag": "1",
        "bid_cancelled_reason": "通过符合性检查的供应商不足3家"
    }
]
2025-07-03 12:50:42.099 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -VsVzpcBsUtJ06Nf2nOH_0
2025-07-03 12:50:42.123 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -VsVzpcBsUtJ06Nf2nOH_0
2025-07-03 12:50:42.124 | INFO     | analyse_noappendix:process_one_record:829 - 成功插入文档 -VsVzpcBsUtJ06Nf2nOH_0
2025-07-03 12:50:42.124 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 12:50:26
2025-07-03 12:50:43.125 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 12:50:43
2025-07-03 12:50:43.127 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:50:43.133 | INFO     | analyse_noappendix:__init__:392 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:50:43.134 | INFO     | analyse_noappendix:__init__:394 - 今日新增黑名单文档: 1 个
2025-07-03 12:50:43.134 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:50:44.221 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3281 条结果
2025-07-03 12:50:44.224 | INFO     | analyse_noappendix:process_one_record:753 - 排除 1 个黑名单文档
2025-07-03 12:50:44.224 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:50:44.328 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 118 条结果
2025-07-03 12:50:44.328 | INFO     | analyse_noappendix:process_one_record:771 - ID: 0FsTzpcBsUtJ06NfTHNU的附件链接为空
2025-07-03 12:50:44.328 | INFO     | analyse_noappendix:process_one_record:772 - 公告标题: 湖北省肿瘤医院医用内窥镜采购中标成交公告
2025-07-03 12:50:44.329 | INFO     | analyse_noappendix:process_one_record:773 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202507/t20250702_24889470.htm
2025-07-03 12:50:44.329 | INFO     | analyse_noappendix:process_one_record:774 - 公告类型: 004
2025-07-03 12:50:44.329 | INFO     | analyse_noappendix:process_one_record:775 - 附件链接: []
2025-07-03 12:50:45.511 | INFO     | analyse_noappendix:llm:304 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:50:45.512 | INFO     | analyse_noappendix:llm:319 - enable_thinking=False
2025-07-03 12:50:45.512 | INFO     | analyse_noappendix:llm:325 - extra_body={'enable_thinking': False}
2025-07-03 12:51:00.877 | INFO     | analyse_noappendix:llm:329 - LLM API调用成功
2025-07-03 12:51:00.877 | INFO     | analyse_noappendix:analyze_document:697 - [
    {
        "bid_name": null,
        "bid_number": null,
        "bid_budget": null,
        "fiscal_delegation_number": "420000-2024-17864",
        "prj_addr": "武汉市洪山区卓刀泉南路 116 号",
        "prj_name": "湖北省肿瘤医院医用内窥镜采购",
        "prj_number": "420001202401003546",
        "prj_type": "货物",
        "release_time": "2025-07-02 12:44:00",
        "prj_approval_authority": null,
        "superintendent_office": "省本级",
        "superintendent_office_code": null,
        "tenderee": "湖北省肿瘤医院（湖北省肿瘤研究所）",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "湖北省",
        "city": "武汉市",
        "county": null,
        "announcement_type": "004",
        "object_name": "高清内镜主机、电子食管胃镜及电子结肠镜",
        "object_brand": "富士",
        "object_model": "VP-7000等",
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "套",
        "object_price": 3880000.0,
        "object_total_price": 3880000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 3880000.0,
        "bidder_name": "华润湖北医药有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "公诚管理咨询有限公司",
        "service_fee": 32676.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 12:51:00.879 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 0FsTzpcBsUtJ06NfTHNU_0
2025-07-03 12:51:00.898 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 0FsTzpcBsUtJ06NfTHNU_0
2025-07-03 12:51:00.898 | INFO     | analyse_noappendix:process_one_record:829 - 成功插入文档 0FsTzpcBsUtJ06NfTHNU_0
2025-07-03 12:51:00.899 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 12:50:43
2025-07-03 12:51:01.900 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 12:51:01
2025-07-03 12:51:01.902 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:51:01.907 | INFO     | analyse_noappendix:__init__:392 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:51:01.908 | INFO     | analyse_noappendix:__init__:394 - 今日新增黑名单文档: 1 个
2025-07-03 12:51:01.908 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:51:02.183 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3282 条结果
2025-07-03 12:51:02.185 | INFO     | analyse_noappendix:process_one_record:753 - 排除 1 个黑名单文档
2025-07-03 12:51:02.185 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:51:02.270 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 117 条结果
2025-07-03 12:51:02.271 | INFO     | analyse_noappendix:process_one_record:771 - ID: 0qEIzpcBfqDgtia4V2-8的附件链接为空
2025-07-03 12:51:02.271 | INFO     | analyse_noappendix:process_one_record:772 - 公告标题: 广西众联工程项目管理有限公司关于龙州县人民医院中联his系统维保服务（CZZC2025-C3-230061-GXZL）竞争性磋商公告
2025-07-03 12:51:02.271 | INFO     | analyse_noappendix:process_one_record:773 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202507/t20250702_24891697.htm
2025-07-03 12:51:02.272 | INFO     | analyse_noappendix:process_one_record:774 - 公告类型: 001
2025-07-03 12:51:02.272 | INFO     | analyse_noappendix:process_one_record:775 - 附件链接: []
2025-07-03 12:51:03.379 | INFO     | analyse_noappendix:llm:304 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:51:03.380 | INFO     | analyse_noappendix:llm:319 - enable_thinking=False
2025-07-03 12:51:03.380 | INFO     | analyse_noappendix:llm:325 - extra_body={'enable_thinking': False}
2025-07-03 12:51:21.217 | INFO     | analyse_noappendix:llm:329 - LLM API调用成功
2025-07-03 12:51:21.218 | INFO     | analyse_noappendix:analyze_document:697 - [
    {
        "bid_name": "龙州县人民医院中联his系统维保服务",
        "bid_number": null,
        "bid_budget": 560000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "广西壮族自治区崇左市龙州县龙州镇仁义街东一巷五号",
        "prj_name": "龙州县人民医院中联his系统维保服务",
        "prj_number": "CZZC2025-C3-230061-GXZL",
        "prj_type": "服务",
        "release_time": "2025-07-02 16:11:00",
        "prj_approval_authority": null,
        "superintendent_office": "龙州县财政局政府采购监督管理股",
        "superintendent_office_code": null,
        "tenderee": "龙州县人民医院",
        "bid_submission_deadline": "2025-07-14 09:30:00",
        "trade_platform": "广西政府采购云平台",
        "procurement_method": "竞争性磋商",
        "prj_sub_type": "维保",
        "province": "广西壮族自治区",
        "city": "崇左市",
        "county": "龙州县",
        "announcement_type": "001",
        "object_name": "中联his系统",
        "object_brand": "重庆中联信息产业有限责任公司",
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "为保证医院信息系统运行正常开展维护项目，需提供以下运维服务：\n一、知识库服务要求\n1、要求提供知识库：为用户提供知识库服务，作为交流、学习平台并提供知识库地址；……具体详见第二章 采购需求。",
        "object_oem": null,
        "object_amount": null,
        "object_unit": null,
        "object_price": null,
        "object_total_price": 560000.0,
        "object_maintenance_period": "自合同签订之日起一年",
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "广西众联工程项目管理有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 12:51:21.218 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 0qEIzpcBfqDgtia4V2-8_0
2025-07-03 12:51:21.240 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 0qEIzpcBfqDgtia4V2-8_0
2025-07-03 12:51:21.240 | INFO     | analyse_noappendix:process_one_record:829 - 成功插入文档 0qEIzpcBfqDgtia4V2-8_0
2025-07-03 12:51:21.241 | INFO     | __main__:job:16 - 定时任务执行完成 当前时间: 2025-07-03 12:51:01
2025-07-03 12:51:22.242 | INFO     | __main__:job:14 - 开始执行定时任务... 当前时间: 2025-07-03 12:51:22
2025-07-03 12:51:22.245 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 12:51:22.250 | INFO     | analyse_noappendix:__init__:392 - 黑名单中有 1 个文档将被跳过
2025-07-03 12:51:22.250 | INFO     | analyse_noappendix:__init__:394 - 今日新增黑名单文档: 1 个
2025-07-03 12:51:22.250 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 12:51:22.550 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3283 条结果
2025-07-03 12:51:22.552 | INFO     | analyse_noappendix:process_one_record:753 - 排除 1 个黑名单文档
2025-07-03 12:51:22.552 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 12:51:22.634 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 116 条结果
2025-07-03 12:51:22.634 | INFO     | analyse_noappendix:process_one_record:771 - ID: 1vIHzpcBGyYixO6vvd0y的附件链接为空
2025-07-03 12:51:22.634 | INFO     | analyse_noappendix:process_one_record:772 - 公告标题: 宝山区吴淞中心医院保洁费采购的公开招标公告
2025-07-03 12:51:22.635 | INFO     | analyse_noappendix:process_one_record:773 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202507/t20250702_24891749.htm
2025-07-03 12:51:22.635 | INFO     | analyse_noappendix:process_one_record:774 - 公告类型: 001
2025-07-03 12:51:22.635 | INFO     | analyse_noappendix:process_one_record:775 - 附件链接: []
2025-07-03 12:51:23.735 | INFO     | analyse_noappendix:llm:304 - 正在调用LLM API (尝试 1/3)...
2025-07-03 12:51:23.737 | INFO     | analyse_noappendix:llm:319 - enable_thinking=False
2025-07-03 12:51:23.737 | INFO     | analyse_noappendix:llm:325 - extra_body={'enable_thinking': False}
2025-07-03 12:51:39.272 | INFO     | __main__:main:37 - 程序被用户中断
2025-07-03 15:05:09.972 | INFO     | analyse_appendix:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段59个, 删除多余字段0个, 补全缺失字段0个
2025-07-03 15:05:09.977 | WARNING  | analyse_appendix:validate_and_normalize_fields:366 - 发现多余字段，将被删除: ['extra_field_1', 'extra_field_2', 'unknown_field']
2025-07-03 15:05:09.977 | INFO     | analyse_appendix:validate_and_normalize_fields:371 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'insert_time', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_id', 'source_title', 'source_url', 'superintendent_office', 'superintendent_office_code', 'tenderee', 'trade_platform']
2025-07-03 15:05:09.977 | INFO     | analyse_appendix:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段2个, 删除多余字段3个, 补全缺失字段57个
2025-07-03 15:05:09.977 | INFO     | analyse_appendix:validate_and_normalize_fields:371 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'insert_time', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_id', 'source_title', 'source_url', 'superintendent_office', 'superintendent_office_code', 'trade_platform']
2025-07-03 15:05:09.977 | INFO     | analyse_appendix:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段3个, 删除多余字段0个, 补全缺失字段56个
2025-07-03 15:05:09.977 | INFO     | analyse_appendix:validate_and_normalize_fields:371 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'insert_time', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_name', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_id', 'source_title', 'source_url', 'superintendent_office', 'superintendent_office_code', 'tenderee', 'trade_platform']
2025-07-03 15:05:09.977 | INFO     | analyse_appendix:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段0个, 删除多余字段0个, 补全缺失字段59个
2025-07-03 15:05:09.977 | WARNING  | analyse_appendix:validate_and_normalize_fields:356 - 文档不是字典类型: <class 'str'>
2025-07-03 15:05:09.982 | INFO     | analyse_appendix:validate_and_normalize_fields:371 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'county', 'fiscal_delegation_number', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_title', 'source_url', 'superintendent_office', 'superintendent_office_code', 'tenderee', 'trade_platform']
2025-07-03 15:05:09.982 | INFO     | analyse_appendix:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段6个, 删除多余字段0个, 补全缺失字段53个
2025-07-03 15:05:09.983 | WARNING  | analyse_appendix:validate_and_normalize_fields:366 - 发现多余字段，将被删除: ['debug_info', 'processing_status', 'temp_field']
2025-07-03 15:05:09.983 | INFO     | analyse_appendix:validate_and_normalize_fields:371 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'object_amount', 'object_conf', 'object_maintenance_period', 'object_model', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'superintendent_office', 'superintendent_office_code', 'trade_platform']
2025-07-03 15:05:09.984 | INFO     | analyse_appendix:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段13个, 删除多余字段3个, 补全缺失字段46个
2025-07-03 15:07:27.129 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 15:07:27.134 | INFO     | __main__:__init__:1747 - 黑名单中有 1 个文档将被跳过
2025-07-03 15:07:27.134 | INFO     | __main__:__init__:1749 - 今日新增黑名单文档: 1 个
2025-07-03 15:07:27.134 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 15:07:27.418 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3283 条结果
2025-07-03 15:07:27.423 | INFO     | __main__:process_one_record:2155 - 排除 1 个黑名单文档
2025-07-03 15:07:27.423 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 15:07:28.185 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7348 条结果
2025-07-03 15:07:28.185 | INFO     | __main__:process_one_record:2167 - ID: -3zTYZcBGyYixO6vSz-p 开始分析公告主体
2025-07-03 15:07:28.185 | INFO     | __main__:process_one_record:2168 - 公告标题: 绵竹市人民医院交换机采购项目(二次)竞争性谈判公告
2025-07-03 15:07:28.185 | INFO     | __main__:process_one_record:2169 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/jzxtpgg/202506/t20250611_24758939.htm
2025-07-03 15:07:29.489 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:07:29.490 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:07:29.892 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:07:43.949 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:07:43.949 | INFO     | __main__:analyze_content:2102 - [
    {
        "bid_name": "交换机采购项目(二次)",
        "bid_number": null,
        "bid_budget": 410000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "绵竹市剑南镇南京大道一段268号",
        "prj_name": "交换机采购项目(二次)",
        "prj_number": "N5106832025000062",
        "prj_type": "货物",
        "release_time": "2025-06-11 17:03:00",
        "prj_approval_authority": null,
        "superintendent_office": "绵竹市财政局",
        "superintendent_office_code": null,
        "tenderee": "绵竹市人民医院",
        "bid_submission_deadline": "2025-06-18 09:30:00",
        "trade_platform": "四川省政府采购一体化平台项目电子化交易系统",
        "procurement_method": "竞争性谈判",
        "prj_sub_type": "设备",
        "province": "四川省",
        "city": "德阳市",
        "county": "绵竹市",
        "announcement_type": "001",
        "object_name": "交换机",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": null,
        "object_unit": null,
        "object_price": null,
        "object_total_price": 410000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "四川三和源招标代理有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 15:07:43.955 | INFO     | __main__:process_one_record:2240 - ID: -3zTYZcBGyYixO6vSz-p 发现附件，开始分析附件
2025-07-03 15:07:43.955 | INFO     | __main__:process_one_record:2241 - 附件列表: [{'url': 'https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc', 'text': '采购需求'}, {'url': 'https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1', 'text': '意向公开时间及链接'}]
2025-07-03 15:07:43.955 | INFO     | __main__:process_one_record:2252 - 正在下载附件: https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc
2025-07-03 15:07:43.955 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 1/3): https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc
2025-07-03 15:07:44.080 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-03 15:07:44.080 | INFO     | __main__:download_file:806 - 检测到网络错误，等待 5 秒后重试...
2025-07-03 15:07:49.083 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 2/3): https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc
2025-07-03 15:07:49.248 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-03 15:07:49.248 | INFO     | __main__:download_file:806 - 检测到网络错误，等待 10 秒后重试...
2025-07-03 15:07:59.253 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 3/3): https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc
2025-07-03 15:07:59.374 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-03 15:07:59.374 | ERROR    | __main__:download_file:789 - 文件下载最终失败: https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc
2025-07-03 15:07:59.374 | INFO     | __main__:process_one_record:2252 - 正在下载附件: https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1
2025-07-03 15:07:59.374 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 1/3): https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1
2025-07-03 15:07:59.493 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 1/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-03 15:07:59.493 | INFO     | __main__:download_file:806 - 检测到网络错误，等待 5 秒后重试...
2025-07-03 15:08:04.494 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 2/3): https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1
2025-07-03 15:08:04.674 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 2/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-03 15:08:04.674 | INFO     | __main__:download_file:806 - 检测到网络错误，等待 10 秒后重试...
2025-07-03 15:08:14.676 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 3/3): https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1
2025-07-03 15:08:14.805 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 3/3): ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-03 15:08:14.805 | ERROR    | __main__:download_file:789 - 文件下载最终失败: https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2623 - ================================================================================
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2624 - 开始融合分析结果
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2625 - ================================================================================
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2626 - 主体解析结果数量: 1
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2627 - 招标文件解析结果数量: 0
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2628 - 合同文件解析结果数量: 0
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2631 - 主体解析结果:
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2633 - 主体结果 1:
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2634 - {
  "bid_name": "交换机采购项目(二次)",
  "bid_number": null,
  "bid_budget": 410000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "绵竹市剑南镇南京大道一段268号",
  "prj_name": "交换机采购项目(二次)",
  "prj_number": "N5106832025000062",
  "prj_type": "货物",
  "release_time": "2025-06-11 17:03:00",
  "prj_approval_authority": null,
  "superintendent_office": "绵竹市财政局",
  "superintendent_office_code": null,
  "tenderee": "绵竹市人民医院",
  "bid_submission_deadline": "2025-06-18 09:30:00",
  "trade_platform": "四川省政府采购一体化平台项目电子化交易系统",
  "procurement_method": "竞争性谈判",
  "prj_sub_type": "设备",
  "province": "四川省",
  "city": "德阳市",
  "county": "绵竹市",
  "announcement_type": "001",
  "object_name": "交换机",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": null,
  "object_unit": null,
  "object_price": null,
  "object_total_price": 410000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "四川三和源招标代理有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-3zTYZcBGyYixO6vSz-p",
  "source_title": "绵竹市人民医院交换机采购项目(二次)竞争性谈判公告",
  "source_create_time": "2025-06-12",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxtpgg/202506/t20250611_24758939.htm",
  "source_appendix": [
    {
      "url": "https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc",
      "text": "采购需求"
    },
    {
      "url": "https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1",
      "text": "意向公开时间及链接"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:07:43"
}
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2658 - 使用智能融合分析
2025-07-03 15:08:14.805 | INFO     | __main__:intelligent_merge_analysis:1561 - 开始基于object_name的匹配融合分析
2025-07-03 15:08:14.805 | INFO     | __main__:merge_analysis_by_object_name:1519 - 主体标的物 '交换机' 匹配结果:
2025-07-03 15:08:14.805 | INFO     | __main__:merge_analysis_by_object_name:1520 -   - 招标文件匹配: 否
2025-07-03 15:08:14.805 | INFO     | __main__:merge_analysis_by_object_name:1521 -   - 合同文件匹配: 否
2025-07-03 15:08:14.805 | INFO     | __main__:intelligent_merge_analysis:1569 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 15:08:14.805 | INFO     | __main__:intelligent_merge_analysis:1698 - 未提供文档内容或模型配置，直接返回基础融合结果
2025-07-03 15:08:14.805 | INFO     | __main__:process_one_record:2673 - ================================================================================
2025-07-03 15:08:14.810 | INFO     | __main__:process_one_record:2674 - 最终融合后的JSON结果列表:
2025-07-03 15:08:14.810 | INFO     | __main__:process_one_record:2675 - ================================================================================
2025-07-03 15:08:14.810 | INFO     | __main__:process_one_record:2677 - 融合结果 1:
2025-07-03 15:08:14.810 | INFO     | __main__:process_one_record:2678 - {
  "bid_name": "交换机采购项目(二次)",
  "bid_number": null,
  "bid_budget": 410000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "绵竹市剑南镇南京大道一段268号",
  "prj_name": "交换机采购项目(二次)",
  "prj_number": "N5106832025000062",
  "prj_type": "货物",
  "release_time": "2025-06-11 17:03:00",
  "prj_approval_authority": null,
  "superintendent_office": "绵竹市财政局",
  "superintendent_office_code": null,
  "tenderee": "绵竹市人民医院",
  "bid_submission_deadline": "2025-06-18 09:30:00",
  "trade_platform": "四川省政府采购一体化平台项目电子化交易系统",
  "procurement_method": "竞争性谈判",
  "prj_sub_type": "设备",
  "province": "四川省",
  "city": "德阳市",
  "county": "绵竹市",
  "announcement_type": "001",
  "object_name": "交换机",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": null,
  "object_unit": null,
  "object_price": null,
  "object_total_price": 410000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "四川三和源招标代理有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-3zTYZcBGyYixO6vSz-p",
  "source_title": "绵竹市人民医院交换机采购项目(二次)竞争性谈判公告",
  "source_create_time": "2025-06-12",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxtpgg/202506/t20250611_24758939.htm",
  "source_appendix": [
    {
      "url": "https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69c457975b393601975e217e4a76dd.pdf?accessCode=d7b552469adee9afbd40101f97cf62fc",
      "text": "采购需求"
    },
    {
      "url": "https://gpx.ccgp-sichuan.gov.cn/gpx-bid-file/ZF_JGBM_000008/zone/2024/12/15/project/gpx-template/8a69f78b975b38aa01975e21b50f7bfc.docx?accessCode=860e76cb5e4a860df5d14a849dd58ae1",
      "text": "意向公开时间及链接"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:07:43"
}
2025-07-03 15:08:14.810 | INFO     | __main__:process_one_record:2679 - ------------------------------------------------------------
2025-07-03 15:08:14.810 | INFO     | __main__:process_one_record:2680 - ================================================================================
2025-07-03 15:08:14.810 | INFO     | __main__:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段27个, 删除多余字段0个, 补全缺失字段0个
2025-07-03 15:08:14.810 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -3zTYZcBGyYixO6vSz-p_0
2025-07-03 15:08:14.837 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -3zTYZcBGyYixO6vSz-p_0
2025-07-03 15:08:14.837 | INFO     | __main__:process_one_record:2695 - 成功插入融合文档 -3zTYZcBGyYixO6vSz-p_0
2025-07-03 15:08:31.783 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 15:08:31.788 | INFO     | __main__:__init__:1747 - 黑名单中有 1 个文档将被跳过
2025-07-03 15:08:31.788 | INFO     | __main__:__init__:1749 - 今日新增黑名单文档: 1 个
2025-07-03 15:08:31.788 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 15:08:32.883 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3284 条结果
2025-07-03 15:08:32.883 | INFO     | __main__:process_one_record:2155 - 排除 1 个黑名单文档
2025-07-03 15:08:32.883 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 15:08:33.562 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7347 条结果
2025-07-03 15:08:33.562 | INFO     | __main__:process_one_record:2167 - ID: -4EVZ5cBGyYixO6vu-HK 开始分析公告主体
2025-07-03 15:08:33.562 | INFO     | __main__:process_one_record:2168 - 公告标题: 创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告
2025-07-03 15:08:33.562 | INFO     | __main__:process_one_record:2169 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm
2025-07-03 15:08:34.505 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:08:34.505 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:08:34.927 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:09:10.109 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:09:10.109 | INFO     | __main__:analyze_content:2102 - [
    {
        "bid_name": "包一",
        "bid_number": null,
        "bid_budget": 720000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "青海省西宁市城西区同仁路29号",
        "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
        "prj_number": "创鑫竞磋（服务）2025-CXQGC2024007",
        "prj_type": "服务",
        "release_time": "2025-06-12 18:12:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "青海大学附属医院",
        "bid_submission_deadline": "2025-06-23 09:30:00",
        "trade_platform": "政采云平台",
        "procurement_method": "竞争性磋商",
        "prj_sub_type": "其他",
        "province": "青海省",
        "city": "西宁市",
        "county": "城西区",
        "announcement_type": "001",
        "object_name": "基坑支护检测、变形观测、原材料见证取样检测等综合检测服务",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "检测内容包括但不限于基坑支护检测（物探测试检测监测）及基坑变形观测、原材料见证取样检测、地基基础工程检测、主体结构工程现场检测、减震隔震产品的检测、钢结构工程制作安装相关检测、建筑幕墙工程相关检测、建筑防雷接地、建筑节能工程相关检测、室内空气检测、能效检测测评、建筑物沉降观测、道路工程检测、人防消防工程材料等检测及设计图纸文件所要求的所有内容的材料、性能及相关检测。",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": 720000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "创鑫工程咨询股份有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    },
    {
        "bid_name": "包二",
        "bid_number": null,
        "bid_budget": 30000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "青海省西宁市城西区同仁路29号",
        "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
        "prj_number": "创鑫竞磋（服务）2025-CXQGC2024007",
        "prj_type": "服务",
        "release_time": "2025-06-12 18:12:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "青海大学附属医院",
        "bid_submission_deadline": "2025-06-23 09:30:00",
        "trade_platform": "政采云平台",
        "procurement_method": "竞争性磋商",
        "prj_sub_type": "其他",
        "province": "青海省",
        "city": "西宁市",
        "county": "城西区",
        "announcement_type": "001",
        "object_name": "普通硅酸盐水泥、细砂、配合比、电线等材料检测服务",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "检测内容包括普通硅酸盐水泥（4组）、细砂（4组）、配合比（2组）、电线（2组）、陶瓷砖（地砖、墙砖）各2组、采暖散热器（2组）、JS聚合物水泥防水涂料（2组）、钢结构防火涂料常规（2组）、树脂板（2组）、PVC同质透心型卷材地板（2组）、给水管PPR（2组）、抗菌耐擦洗乳胶漆（2组）、窗帘布（B1级）（2组）、窗户玻璃（性能检测）（2组）、SBS改性沥青防水卷材（2组），如有增加检测项目及内容，其费用已包括此次招标范围内。",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": 30000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "创鑫工程咨询股份有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 15:09:10.111 | INFO     | __main__:process_one_record:2240 - ID: -4EVZ5cBGyYixO6vu-HK 发现附件，开始分析附件
2025-07-03 15:09:10.111 | INFO     | __main__:process_one_record:2241 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf', 'text': '青海大学附属医院遴选病房改造提升建设项目工程检测单位项目'}]
2025-07-03 15:09:10.111 | INFO     | __main__:process_one_record:2252 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:10.111 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:10.357 | INFO     | __main__:download_file:781 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:10.362 | INFO     | __main__:process_one_record:2294 - 附件已保存到: downloads\青海大学附属医院遴选病房改造提升建设项目工程检测单位项目.pdf
2025-07-03 15:09:10.362 | INFO     | __main__:process_one_record:2318 - 使用markitdown转换文件: 青海大学附属医院遴选病房改造提升建设项目工程检测单位项目.pdf
2025-07-03 15:09:14.469 | DEBUG    | __main__:convert_to_markdown_with_markitdown:891 - markitdown成功转换.pdf文件，生成40860字符的Markdown
2025-07-03 15:09:14.469 | INFO     | __main__:process_one_record:2360 - 正在分析文件: 9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:14.807 | DEBUG    | __main__:extract_preview_text:1214 - Successfully extracted PDF preview with pdfplumber, 3195 characters
2025-07-03 15:09:14.807 | INFO     | __main__:detect_file_type:1258 - 此文件是招标文件: 青海大学附属医院遴选病房改造提升建设项目工程检测单位项目 (匹配关键词: ['磋商文件'])
2025-07-03 15:09:14.807 | INFO     | __main__:process_one_record:2371 - 存储招标文件内容: 9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:14.807 | INFO     | __main__:process_one_record:2389 - 开始上传招标文件: 9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:14.807 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-4EVZ5cBGyYixO6vu-HK.pdf
2025-07-03 15:09:14.810 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-4EVZ5cBGyYixO6vu-HK.pdf, 大小: 502349 字节
2025-07-03 15:09:14.810 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=502349&objectName=招标文件_-4EVZ5cBGyYixO6vu-HK.pdf
2025-07-03 15:09:14.827 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197cf1e428875f99763b30a40c785a8', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197cf1e428875f99763b30a40c785a8/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-4EVZ5cBGyYixO6vu-HK.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T070916Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=b53f96d08a7480967a86e29fcb61e10222affae0e086f624989d94f2c9e7d465'}, 'timestamp': 1751526556303, 'callId': None}
2025-07-03 15:09:14.828 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 502349 字节
2025-07-03 15:09:14.922 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 15:09:14.922 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197cf1e428875f99763b30a40c785a8
2025-07-03 15:09:15.017 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 15:09:15.018 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-4EVZ5cBGyYixO6vu-HK.pdf
2025-07-03 15:09:15.018 | INFO     | __main__:process_one_record:2402 - ✓ 招标文件上传成功: 招标文件_-4EVZ5cBGyYixO6vu-HK.pdf, upload_id: 0197cf1e428875f99763b30a40c785a8
2025-07-03 15:09:16.150 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:09:16.150 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:09:16.150 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:09:50.141 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:09:50.141 | INFO     | __main__:analyze_content:2102 - [
    {
        "bid_name": "包一",
        "bid_number": "CXJC(服务)2025-CXQGC2024007-1",
        "bid_budget": 720000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "青海省西宁市城中区南川西路158号青海大学附属医院南院区",
        "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
        "prj_number": "CXJC(服务)2025-CXQGC2024007",
        "prj_type": "服务",
        "release_time": "2025-06-11 00:00:00",
        "prj_approval_authority": null,
        "superintendent_office": "青海省财政厅",
        "superintendent_office_code": null,
        "tenderee": "青海大学附属医院",
        "bid_submission_deadline": "2025-06-23 09:30:00",
        "trade_platform": "青海省政府采购网",
        "procurement_method": "竞争性磋商",
        "prj_sub_type": "其他",
        "province": "青海省",
        "city": "西宁市",
        "county": "城中区",
        "announcement_type": "001",
        "object_name": "基坑支护检测及变形观测",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "检测内容包括但不限于基坑支护检测（物探测试检测监测）及基坑变形观测",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": null,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "创鑫工程咨询股份有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    },
    {
        "bid_name": "包二",
        "bid_number": "CXJC(服务)2025-CXQGC2024007-2",
        "bid_budget": 30000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "青海省西宁市城西区同仁路29号",
        "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
        "prj_number": "CXJC(服务)2025-CXQGC2024007",
        "prj_type": "服务",
        "release_time": "2025-06-11 00:00:00",
        "prj_approval_authority": null,
        "superintendent_office": "青海省财政厅",
        "superintendent_office_code": null,
        "tenderee": "青海大学附属医院",
        "bid_submission_deadline": "2025-06-23 09:30:00",
        "trade_platform": "青海省政府采购网",
        "procurement_method": "竞争性磋商",
        "prj_sub_type": "其他",
        "province": "青海省",
        "city": "西宁市",
        "county": "城西区",
        "announcement_type": "001",
        "object_name": "普通硅酸盐水泥检测",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "检测4组",
        "object_oem": null,
        "object_amount": 4,
        "object_unit": "组",
        "object_price": null,
        "object_total_price": null,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "创鑫工程咨询股份有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 15:09:50.141 | INFO     | __main__:process_one_record:2523 - 已找到并分析招标文件: 9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:50.141 | INFO     | __main__:process_one_record:2523 - 已找到并分析招标文件: 9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2623 - ================================================================================
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2624 - 开始融合分析结果
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2625 - ================================================================================
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2626 - 主体解析结果数量: 2
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2627 - 招标文件解析结果数量: 2
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2628 - 合同文件解析结果数量: 0
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2631 - 主体解析结果:
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2633 - 主体结果 1:
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2634 - {
  "bid_name": "包一",
  "bid_number": null,
  "bid_budget": 720000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "青海省西宁市城西区同仁路29号",
  "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
  "prj_number": "创鑫竞磋（服务）2025-CXQGC2024007",
  "prj_type": "服务",
  "release_time": "2025-06-12 18:12:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "青海大学附属医院",
  "bid_submission_deadline": "2025-06-23 09:30:00",
  "trade_platform": "政采云平台",
  "procurement_method": "竞争性磋商",
  "prj_sub_type": "其他",
  "province": "青海省",
  "city": "西宁市",
  "county": "城西区",
  "announcement_type": "001",
  "object_name": "基坑支护检测、变形观测、原材料见证取样检测等综合检测服务",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "检测内容包括但不限于基坑支护检测（物探测试检测监测）及基坑变形观测、原材料见证取样检测、地基基础工程检测、主体结构工程现场检测、减震隔震产品的检测、钢结构工程制作安装相关检测、建筑幕墙工程相关检测、建筑防雷接地、建筑节能工程相关检测、室内空气检测、能效检测测评、建筑物沉降观测、道路工程检测、人防消防工程材料等检测及设计图纸文件所要求的所有内容的材料、性能及相关检测。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "项",
  "object_price": null,
  "object_total_price": 720000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "创鑫工程咨询股份有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-4EVZ5cBGyYixO6vu-HK",
  "source_title": "创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告",
  "source_create_time": "2025-06-13",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
      "text": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:09:10"
}
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2633 - 主体结果 2:
2025-07-03 15:09:50.146 | INFO     | __main__:process_one_record:2634 - {
  "bid_name": "包二",
  "bid_number": null,
  "bid_budget": 30000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "青海省西宁市城西区同仁路29号",
  "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
  "prj_number": "创鑫竞磋（服务）2025-CXQGC2024007",
  "prj_type": "服务",
  "release_time": "2025-06-12 18:12:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "青海大学附属医院",
  "bid_submission_deadline": "2025-06-23 09:30:00",
  "trade_platform": "政采云平台",
  "procurement_method": "竞争性磋商",
  "prj_sub_type": "其他",
  "province": "青海省",
  "city": "西宁市",
  "county": "城西区",
  "announcement_type": "001",
  "object_name": "普通硅酸盐水泥、细砂、配合比、电线等材料检测服务",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "检测内容包括普通硅酸盐水泥（4组）、细砂（4组）、配合比（2组）、电线（2组）、陶瓷砖（地砖、墙砖）各2组、采暖散热器（2组）、JS聚合物水泥防水涂料（2组）、钢结构防火涂料常规（2组）、树脂板（2组）、PVC同质透心型卷材地板（2组）、给水管PPR（2组）、抗菌耐擦洗乳胶漆（2组）、窗帘布（B1级）（2组）、窗户玻璃（性能检测）（2组）、SBS改性沥青防水卷材（2组），如有增加检测项目及内容，其费用已包括此次招标范围内。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "项",
  "object_price": null,
  "object_total_price": 30000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "创鑫工程咨询股份有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-4EVZ5cBGyYixO6vu-HK",
  "source_title": "创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告",
  "source_create_time": "2025-06-13",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
      "text": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:09:10"
}
2025-07-03 15:09:50.151 | INFO     | __main__:process_one_record:2639 - 招标文件解析结果:
2025-07-03 15:09:50.151 | INFO     | __main__:process_one_record:2641 - 招标文件结果 1:
2025-07-03 15:09:50.151 | INFO     | __main__:process_one_record:2642 - {
  "bid_name": "包一",
  "bid_number": "CXJC(服务)2025-CXQGC2024007-1",
  "bid_budget": 720000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "青海省西宁市城中区南川西路158号青海大学附属医院南院区",
  "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
  "prj_number": "CXJC(服务)2025-CXQGC2024007",
  "prj_type": "服务",
  "release_time": "2025-06-11 00:00:00",
  "prj_approval_authority": null,
  "superintendent_office": "青海省财政厅",
  "superintendent_office_code": null,
  "tenderee": "青海大学附属医院",
  "bid_submission_deadline": "2025-06-23 09:30:00",
  "trade_platform": "青海省政府采购网",
  "procurement_method": "竞争性磋商",
  "prj_sub_type": "其他",
  "province": "青海省",
  "city": "西宁市",
  "county": "城中区",
  "announcement_type": "001",
  "object_name": "基坑支护检测及变形观测",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "检测内容包括但不限于基坑支护检测（物探测试检测监测）及基坑变形观测",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "项",
  "object_price": null,
  "object_total_price": null,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "创鑫工程咨询股份有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-4EVZ5cBGyYixO6vu-HK",
  "source_title": "创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告",
  "source_create_time": "2025-06-13",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
      "text": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目"
    }
  ],
  "insert_time": "2025-07-03 15:09:50",
  "bid_doc_name": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目.pdf",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
  "bid_doc_link_key": "0197cf1e428875f99763b30a40c785a8",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null
}
2025-07-03 15:09:50.153 | INFO     | __main__:process_one_record:2641 - 招标文件结果 2:
2025-07-03 15:09:50.153 | INFO     | __main__:process_one_record:2642 - {
  "bid_name": "包二",
  "bid_number": "CXJC(服务)2025-CXQGC2024007-2",
  "bid_budget": 30000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "青海省西宁市城西区同仁路29号",
  "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
  "prj_number": "CXJC(服务)2025-CXQGC2024007",
  "prj_type": "服务",
  "release_time": "2025-06-11 00:00:00",
  "prj_approval_authority": null,
  "superintendent_office": "青海省财政厅",
  "superintendent_office_code": null,
  "tenderee": "青海大学附属医院",
  "bid_submission_deadline": "2025-06-23 09:30:00",
  "trade_platform": "青海省政府采购网",
  "procurement_method": "竞争性磋商",
  "prj_sub_type": "其他",
  "province": "青海省",
  "city": "西宁市",
  "county": "城西区",
  "announcement_type": "001",
  "object_name": "普通硅酸盐水泥检测",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "检测4组",
  "object_oem": null,
  "object_amount": 4,
  "object_unit": "组",
  "object_price": null,
  "object_total_price": null,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "创鑫工程咨询股份有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-4EVZ5cBGyYixO6vu-HK",
  "source_title": "创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告",
  "source_create_time": "2025-06-13",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
      "text": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目"
    }
  ],
  "insert_time": "2025-07-03 15:09:50",
  "bid_doc_name": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目.pdf",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
  "bid_doc_link_key": "0197cf1e428875f99763b30a40c785a8",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null
}
2025-07-03 15:09:50.154 | INFO     | __main__:process_one_record:2658 - 使用智能融合分析
2025-07-03 15:09:50.154 | INFO     | __main__:intelligent_merge_analysis:1561 - 开始基于object_name的匹配融合分析
2025-07-03 15:09:50.155 | INFO     | __main__:merge_analysis_by_object_name:1519 - 主体标的物 '基坑支护检测、变形观测、原材料见证取样检测等综合检测服务' 匹配结果:
2025-07-03 15:09:50.156 | INFO     | __main__:merge_analysis_by_object_name:1520 -   - 招标文件匹配: 否
2025-07-03 15:09:50.156 | INFO     | __main__:merge_analysis_by_object_name:1521 -   - 合同文件匹配: 否
2025-07-03 15:09:50.156 | INFO     | __main__:merge_analysis_by_object_name:1519 - 主体标的物 '普通硅酸盐水泥、细砂、配合比、电线等材料检测服务' 匹配结果:
2025-07-03 15:09:50.156 | INFO     | __main__:merge_analysis_by_object_name:1520 -   - 招标文件匹配: 否
2025-07-03 15:09:50.156 | INFO     | __main__:merge_analysis_by_object_name:1521 -   - 合同文件匹配: 否
2025-07-03 15:09:50.156 | INFO     | __main__:intelligent_merge_analysis:1569 - 基于object_name的匹配融合完成，产生2个结果
2025-07-03 15:09:50.156 | INFO     | __main__:intelligent_merge_analysis:1578 - 开始对融合结果进行智能补充
2025-07-03 15:09:50.156 | INFO     | __main__:intelligent_merge_analysis:1582 - 处理第1个融合结果的智能补充
2025-07-03 15:09:50.156 | INFO     | __main__:intelligent_merge_analysis:1595 - 发现空缺字段: ['bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_price', 'bidder_name', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:09:50.156 | INFO     | __main__:intelligent_merge_analysis:1661 - 从招标文件内容中检索剩余字段: ['bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:09:50.156 | WARNING  | __main__:extract_fields_from_content:236 - 智能融合文档内容过长(40860字符)，截取前32768字符
2025-07-03 15:09:51.081 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:09:51.081 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:09:51.081 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:10:01.375 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:10:01.375 | INFO     | __main__:extract_fields_from_content:337 - 从文档内容中提取到字段: ['bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:10:01.375 | INFO     | __main__:intelligent_merge_analysis:1681 - 从招标文件补充字段 bid_number: 2
2025-07-03 15:10:01.375 | INFO     | __main__:intelligent_merge_analysis:1681 - 从招标文件补充字段 superintendent_office: 青海省财政厅
2025-07-03 15:10:01.381 | INFO     | __main__:intelligent_merge_analysis:1693 - 第1个融合结果智能补充完成
2025-07-03 15:10:01.381 | INFO     | __main__:intelligent_merge_analysis:1582 - 处理第2个融合结果的智能补充
2025-07-03 15:10:01.381 | INFO     | __main__:intelligent_merge_analysis:1595 - 发现空缺字段: ['bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_price', 'bidder_name', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:10:01.381 | INFO     | __main__:intelligent_merge_analysis:1661 - 从招标文件内容中检索剩余字段: ['bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:10:01.381 | WARNING  | __main__:extract_fields_from_content:236 - 智能融合文档内容过长(40860字符)，截取前32768字符
2025-07-03 15:10:02.320 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:10:02.320 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:10:02.320 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:10:11.161 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:10:11.161 | INFO     | __main__:extract_fields_from_content:337 - 从文档内容中提取到字段: ['bid_number', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price', 'object_maintenance_period', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:10:11.161 | INFO     | __main__:intelligent_merge_analysis:1681 - 从招标文件补充字段 bid_number: 2
2025-07-03 15:10:11.161 | INFO     | __main__:intelligent_merge_analysis:1681 - 从招标文件补充字段 superintendent_office: 青海省财政厅
2025-07-03 15:10:11.161 | INFO     | __main__:intelligent_merge_analysis:1693 - 第2个融合结果智能补充完成
2025-07-03 15:10:11.161 | INFO     | __main__:intelligent_merge_analysis:1695 - 智能补充完成，返回2个增强结果
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2673 - ================================================================================
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2674 - 最终融合后的JSON结果列表:
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2675 - ================================================================================
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2677 - 融合结果 1:
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2678 - {
  "bid_name": "包一",
  "bid_number": "2",
  "bid_budget": 720000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "青海省西宁市城西区同仁路29号",
  "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
  "prj_number": "创鑫竞磋（服务）2025-CXQGC2024007",
  "prj_type": "服务",
  "release_time": "2025-06-12 18:12:00",
  "prj_approval_authority": null,
  "superintendent_office": "青海省财政厅",
  "superintendent_office_code": null,
  "tenderee": "青海大学附属医院",
  "bid_submission_deadline": "2025-06-23 09:30:00",
  "trade_platform": "政采云平台",
  "procurement_method": "竞争性磋商",
  "prj_sub_type": "其他",
  "province": "青海省",
  "city": "西宁市",
  "county": "城西区",
  "announcement_type": "001",
  "object_name": "基坑支护检测、变形观测、原材料见证取样检测等综合检测服务",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "检测内容包括但不限于基坑支护检测（物探测试检测监测）及基坑变形观测、原材料见证取样检测、地基基础工程检测、主体结构工程现场检测、减震隔震产品的检测、钢结构工程制作安装相关检测、建筑幕墙工程相关检测、建筑防雷接地、建筑节能工程相关检测、室内空气检测、能效检测测评、建筑物沉降观测、道路工程检测、人防消防工程材料等检测及设计图纸文件所要求的所有内容的材料、性能及相关检测。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "项",
  "object_price": null,
  "object_total_price": 720000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "创鑫工程咨询股份有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-4EVZ5cBGyYixO6vu-HK",
  "source_title": "创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告",
  "source_create_time": "2025-06-13",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
      "text": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:09:10"
}
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2679 - ------------------------------------------------------------
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2677 - 融合结果 2:
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2678 - {
  "bid_name": "包二",
  "bid_number": "2",
  "bid_budget": 30000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "青海省西宁市城西区同仁路29号",
  "prj_name": "青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目",
  "prj_number": "创鑫竞磋（服务）2025-CXQGC2024007",
  "prj_type": "服务",
  "release_time": "2025-06-12 18:12:00",
  "prj_approval_authority": null,
  "superintendent_office": "青海省财政厅",
  "superintendent_office_code": null,
  "tenderee": "青海大学附属医院",
  "bid_submission_deadline": "2025-06-23 09:30:00",
  "trade_platform": "政采云平台",
  "procurement_method": "竞争性磋商",
  "prj_sub_type": "其他",
  "province": "青海省",
  "city": "西宁市",
  "county": "城西区",
  "announcement_type": "001",
  "object_name": "普通硅酸盐水泥、细砂、配合比、电线等材料检测服务",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "检测内容包括普通硅酸盐水泥（4组）、细砂（4组）、配合比（2组）、电线（2组）、陶瓷砖（地砖、墙砖）各2组、采暖散热器（2组）、JS聚合物水泥防水涂料（2组）、钢结构防火涂料常规（2组）、树脂板（2组）、PVC同质透心型卷材地板（2组）、给水管PPR（2组）、抗菌耐擦洗乳胶漆（2组）、窗帘布（B1级）（2组）、窗户玻璃（性能检测）（2组）、SBS改性沥青防水卷材（2组），如有增加检测项目及内容，其费用已包括此次招标范围内。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "项",
  "object_price": null,
  "object_total_price": 30000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "创鑫工程咨询股份有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-4EVZ5cBGyYixO6vu-HK",
  "source_title": "创鑫工程咨询股份有限公司关于青海大学附属医院遴选“病房改造提升建设项目”工程检测单位项目的竞争性磋商公告",
  "source_create_time": "2025-06-13",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/jzxcs/202506/t20250612_24768365.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1023FP/639900/10007428210/20256/9fd5d8dd-3c70-494b-bd23-1aed3327432c.pdf",
      "text": "青海大学附属医院遴选病房改造提升建设项目工程检测单位项目"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:09:10"
}
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2679 - ------------------------------------------------------------
2025-07-03 15:10:11.166 | INFO     | __main__:process_one_record:2680 - ================================================================================
2025-07-03 15:10:11.166 | INFO     | __main__:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段31个, 删除多余字段0个, 补全缺失字段0个
2025-07-03 15:10:11.166 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -4EVZ5cBGyYixO6vu-HK_0
2025-07-03 15:10:11.183 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -4EVZ5cBGyYixO6vu-HK_0
2025-07-03 15:10:11.183 | INFO     | __main__:process_one_record:2695 - 成功插入融合文档 -4EVZ5cBGyYixO6vu-HK_0
2025-07-03 15:10:11.183 | INFO     | __main__:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段31个, 删除多余字段0个, 补全缺失字段0个
2025-07-03 15:10:11.183 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -4EVZ5cBGyYixO6vu-HK_1
2025-07-03 15:10:11.192 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -4EVZ5cBGyYixO6vu-HK_1
2025-07-03 15:10:11.192 | INFO     | __main__:process_one_record:2695 - 成功插入融合文档 -4EVZ5cBGyYixO6vu-HK_1
2025-07-03 15:17:47.336 | INFO     | analyse_noappendix:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'superintendent_office', 'superintendent_office_code', 'trade_platform']
2025-07-03 15:17:47.336 | INFO     | analyse_noappendix:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段8个, 删除多余字段0个, 补全缺失字段51个
2025-07-03 15:17:47.336 | WARNING  | analyse_noappendix:validate_and_normalize_fields:101 - 发现多余字段，将被删除: ['debug_field', 'processing_info', 'temp_data']
2025-07-03 15:17:47.340 | INFO     | analyse_noappendix:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'insert_time', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_title', 'source_url', 'superintendent_office', 'superintendent_office_code', 'tenderee', 'trade_platform']
2025-07-03 15:17:47.340 | INFO     | analyse_noappendix:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段3个, 删除多余字段3个, 补全缺失字段56个
2025-07-03 15:17:47.340 | INFO     | analyse_noappendix:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_name', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_url', 'superintendent_office', 'superintendent_office_code', 'tenderee', 'trade_platform']
2025-07-03 15:17:47.340 | INFO     | analyse_noappendix:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段3个, 删除多余字段0个, 补全缺失字段56个
2025-07-03 15:17:48.863 | INFO     | analyse_noappendix:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['agent', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bidder_name', 'bidder_price', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'object_amount', 'object_conf', 'object_maintenance_period', 'object_model', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_sub_type', 'prj_type', 'procurement_method', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'superintendent_office', 'superintendent_office_code', 'trade_platform']
2025-07-03 15:17:48.863 | INFO     | analyse_noappendix:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段13个, 删除多余字段0个, 补全缺失字段46个
2025-07-03 15:17:48.866 | INFO     | analyse_noappendix:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['agent', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'city', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'object_amount', 'object_brand', 'object_conf', 'object_maintenance_period', 'object_model', 'object_name', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_name', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'province', 'release_time', 'service_fee', 'source_appendix', 'source_category', 'source_create_time', 'source_url', 'superintendent_office', 'superintendent_office_code', 'tenderee', 'trade_platform']
2025-07-03 15:17:48.866 | INFO     | analyse_noappendix:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段7个, 删除多余字段0个, 补全缺失字段52个
2025-07-03 15:17:48.866 | WARNING  | analyse_noappendix:validate_and_normalize_fields:101 - 发现多余字段，将被删除: ['confidence_score', 'extraction_method', 'model_version']
2025-07-03 15:17:48.866 | INFO     | analyse_noappendix:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['agent', 'announcement_type', 'bid_budget', 'bid_cancelled_flag', 'bid_cancelled_reason', 'bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'bid_number', 'bid_submission_deadline', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name', 'county', 'fiscal_delegation_number', 'object_amount', 'object_conf', 'object_maintenance_period', 'object_model', 'object_oem', 'object_price', 'object_price_source', 'object_produce_area', 'object_quality', 'object_supplier', 'object_total_price', 'object_unit', 'prj_addr', 'prj_approval_authority', 'prj_number', 'prj_sub_type', 'prj_type', 'procurement_method', 'release_time', 'service_fee', 'superintendent_office', 'superintendent_office_code', 'trade_platform']
2025-07-03 15:17:48.866 | INFO     | analyse_noappendix:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段15个, 删除多余字段3个, 补全缺失字段43个
2025-07-03 15:20:11.862 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 15:20:11.867 | INFO     | __main__:__init__:508 - 黑名单中有 1 个文档将被跳过
2025-07-03 15:20:11.867 | INFO     | __main__:__init__:510 - 今日新增黑名单文档: 1 个
2025-07-03 15:20:11.867 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 15:20:12.100 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3286 条结果
2025-07-03 15:20:12.102 | INFO     | __main__:process_one_record:869 - 排除 1 个黑名单文档
2025-07-03 15:20:12.102 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 15:20:12.483 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 116 条结果
2025-07-03 15:20:12.483 | INFO     | __main__:process_one_record:887 - ID: 1vIHzpcBGyYixO6vvd0y的附件链接为空
2025-07-03 15:20:12.483 | INFO     | __main__:process_one_record:888 - 公告标题: 宝山区吴淞中心医院保洁费采购的公开招标公告
2025-07-03 15:20:12.483 | INFO     | __main__:process_one_record:889 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202507/t20250702_24891749.htm
2025-07-03 15:20:12.483 | INFO     | __main__:process_one_record:890 - 公告类型: 001
2025-07-03 15:20:12.483 | INFO     | __main__:process_one_record:891 - 附件链接: []
2025-07-03 15:20:13.671 | INFO     | __main__:llm:420 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:20:13.671 | INFO     | __main__:llm:435 - enable_thinking=False
2025-07-03 15:20:13.989 | INFO     | __main__:llm:441 - extra_body={'enable_thinking': False}
2025-07-03 15:20:32.243 | INFO     | __main__:llm:445 - LLM API调用成功
2025-07-03 15:20:32.243 | INFO     | __main__:analyze_document:813 - [
    {
        "bid_name": "宝山区吴淞中心医院保洁费采购",
        "bid_number": "310113000250605115044-13248945",
        "bid_budget": 10816800.0,
        "fiscal_delegation_number": "1325-00001276",
        "prj_addr": "宝山区同泰北路101号",
        "prj_name": "宝山区吴淞中心医院保洁费采购",
        "prj_number": "310113000250605115044-13248945",
        "prj_type": "服务",
        "release_time": "2025-07-02 16:16:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "上海市宝山区吴淞中心医院",
        "bid_submission_deadline": "2025-07-25 14:00:00",
        "trade_platform": "上海市政府采购网",
        "procurement_method": "公开招标",
        "prj_sub_type": "其他",
        "province": "上海市",
        "city": "上海市",
        "county": null,
        "announcement_type": "001",
        "object_name": "保洁服务",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "1、项目地址：宝山区同泰北路101号。 2、物业概况：总建筑面积约65692平方米，主要有9幢建筑。其中高层3幢，分别为16层的门急诊楼、13层和8层的2幢住院楼。配置开放病床总数约658张。3.本项目主要采购医院的保洁服务服务。",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "项",
        "object_price": null,
        "object_total_price": 10816800.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "上海市宝山区政府采购中心",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 15:20:32.248 | INFO     | __main__:validate_and_normalize_fields:106 - 发现缺失字段，将补全为None: ['bid_doc_ext', 'bid_doc_link_key', 'bid_doc_link_out', 'bid_doc_name', 'contract_ext', 'contract_link_key', 'contract_link_out', 'contract_name']
2025-07-03 15:20:32.248 | INFO     | __main__:validate_and_normalize_fields:122 - 字段校验完成: 标准字段59个, 有效字段30个, 删除多余字段0个, 补全缺失字段8个
2025-07-03 15:20:32.248 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: 1vIHzpcBGyYixO6vvd0y_0
2025-07-03 15:20:32.258 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: 1vIHzpcBGyYixO6vvd0y_0
2025-07-03 15:20:32.258 | INFO     | __main__:process_one_record:950 - 成功插入文档 1vIHzpcBGyYixO6vvd0y_0
2025-07-03 15:24:15.294 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 15:24:15.299 | INFO     | __main__:__init__:1747 - 黑名单中有 1 个文档将被跳过
2025-07-03 15:24:15.299 | INFO     | __main__:__init__:1749 - 今日新增黑名单文档: 1 个
2025-07-03 15:24:15.299 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 15:24:15.707 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3287 条结果
2025-07-03 15:24:15.709 | INFO     | __main__:process_one_record:2155 - 排除 1 个黑名单文档
2025-07-03 15:24:15.709 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 15:24:16.375 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7346 条结果
2025-07-03 15:24:16.375 | INFO     | __main__:process_one_record:2167 - ID: -5-6n5cBfqDgtia4XGzu 开始分析公告主体
2025-07-03 15:24:16.375 | INFO     | __main__:process_one_record:2168 - 公告标题: 息烽县人民医院关于息烽县人民医院体外冲击波碎石机采购项目的公开招标公告
2025-07-03 15:24:16.376 | INFO     | __main__:process_one_record:2169 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250623_24824912.htm
2025-07-03 15:24:17.288 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:24:17.288 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:24:17.587 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:24:36.606 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:24:36.607 | INFO     | __main__:analyze_content:2102 - [
    {
        "bid_name": "息烽县人民医院体外冲击波碎石机采购项目",
        "bid_number": null,
        "bid_budget": 600000.0,
        "fiscal_delegation_number": "P52012220250005MN",
        "prj_addr": "贵州省息烽县永靖镇花园东路38号",
        "prj_name": "息烽县人民医院体外冲击波碎石机采购项目",
        "prj_number": "GZHBY-2025C-GK301",
        "prj_type": "货物",
        "release_time": "2025-06-23 11:02:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "息烽县人民医院",
        "bid_submission_deadline": "2025-07-14 09:30:00",
        "trade_platform": "全国公共资源交易平台（贵州省·贵阳市）网站",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "贵州省",
        "city": "贵阳市",
        "county": "息烽县",
        "announcement_type": "001",
        "object_name": "体外冲击波碎石机",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "采购体外冲击波碎石机一台。",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "台",
        "object_price": null,
        "object_total_price": 600000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "贵州海博源信息咨询服务有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 15:24:36.607 | INFO     | __main__:process_one_record:2240 - ID: -5-6n5cBfqDgtia4XGzu 发现附件，开始分析附件
2025-07-03 15:24:36.607 | INFO     | __main__:process_one_record:2241 - 附件列表: [{'url': 'https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip', 'text': '招标文件压缩包'}, {'url': 'https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf', 'text': '采购公告'}]
2025-07-03 15:24:36.608 | INFO     | __main__:process_one_record:2252 - 正在下载附件: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:36.608 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 1/3): https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:36.786 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 1/3): 403 Client Error: Forbidden for url: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:36.786 | INFO     | __main__:download_file:796 - 检测到403错误，尝试使用不同的请求策略...
2025-07-03 15:24:38.787 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 2/3): https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:39.221 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 2/3): 403 Client Error: Forbidden for url: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:39.221 | INFO     | __main__:download_file:796 - 检测到403错误，尝试使用不同的请求策略...
2025-07-03 15:24:41.222 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 3/3): https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:47.759 | INFO     | __main__:download_file:781 - 文件下载成功: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:47.760 | INFO     | __main__:process_one_record:2294 - 附件已保存到: downloads\招标文件压缩包.zip
2025-07-03 15:24:47.761 | INFO     | __main__:process_one_record:2304 - 发现压缩文件，正在解压: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip
2025-07-03 15:24:48.255 | INFO     | __main__:handle_compressed_file:1096 - 成功从 8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip 解压文件到 C:\Users\<USER>\AppData\Local\Temp\tmplh7d063b
2025-07-03 15:24:54.941 | DEBUG    | __main__:parse_pdf:837 - Successfully parsed PDF with pdfplumber, extracted 66504 characters
2025-07-03 15:24:55.451 | DEBUG    | __main__:parse_pdf:837 - Successfully parsed PDF with pdfplumber, extracted 6138 characters
2025-07-03 15:24:55.454 | INFO     | __main__:process_one_record:2360 - 正在分析文件: 附件0招标文件正文.pdf
2025-07-03 15:24:56.297 | DEBUG    | __main__:extract_preview_text:1214 - Successfully extracted PDF preview with pdfplumber, 10399 characters
2025-07-03 15:24:56.297 | INFO     | __main__:detect_file_type:1258 - 此文件是招标文件: 招标文件压缩包 (匹配关键词: ['招标文件', '采购文件', '采购公告'])
2025-07-03 15:24:56.297 | INFO     | __main__:process_one_record:2371 - 存储招标文件内容: 附件0招标文件正文.pdf
2025-07-03 15:24:56.298 | INFO     | __main__:process_one_record:2389 - 开始上传招标文件: 附件0招标文件正文.pdf
2025-07-03 15:24:56.298 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:24:56.298 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf, 大小: 789742 字节
2025-07-03 15:24:56.298 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=789742&objectName=招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:24:56.324 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197cf2ca047709597e2b71c1b339b29', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197cf2ca047709597e2b71c1b339b29/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-5-6n5cBfqDgtia4XGzu.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T072457Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=01d7b9cb997f626cd79f404814118045883246688ce32b3965c69d62ebee1fba'}, 'timestamp': 1751527497808, 'callId': None}
2025-07-03 15:24:56.324 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 789742 字节
2025-07-03 15:24:56.415 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 15:24:56.416 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197cf2ca047709597e2b71c1b339b29
2025-07-03 15:24:56.496 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 15:24:56.498 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:24:56.498 | INFO     | __main__:process_one_record:2402 - ✓ 招标文件上传成功: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf, upload_id: 0197cf2ca047709597e2b71c1b339b29
2025-07-03 15:24:57.383 | INFO     | __main__:llm:685 - 正在调用LLM API (尝试 1/3)...
2025-07-03 15:24:57.383 | INFO     | __main__:llm:700 - enable_thinking=False
2025-07-03 15:24:57.384 | INFO     | __main__:llm:706 - extra_body={'enable_thinking': False}
2025-07-03 15:26:34.212 | INFO     | __main__:llm:710 - LLM API调用成功
2025-07-03 15:26:34.212 | INFO     | __main__:analyze_content:2102 - [
    {
        "bid_name": null,
        "bid_number": "ZFCG20250618051",
        "bid_budget": 600000.0,
        "fiscal_delegation_number": null,
        "prj_addr": "贵州省息烽县永靖镇花园东路38号",
        "prj_name": "息烽县人民医院体外冲击波碎石机采购项目",
        "prj_number": "P52012220250005MN",
        "prj_type": "货物",
        "release_time": "2025-06-18 00:00:00",
        "prj_approval_authority": null,
        "superintendent_office": "息烽县财政局",
        "superintendent_office_code": null,
        "tenderee": "息烽县人民医院",
        "bid_submission_deadline": null,
        "trade_platform": "全国公共资源交易平台（贵州省.贵阳市）",
        "procurement_method": "公开招标",
        "prj_sub_type": "设备",
        "province": "贵州省",
        "city": "贵阳市",
        "county": "息烽县",
        "announcement_type": "001",
        "object_name": "体外冲击波碎石机",
        "object_brand": null,
        "object_model": null,
        "object_supplier": null,
        "object_produce_area": null,
        "object_conf": "技术参数要求：\n1. 冲击波发生器\n▲1.1 冲击波发生器：无透镜自聚焦电磁式冲击波源\n▲1.2 电磁式工作时的治疗电压：≤11KV\n1.3 放电能量：50J～100J；\n▲1.4 焦点至发生器平面距离（即治疗深度）：≥145mm；\n1.5 脉冲上升时间≤0.4μS、脉冲宽度≤0.4μS；\n1.6 聚焦点冲击波压缩声压峰值：27MPa~31MPa；\n▲1.7 聚焦体大小：径向范围±10mm；轴向靠近波源方向范围：从压力脉冲焦点往波源端口靠近的方向轴向范围为100mm，轴向远离波源方向范围：从压力脉冲焦点往波源端口平面远离的方向轴向范围为100mm；\n2. 冲击波治疗头\n2.1 采用悬挂式冲击波源（非上下定位臂）；\n2.2 反射体可沿中轴线做任意角度的旋转治疗；\n2.3 旋转治疗部可向左旋转90度；\n3. B超定位系统\n3.1 彩色多谱勒超声波诊断仪包括：数字化声束形成器、多倍波束合成、组织谐波成像、具有空间复合成像技术、斑点噪声抑制技术；\n3.2 一般测量（距离测量、椭圆及描迹测量面积周长、体积测量）、支持碎石；\n3.3 监视器: ≥21 英寸、医用专业彩色液晶显示器、主机探头接口数: ≥3 个；\n3.4 凸阵探头, 满足常规腹部、碎石、血管、泌尿检查；\n3.5 线阵探头, 满足常规浅表、外周血管、小器官、肌骨检查；\n3.6 二维灰阶模式：数字化全程动态聚焦，数字化可变孔径及动态变迹；\n3.7 动态范围: ≥220；\n4. 操作系统\n4.1 主机PLC智能化微电脑控制系统；\n4.2 开机系统自动检测，高压部分独立检测；\n4.3 可移动，悬挂的床边操作系统；\n4.4 触摸控制器\n4.5 涡流除气冷却系统，具有封闭式水加热、自动排气、自动冷却水温、恒温超温保护自动循环装置，保证碎石机24小时工作；\n5. 治疗床\n5.1 与主机分离的独立移动式多功能治疗床；\n5.2 治疗床全电动控制，能进行三维运动。治疗床位移距离：上下≥180mm，纵向≥100mm，横向≥100mm；\n6. 碎石机使用年限≥10年。",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "套",
        "object_price": 600000.0,
        "object_total_price": 600000.0,
        "object_maintenance_period": "3年",
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": null,
        "bidder_name": null,
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "贵州海博源信息咨询服务有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 15:26:34.213 | INFO     | __main__:process_one_record:2523 - 已找到并分析招标文件: 附件0招标文件正文.pdf
2025-07-03 15:26:34.213 | INFO     | __main__:process_one_record:2360 - 正在分析文件: 附件1采购公告.pdf
2025-07-03 15:26:34.465 | DEBUG    | __main__:extract_preview_text:1214 - Successfully extracted PDF preview with pdfplumber, 6138 characters
2025-07-03 15:26:34.465 | INFO     | __main__:detect_file_type:1258 - 此文件是招标文件: 招标文件压缩包 (匹配关键词: ['招标文件', '采购文件'])
2025-07-03 15:26:34.465 | INFO     | __main__:process_one_record:2389 - 开始上传招标文件: 附件1采购公告.pdf
2025-07-03 15:26:34.466 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:26:34.466 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf, 大小: 190090 字节
2025-07-03 15:26:34.466 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=190090&objectName=招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:26:34.617 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197cf2e1ff87f9ab67f3cbb7fafd1e2', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197cf2e1ff87f9ab67f3cbb7fafd1e2/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-5-6n5cBfqDgtia4XGzu.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T072636Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=0fa709fa902a0fc57e16e592791af661607515950d35e665f231abe8ad9b5414'}, 'timestamp': 1751527596029, 'callId': None}
2025-07-03 15:26:34.618 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 190090 字节
2025-07-03 15:26:34.667 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 15:26:34.667 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197cf2e1ff87f9ab67f3cbb7fafd1e2
2025-07-03 15:26:34.857 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 15:26:34.857 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:26:34.858 | INFO     | __main__:process_one_record:2402 - ✓ 招标文件上传成功: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf, upload_id: 0197cf2e1ff87f9ab67f3cbb7fafd1e2
2025-07-03 15:26:34.858 | INFO     | __main__:process_one_record:2434 - 已找到招标文件，跳过后续招标文件: 附件1采购公告.pdf
2025-07-03 15:26:34.858 | INFO     | __main__:process_one_record:2252 - 正在下载附件: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:34.858 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 1/3): https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:35.015 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 1/3): 403 Client Error: Forbidden for url: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:35.015 | INFO     | __main__:download_file:796 - 检测到403错误，尝试使用不同的请求策略...
2025-07-03 15:26:37.016 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 2/3): https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:37.167 | ERROR    | __main__:download_file:785 - 文件下载失败 (尝试 2/3): 403 Client Error: Forbidden for url: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:37.167 | INFO     | __main__:download_file:796 - 检测到403错误，尝试使用不同的请求策略...
2025-07-03 15:26:39.169 | INFO     | __main__:download_file:750 - 正在下载文件 (尝试 3/3): https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:39.911 | INFO     | __main__:download_file:781 - 文件下载成功: https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:39.913 | INFO     | __main__:process_one_record:2294 - 附件已保存到: downloads\采购公告.pdf
2025-07-03 15:26:39.914 | INFO     | __main__:process_one_record:2318 - 使用markitdown转换文件: 采购公告.pdf
2025-07-03 15:26:40.183 | DEBUG    | __main__:convert_to_markdown_with_markitdown:891 - markitdown成功转换.pdf文件，生成5909字符的Markdown
2025-07-03 15:26:40.185 | INFO     | __main__:process_one_record:2360 - 正在分析文件: 33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:40.522 | DEBUG    | __main__:extract_preview_text:1214 - Successfully extracted PDF preview with pdfplumber, 6138 characters
2025-07-03 15:26:40.522 | INFO     | __main__:detect_file_type:1258 - 此文件是招标文件: 采购公告 (匹配关键词: ['招标文件', '采购文件', '采购公告'])
2025-07-03 15:26:40.523 | INFO     | __main__:process_one_record:2389 - 开始上传招标文件: 33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:40.523 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:26:40.523 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf, 大小: 190090 字节
2025-07-03 15:26:40.523 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=190090&objectName=招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:26:40.663 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197cf2e37a073f784c76c7d49a0a5a4', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197cf2e37a073f784c76c7d49a0a5a4/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-5-6n5cBfqDgtia4XGzu.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T072642Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=f20b7e35a75f3916d7491c84e365ecce638cee87ebc12fc6f11eed991e2aee1e'}, 'timestamp': 1751527602086, 'callId': None}
2025-07-03 15:26:40.665 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 190090 字节
2025-07-03 15:26:40.722 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 15:26:40.723 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197cf2e37a073f784c76c7d49a0a5a4
2025-07-03 15:26:40.872 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 15:26:40.872 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf
2025-07-03 15:26:40.872 | INFO     | __main__:process_one_record:2402 - ✓ 招标文件上传成功: 招标文件_-5-6n5cBfqDgtia4XGzu.pdf, upload_id: 0197cf2e37a073f784c76c7d49a0a5a4
2025-07-03 15:26:40.873 | INFO     | __main__:process_one_record:2434 - 已找到招标文件，跳过后续招标文件: 33561c31-3646-48f6-9431-1537c724b268.pdf
2025-07-03 15:26:40.873 | INFO     | __main__:process_one_record:2623 - ================================================================================
2025-07-03 15:26:40.873 | INFO     | __main__:process_one_record:2624 - 开始融合分析结果
2025-07-03 15:26:40.873 | INFO     | __main__:process_one_record:2625 - ================================================================================
2025-07-03 15:26:40.873 | INFO     | __main__:process_one_record:2626 - 主体解析结果数量: 1
2025-07-03 15:26:40.874 | INFO     | __main__:process_one_record:2627 - 招标文件解析结果数量: 1
2025-07-03 15:26:40.874 | INFO     | __main__:process_one_record:2628 - 合同文件解析结果数量: 0
2025-07-03 15:26:40.874 | INFO     | __main__:process_one_record:2631 - 主体解析结果:
2025-07-03 15:26:40.874 | INFO     | __main__:process_one_record:2633 - 主体结果 1:
2025-07-03 15:26:40.874 | INFO     | __main__:process_one_record:2634 - {
  "bid_name": "息烽县人民医院体外冲击波碎石机采购项目",
  "bid_number": null,
  "bid_budget": 600000.0,
  "fiscal_delegation_number": "P52012220250005MN",
  "prj_addr": "贵州省息烽县永靖镇花园东路38号",
  "prj_name": "息烽县人民医院体外冲击波碎石机采购项目",
  "prj_number": "GZHBY-2025C-GK301",
  "prj_type": "货物",
  "release_time": "2025-06-23 11:02:00",
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "息烽县人民医院",
  "bid_submission_deadline": "2025-07-14 09:30:00",
  "trade_platform": "全国公共资源交易平台（贵州省·贵阳市）网站",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "贵州省",
  "city": "贵阳市",
  "county": "息烽县",
  "announcement_type": "001",
  "object_name": "体外冲击波碎石机",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "采购体外冲击波碎石机一台。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": null,
  "object_total_price": 600000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "贵州海博源信息咨询服务有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-5-6n5cBfqDgtia4XGzu",
  "source_title": "息烽县人民医院关于息烽县人民医院体外冲击波碎石机采购项目的公开招标公告",
  "source_create_time": "2025-06-24",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250623_24824912.htm",
  "source_appendix": [
    {
      "url": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip",
      "text": "招标文件压缩包"
    },
    {
      "url": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf",
      "text": "采购公告"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:24:36"
}
2025-07-03 15:26:40.875 | INFO     | __main__:process_one_record:2639 - 招标文件解析结果:
2025-07-03 15:26:40.875 | INFO     | __main__:process_one_record:2641 - 招标文件结果 1:
2025-07-03 15:26:40.875 | INFO     | __main__:process_one_record:2642 - {
  "bid_name": null,
  "bid_number": "ZFCG20250618051",
  "bid_budget": 600000.0,
  "fiscal_delegation_number": null,
  "prj_addr": "贵州省息烽县永靖镇花园东路38号",
  "prj_name": "息烽县人民医院体外冲击波碎石机采购项目",
  "prj_number": "P52012220250005MN",
  "prj_type": "货物",
  "release_time": "2025-06-18 00:00:00",
  "prj_approval_authority": null,
  "superintendent_office": "息烽县财政局",
  "superintendent_office_code": null,
  "tenderee": "息烽县人民医院",
  "bid_submission_deadline": null,
  "trade_platform": "全国公共资源交易平台（贵州省.贵阳市）",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "贵州省",
  "city": "贵阳市",
  "county": "息烽县",
  "announcement_type": "001",
  "object_name": "体外冲击波碎石机",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "技术参数要求：\n1. 冲击波发生器\n▲1.1 冲击波发生器：无透镜自聚焦电磁式冲击波源\n▲1.2 电磁式工作时的治疗电压：≤11KV\n1.3 放电能量：50J～100J；\n▲1.4 焦点至发生器平面距离（即治疗深度）：≥145mm；\n1.5 脉冲上升时间≤0.4μS、脉冲宽度≤0.4μS；\n1.6 聚焦点冲击波压缩声压峰值：27MPa~31MPa；\n▲1.7 聚焦体大小：径向范围±10mm；轴向靠近波源方向范围：从压力脉冲焦点往波源端口靠近的方向轴向范围为100mm，轴向远离波源方向范围：从压力脉冲焦点往波源端口平面远离的方向轴向范围为100mm；\n2. 冲击波治疗头\n2.1 采用悬挂式冲击波源（非上下定位臂）；\n2.2 反射体可沿中轴线做任意角度的旋转治疗；\n2.3 旋转治疗部可向左旋转90度；\n3. B超定位系统\n3.1 彩色多谱勒超声波诊断仪包括：数字化声束形成器、多倍波束合成、组织谐波成像、具有空间复合成像技术、斑点噪声抑制技术；\n3.2 一般测量（距离测量、椭圆及描迹测量面积周长、体积测量）、支持碎石；\n3.3 监视器: ≥21 英寸、医用专业彩色液晶显示器、主机探头接口数: ≥3 个；\n3.4 凸阵探头, 满足常规腹部、碎石、血管、泌尿检查；\n3.5 线阵探头, 满足常规浅表、外周血管、小器官、肌骨检查；\n3.6 二维灰阶模式：数字化全程动态聚焦，数字化可变孔径及动态变迹；\n3.7 动态范围: ≥220；\n4. 操作系统\n4.1 主机PLC智能化微电脑控制系统；\n4.2 开机系统自动检测，高压部分独立检测；\n4.3 可移动，悬挂的床边操作系统；\n4.4 触摸控制器\n4.5 涡流除气冷却系统，具有封闭式水加热、自动排气、自动冷却水温、恒温超温保护自动循环装置，保证碎石机24小时工作；\n5. 治疗床\n5.1 与主机分离的独立移动式多功能治疗床；\n5.2 治疗床全电动控制，能进行三维运动。治疗床位移距离：上下≥180mm，纵向≥100mm，横向≥100mm；\n6. 碎石机使用年限≥10年。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "套",
  "object_price": 600000.0,
  "object_total_price": 600000.0,
  "object_maintenance_period": "3年",
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "贵州海博源信息咨询服务有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-5-6n5cBfqDgtia4XGzu",
  "source_title": "息烽县人民医院关于息烽县人民医院体外冲击波碎石机采购项目的公开招标公告",
  "source_create_time": "2025-06-24",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250623_24824912.htm",
  "source_appendix": [
    {
      "url": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip",
      "text": "招标文件压缩包"
    },
    {
      "url": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf",
      "text": "采购公告"
    }
  ],
  "insert_time": "2025-07-03 15:26:34",
  "bid_doc_name": "招标文件压缩包.zip",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip",
  "bid_doc_link_key": "0197cf2ca047709597e2b71c1b339b29",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null
}
2025-07-03 15:26:40.878 | INFO     | __main__:process_one_record:2658 - 使用智能融合分析
2025-07-03 15:26:40.878 | INFO     | __main__:intelligent_merge_analysis:1561 - 开始基于object_name的匹配融合分析
2025-07-03 15:26:40.879 | INFO     | __main__:merge_analysis_by_object_name:1519 - 主体标的物 '体外冲击波碎石机' 匹配结果:
2025-07-03 15:26:40.880 | INFO     | __main__:merge_analysis_by_object_name:1520 -   - 招标文件匹配: 是
2025-07-03 15:26:40.881 | INFO     | __main__:merge_analysis_by_object_name:1521 -   - 合同文件匹配: 否
2025-07-03 15:26:40.881 | INFO     | __main__:intelligent_merge_analysis:1569 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 15:26:40.882 | INFO     | __main__:intelligent_merge_analysis:1578 - 开始对融合结果进行智能补充
2025-07-03 15:26:40.883 | INFO     | __main__:intelligent_merge_analysis:1582 - 处理第1个融合结果的智能补充
2025-07-03 15:26:40.884 | INFO     | __main__:intelligent_merge_analysis:1595 - 发现空缺字段: ['prj_approval_authority', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price_source', 'object_quality', 'bidder_price', 'bidder_name', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:26:40.885 | INFO     | __main__:intelligent_merge_analysis:1640 - 从招标文件解析结果中获取字段: ['prj_approval_authority', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 15:26:40.885 | INFO     | __main__:intelligent_merge_analysis:1656 - 招标文件已解析但缺少字段 ['prj_approval_authority', 'superintendent_office_code', 'object_brand', 'object_model', 'object_supplier', 'object_produce_area', 'object_oem', 'object_price_source', 'object_quality', 'service_fee', 'bid_cancelled_flag', 'bid_cancelled_reason']，跳过LLM重新提取
2025-07-03 15:26:40.886 | INFO     | __main__:intelligent_merge_analysis:1659 - 建议：检查字段定义或文档内容是否包含这些信息
2025-07-03 15:26:40.886 | INFO     | __main__:intelligent_merge_analysis:1693 - 第1个融合结果智能补充完成
2025-07-03 15:26:40.887 | INFO     | __main__:intelligent_merge_analysis:1695 - 智能补充完成，返回1个增强结果
2025-07-03 15:26:40.887 | INFO     | __main__:process_one_record:2673 - ================================================================================
2025-07-03 15:26:40.888 | INFO     | __main__:process_one_record:2674 - 最终融合后的JSON结果列表:
2025-07-03 15:26:40.889 | INFO     | __main__:process_one_record:2675 - ================================================================================
2025-07-03 15:26:40.889 | INFO     | __main__:process_one_record:2677 - 融合结果 1:
2025-07-03 15:26:40.890 | INFO     | __main__:process_one_record:2678 - {
  "bid_name": "息烽县人民医院体外冲击波碎石机采购项目",
  "bid_number": "ZFCG20250618051",
  "bid_budget": 600000.0,
  "fiscal_delegation_number": "P52012220250005MN",
  "prj_addr": "贵州省息烽县永靖镇花园东路38号",
  "prj_name": "息烽县人民医院体外冲击波碎石机采购项目",
  "prj_number": "GZHBY-2025C-GK301",
  "prj_type": "货物",
  "release_time": "2025-06-23 11:02:00",
  "prj_approval_authority": null,
  "superintendent_office": "息烽县财政局",
  "superintendent_office_code": null,
  "tenderee": "息烽县人民医院",
  "bid_submission_deadline": "2025-07-14 09:30:00",
  "trade_platform": "全国公共资源交易平台（贵州省·贵阳市）网站",
  "procurement_method": "公开招标",
  "prj_sub_type": "设备",
  "province": "贵州省",
  "city": "贵阳市",
  "county": "息烽县",
  "announcement_type": "001",
  "object_name": "体外冲击波碎石机",
  "object_brand": null,
  "object_model": null,
  "object_supplier": null,
  "object_produce_area": null,
  "object_conf": "采购体外冲击波碎石机一台。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "台",
  "object_price": 600000.0,
  "object_total_price": 600000.0,
  "object_maintenance_period": "3年",
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": null,
  "bidder_name": null,
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "贵州海博源信息咨询服务有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-5-6n5cBfqDgtia4XGzu",
  "source_title": "息烽县人民医院关于息烽县人民医院体外冲击波碎石机采购项目的公开招标公告",
  "source_create_time": "2025-06-24",
  "source_category": "001",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/gkzb/202506/t20250623_24824912.htm",
  "source_appendix": [
    {
      "url": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip",
      "text": "招标文件压缩包"
    },
    {
      "url": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1024FPA/open/33561c31-3646-48f6-9431-1537c724b268.pdf",
      "text": "采购公告"
    }
  ],
  "bid_doc_name": "招标文件压缩包.zip",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "https://gz-gov-open-doc.oss-cn-gz-ysgzlt-d01-a.ltops.gzdata.com.cn/1023FP/open/8b47f8cf-a9ba-47d6-af4e-e972dbc0daba.zip",
  "bid_doc_link_key": "0197cf2ca047709597e2b71c1b339b29",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 15:24:36"
}
2025-07-03 15:26:40.892 | INFO     | __main__:process_one_record:2679 - ------------------------------------------------------------
2025-07-03 15:26:40.892 | INFO     | __main__:process_one_record:2680 - ================================================================================
2025-07-03 15:26:40.893 | INFO     | __main__:validate_and_normalize_fields:387 - 字段校验完成: 标准字段59个, 有效字段38个, 删除多余字段0个, 补全缺失字段0个
2025-07-03 15:26:40.893 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -5-6n5cBfqDgtia4XGzu_0
2025-07-03 15:26:40.971 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -5-6n5cBfqDgtia4XGzu_0
2025-07-03 15:26:40.972 | INFO     | __main__:process_one_record:2695 - 成功插入融合文档 -5-6n5cBfqDgtia4XGzu_0
2025-07-03 15:47:47.776 | INFO     | analyse_appendix:merge_analysis:1492 - 招标文件覆盖字段: object_name, object_brand, object_model, object_conf
2025-07-03 15:47:47.776 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_name: '主体解析的设备名称' → '招标文件中的设备名称'
2025-07-03 15:47:47.776 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_brand: '西门子' → '西门子'
2025-07-03 15:47:47.776 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_model: '主体型号' → '招标文件型号'
2025-07-03 15:47:47.776 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_conf: '详细技术配置参数' → '详细技术配置参数'
2025-07-03 15:47:47.777 | INFO     | analyse_appendix:merge_analysis:1510 - 合同文件覆盖字段: bidder_price, bidder_name, bidder_contact_person
2025-07-03 15:47:47.779 | DEBUG    | analyse_appendix:merge_analysis:1514 -   bidder_price: '2500000.0' → '2500000.0'
2025-07-03 15:47:47.780 | DEBUG    | analyse_appendix:merge_analysis:1514 -   bidder_name: '主体中标单位' → '合同文件中标单位'
2025-07-03 15:47:47.780 | DEBUG    | analyse_appendix:merge_analysis:1514 -   bidder_contact_person: '张三' → '张三'
2025-07-03 15:47:47.782 | INFO     | analyse_appendix:merge_analysis:1492 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 15:47:47.782 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_name: '主体设备名称' → '招标文件设备名称'
2025-07-03 15:47:47.784 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 15:47:47.784 | DEBUG    | analyse_appendix:merge_analysis:1496 -   object_conf: '技术配置参数' → '技术配置参数'
2025-07-03 15:47:47.784 | INFO     | analyse_appendix:merge_analysis:1510 - 合同文件覆盖字段: bidder_price, bidder_name, bidder_contact_person
2025-07-03 15:47:47.785 | DEBUG    | analyse_appendix:merge_analysis:1514 -   bidder_price: '3000000.0' → '3000000.0'
2025-07-03 15:47:47.785 | DEBUG    | analyse_appendix:merge_analysis:1514 -   bidder_name: '主体中标单位' → '合同文件中标单位'
2025-07-03 15:47:47.786 | DEBUG    | analyse_appendix:merge_analysis:1514 -   bidder_contact_person: '李四' → '李四'
2025-07-03 15:50:25.826 | INFO     | analyse_appendix:merge_analysis:1502 - 招标文件覆盖字段: object_name, object_brand, object_model, object_conf
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_name: '主体解析的设备名称' → '招标文件中的设备名称'
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_brand: '西门子' → '西门子'
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_model: '主体型号' → '招标文件型号'
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_conf: '详细技术配置参数' → '详细技术配置参数'
2025-07-03 15:50:25.826 | INFO     | analyse_appendix:merge_analysis:1520 - 合同文件覆盖字段: bidder_price, bidder_name, bidder_contact_person
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_price: '2500000.0' → '2500000.0'
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_name: '主体中标单位' → '合同文件中标单位'
2025-07-03 15:50:25.826 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_contact_person: '张三' → '张三'
2025-07-03 15:50:25.835 | INFO     | analyse_appendix:merge_analysis:1502 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 15:50:25.835 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_name: '主体设备名称' → '招标文件设备名称'
2025-07-03 15:50:25.835 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 15:50:25.835 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_conf: '技术配置参数' → '技术配置参数'
2025-07-03 15:50:25.836 | INFO     | analyse_appendix:merge_analysis:1520 - 合同文件覆盖字段: bidder_price, bidder_name, bidder_contact_person
2025-07-03 15:50:25.836 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_price: '3000000.0' → '3000000.0'
2025-07-03 15:50:25.836 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_name: '主体中标单位' → '合同文件中标单位'
2025-07-03 15:50:25.836 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_contact_person: '李四' → '李四'
2025-07-03 15:51:26.372 | INFO     | analyse_appendix:merge_analysis:1502 - 招标文件覆盖字段: object_name, object_brand, object_model, object_conf
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_name: '主体解析的设备名称' → '招标文件中的设备名称'
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_brand: '西门子' → '西门子'
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_model: '主体型号' → '招标文件型号'
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_conf: '详细技术配置参数' → '详细技术配置参数'
2025-07-03 15:51:26.372 | INFO     | analyse_appendix:merge_analysis:1520 - 合同文件覆盖字段: bidder_price, bidder_name, bidder_contact_person
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_price: '2500000.0' → '2500000.0'
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_name: '主体中标单位' → '合同文件中标单位'
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_contact_person: '张三' → '张三'
2025-07-03 15:51:26.372 | INFO     | analyse_appendix:merge_analysis:1502 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 15:51:26.372 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_name: '主体设备名称' → '招标文件设备名称'
2025-07-03 15:51:26.377 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 15:51:26.377 | DEBUG    | analyse_appendix:merge_analysis:1506 -   object_conf: '技术配置参数' → '技术配置参数'
2025-07-03 15:51:26.377 | INFO     | analyse_appendix:merge_analysis:1520 - 合同文件覆盖字段: bidder_price, bidder_name, bidder_contact_person
2025-07-03 15:51:26.378 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_price: '3000000.0' → '3000000.0'
2025-07-03 15:51:26.378 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_name: '主体中标单位' → '合同文件中标单位'
2025-07-03 15:51:26.378 | DEBUG    | analyse_appendix:merge_analysis:1524 -   bidder_contact_person: '李四' → '李四'
2025-07-03 16:25:31.996 | INFO     | analyse_appendix:merge_analysis:1605 - 招标文件object_name匹配验证: object_name完全匹配 (相似度: 1.0)
2025-07-03 16:25:31.996 | INFO     | analyse_appendix:merge_analysis:1618 - 招标文件覆盖字段: object_name, object_brand, object_model
2025-07-03 16:25:31.996 | DEBUG    | analyse_appendix:merge_analysis:1622 -   object_name: '招标文件设备名称' → '招标文件设备名称'
2025-07-03 16:25:31.998 | DEBUG    | analyse_appendix:merge_analysis:1622 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 16:25:31.998 | DEBUG    | analyse_appendix:merge_analysis:1622 -   object_model: '招标文件型号' → '招标文件型号'
2025-07-03 16:25:32.001 | INFO     | analyse_appendix:merge_analysis:1605 - 招标文件object_name匹配验证: object_name完全匹配 (相似度: 1.0)
2025-07-03 16:25:32.001 | INFO     | analyse_appendix:merge_analysis:1618 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 16:25:32.002 | DEBUG    | analyse_appendix:merge_analysis:1622 -   object_name: 'CT扫描仪' → 'CT扫描仪'
2025-07-03 16:25:32.002 | DEBUG    | analyse_appendix:merge_analysis:1622 -   object_brand: '主体品牌' → '西门子'
2025-07-03 16:25:32.002 | DEBUG    | analyse_appendix:merge_analysis:1622 -   object_conf: '详细技术参数' → '详细技术参数'
2025-07-03 16:25:32.004 | INFO     | analyse_appendix:merge_analysis:1605 - 招标文件object_name匹配验证: object_name相似度0.638<0.8，匹配失败 (相似度: 0.638)
2025-07-03 16:25:32.005 | WARNING  | analyse_appendix:merge_analysis:1625 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='CT扫描设备' (相似度: 0.638)
2025-07-03 16:25:32.006 | INFO     | analyse_appendix:merge_analysis:1605 - 招标文件object_name匹配验证: object_name相似度0.0<0.8，匹配失败 (相似度: 0.0)
2025-07-03 16:25:32.006 | WARNING  | analyse_appendix:merge_analysis:1625 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='MRI设备' (相似度: 0.0)
2025-07-03 16:39:59.111 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name完全匹配 (相似度: 1.0)
2025-07-03 16:39:59.112 | INFO     | analyse_appendix:merge_analysis:1663 - 招标文件覆盖字段: object_name, object_brand, object_model
2025-07-03 16:39:59.112 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_name: '招标文件设备名称' → '招标文件设备名称'
2025-07-03 16:39:59.112 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 16:39:59.114 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_model: '招标文件型号' → '招标文件型号'
2025-07-03 16:39:59.114 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name完全匹配 (相似度: 1.0)
2025-07-03 16:39:59.116 | INFO     | analyse_appendix:merge_analysis:1663 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 16:39:59.116 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_name: 'CT扫描仪' → 'CT扫描仪'
2025-07-03 16:39:59.116 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_brand: '主体品牌' → '西门子'
2025-07-03 16:39:59.116 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_conf: '详细技术参数' → '详细技术参数'
2025-07-03 16:39:59.120 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name相似度0.514<0.6，匹配失败 (相似度: 0.514)
2025-07-03 16:39:59.120 | WARNING  | analyse_appendix:merge_analysis:1670 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='CT扫描设备' (相似度: 0.514)
2025-07-03 16:39:59.121 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name相似度0.0<0.6，匹配失败 (相似度: 0.0)
2025-07-03 16:39:59.122 | WARNING  | analyse_appendix:merge_analysis:1670 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='MRI设备' (相似度: 0.0)
2025-07-03 16:40:16.461 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name完全匹配 (相似度: 1.0)
2025-07-03 16:40:16.461 | INFO     | analyse_appendix:merge_analysis:1663 - 招标文件覆盖字段: object_name, object_brand, object_model
2025-07-03 16:40:16.462 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_name: '招标文件设备名称' → '招标文件设备名称'
2025-07-03 16:40:16.462 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 16:40:16.462 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_model: '招标文件型号' → '招标文件型号'
2025-07-03 16:40:16.465 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name完全匹配 (相似度: 1.0)
2025-07-03 16:40:16.465 | INFO     | analyse_appendix:merge_analysis:1663 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 16:40:16.465 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_name: 'CT扫描仪' → 'CT扫描仪'
2025-07-03 16:40:16.465 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_brand: '主体品牌' → '西门子'
2025-07-03 16:40:16.465 | DEBUG    | analyse_appendix:merge_analysis:1667 -   object_conf: '详细技术参数' → '详细技术参数'
2025-07-03 16:40:16.465 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name相似度0.514<0.6，匹配失败 (相似度: 0.514)
2025-07-03 16:40:16.465 | WARNING  | analyse_appendix:merge_analysis:1670 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='CT扫描设备' (相似度: 0.514)
2025-07-03 16:40:16.465 | INFO     | analyse_appendix:merge_analysis:1650 - 招标文件object_name匹配验证: object_name相似度0.0<0.6，匹配失败 (相似度: 0.0)
2025-07-03 16:40:16.465 | WARNING  | analyse_appendix:merge_analysis:1670 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='MRI设备' (相似度: 0.0)
2025-07-03 16:41:46.220 | DEBUG    | analyse_appendix:merge_analysis:1621 - 招标文件可用于基础补充: 主体文件object_name为空，直接使用招标文件数据
2025-07-03 16:41:46.221 | INFO     | analyse_appendix:merge_analysis:1661 - 招标文件已通过匹配验证，执行字段覆盖
2025-07-03 16:41:46.221 | INFO     | analyse_appendix:merge_analysis:1670 - 招标文件覆盖字段: object_name, object_brand, object_model
2025-07-03 16:41:46.221 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_name: '招标文件设备名称' → '招标文件设备名称'
2025-07-03 16:41:46.223 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 16:41:46.223 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_model: '招标文件型号' → '招标文件型号'
2025-07-03 16:41:46.224 | DEBUG    | analyse_appendix:merge_analysis:1621 - 招标文件可用于基础补充: object_name完全匹配
2025-07-03 16:41:46.224 | INFO     | analyse_appendix:merge_analysis:1661 - 招标文件已通过匹配验证，执行字段覆盖
2025-07-03 16:41:46.224 | INFO     | analyse_appendix:merge_analysis:1670 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 16:41:46.224 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_name: 'CT扫描仪' → 'CT扫描仪'
2025-07-03 16:41:46.224 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_brand: '主体品牌' → '西门子'
2025-07-03 16:41:46.224 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_conf: '详细技术参数' → '详细技术参数'
2025-07-03 16:41:46.228 | DEBUG    | analyse_appendix:merge_analysis:1623 - 招标文件不可用于基础补充: object_name相似度0.514<0.6，匹配失败
2025-07-03 16:41:46.228 | WARNING  | analyse_appendix:merge_analysis:1681 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='CT扫描设备' (相似度: 0.514)
2025-07-03 16:41:46.230 | DEBUG    | analyse_appendix:merge_analysis:1623 - 招标文件不可用于基础补充: object_name相似度0.0<0.6，匹配失败
2025-07-03 16:41:46.230 | WARNING  | analyse_appendix:merge_analysis:1681 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='MRI设备' (相似度: 0.0)
2025-07-03 16:42:54.460 | DEBUG    | analyse_appendix:merge_analysis:1621 - 招标文件可用于基础补充: 主体文件object_name为空，直接使用招标文件数据
2025-07-03 16:42:54.460 | INFO     | analyse_appendix:merge_analysis:1661 - 招标文件已通过匹配验证，执行字段覆盖
2025-07-03 16:42:54.466 | INFO     | analyse_appendix:merge_analysis:1670 - 招标文件覆盖字段: object_name, object_brand, object_model
2025-07-03 16:42:54.466 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_name: '招标文件设备名称' → '招标文件设备名称'
2025-07-03 16:42:54.466 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_brand: '主体品牌' → '招标文件品牌'
2025-07-03 16:42:54.466 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_model: '招标文件型号' → '招标文件型号'
2025-07-03 16:42:54.467 | DEBUG    | analyse_appendix:merge_analysis:1621 - 招标文件可用于基础补充: object_name完全匹配
2025-07-03 16:42:54.467 | INFO     | analyse_appendix:merge_analysis:1661 - 招标文件已通过匹配验证，执行字段覆盖
2025-07-03 16:42:54.467 | INFO     | analyse_appendix:merge_analysis:1670 - 招标文件覆盖字段: object_name, object_brand, object_conf
2025-07-03 16:42:54.468 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_name: 'CT扫描仪' → 'CT扫描仪'
2025-07-03 16:42:54.468 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_brand: '主体品牌' → '西门子'
2025-07-03 16:42:54.468 | DEBUG    | analyse_appendix:merge_analysis:1674 -   object_conf: '详细技术参数' → '详细技术参数'
2025-07-03 16:42:54.469 | DEBUG    | analyse_appendix:merge_analysis:1623 - 招标文件不可用于基础补充: object_name相似度0.514<0.6，匹配失败
2025-07-03 16:42:54.470 | WARNING  | analyse_appendix:merge_analysis:1681 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='CT扫描设备' (相似度: 0.514)
2025-07-03 16:42:54.470 | DEBUG    | analyse_appendix:merge_analysis:1623 - 招标文件不可用于基础补充: object_name相似度0.0<0.6，匹配失败
2025-07-03 16:42:54.470 | WARNING  | analyse_appendix:merge_analysis:1681 - 招标文件object_name不匹配，跳过字段覆盖: 主体='CT扫描仪' vs 招标='MRI设备' (相似度: 0.0)
2025-07-03 16:45:00.041 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 16:45:00.041 | INFO     | __main__:__init__:2019 - 黑名单中有 1 个文档将被跳过
2025-07-03 16:45:00.046 | INFO     | __main__:__init__:2021 - 今日新增黑名单文档: 1 个
2025-07-03 16:45:00.046 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 16:45:00.406 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3288 条结果
2025-07-03 16:45:00.406 | INFO     | __main__:process_one_record:2427 - 排除 1 个黑名单文档
2025-07-03 16:45:00.406 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 16:45:01.117 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7345 条结果
2025-07-03 16:45:01.122 | INFO     | __main__:process_one_record:2439 - ID: -55FkJcBfqDgtia4w8Gx 开始分析公告主体
2025-07-03 16:45:01.122 | INFO     | __main__:process_one_record:2440 - 公告标题: 中南大学湘雅医院检验科第二批试剂入围遴选项目（标段二）成交公告
2025-07-03 16:45:01.122 | INFO     | __main__:process_one_record:2441 - 公告链接: http://www.ccgp.gov.cn/cggg/zygg/cjgg/202506/t20250620_24817327.htm
2025-07-03 16:45:02.140 | INFO     | __main__:llm:885 - 正在调用LLM API (尝试 1/3)...
2025-07-03 16:45:02.140 | INFO     | __main__:llm:900 - enable_thinking=False
2025-07-03 16:45:02.574 | INFO     | __main__:llm:906 - extra_body={'enable_thinking': False}
2025-07-03 16:51:04.279 | ERROR    | __main__:llm:915 - LLM API调用失败 (尝试 1/3): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 16:51:04.279 | INFO     | __main__:llm:929 - 检测到网络错误，等待 10 秒后重试...
2025-07-03 16:51:15.536 | INFO     | __main__:llm:885 - 正在调用LLM API (尝试 2/3)...
2025-07-03 16:51:15.536 | INFO     | __main__:llm:900 - enable_thinking=False
2025-07-03 16:51:15.536 | INFO     | __main__:llm:906 - extra_body={'enable_thinking': False}
2025-07-03 16:57:17.169 | ERROR    | __main__:llm:915 - LLM API调用失败 (尝试 2/3): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 16:57:17.170 | INFO     | __main__:llm:929 - 检测到网络错误，等待 20 秒后重试...
2025-07-03 16:57:38.176 | INFO     | __main__:llm:885 - 正在调用LLM API (尝试 3/3)...
2025-07-03 16:57:38.176 | INFO     | __main__:llm:900 - enable_thinking=False
2025-07-03 16:57:38.176 | INFO     | __main__:llm:906 - extra_body={'enable_thinking': False}
2025-07-03 17:03:39.802 | ERROR    | __main__:llm:915 - LLM API调用失败 (尝试 3/3): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 17:03:39.802 | ERROR    | __main__:process_one_record:2975 - 处理过程中发生异常: LLM API调用失败 (已重试3次): <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body>

<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by Tengine<hr><center>nginx</center>

</body>

</html>
2025-07-03 17:03:39.802 | WARNING  | __main__:process_one_record:2980 - LLM调用三次失败，将文档 -55FkJcBfqDgtia4w8Gx 添加到黑名单
2025-07-03 17:03:39.807 | INFO     | __main__:process_one_record:2994 - ✓ 文档 -55FkJcBfqDgtia4w8Gx 已添加到黑名单，下次运行将跳过
2025-07-03 17:03:39.807 | WARNING  | __main__:process_one_record:2998 - 尝试保存已处理的主体解析结果...
2025-07-03 17:03:39.807 | WARNING  | __main__:process_one_record:3019 - 没有主体解析结果可保存
2025-07-03 17:19:55.064 | INFO     | es_deal:init_es_client:52 - ES客户端初始化成功，连接到: http://172.18.7.2:9200
2025-07-03 17:19:55.070 | INFO     | __main__:__init__:2019 - 黑名单中有 2 个文档将被跳过
2025-07-03 17:19:55.070 | INFO     | __main__:__init__:2021 - 今日新增黑名单文档: 2 个
2025-07-03 17:19:55.070 | INFO     | es_deal:search_documents:598 - 开始在索引 markersweb_attachment_analysis_alias 中搜索文档
2025-07-03 17:19:55.762 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 markersweb_attachment_analysis_alias 中找到 3288 条结果
2025-07-03 17:19:55.763 | INFO     | __main__:process_one_record:2427 - 排除 2 个黑名单文档
2025-07-03 17:19:55.763 | INFO     | es_deal:search_documents:598 - 开始在索引 chn_ylcg 中搜索文档
2025-07-03 17:19:56.859 | INFO     | es_deal:search_documents:600 - 搜索完成，在索引 chn_ylcg 中找到 7344 条结果
2025-07-03 17:19:56.859 | INFO     | __main__:process_one_record:2439 - ID: -55YlZcBfqDgtia4ifmt 开始分析公告主体
2025-07-03 17:19:56.859 | INFO     | __main__:process_one_record:2440 - 公告标题: 浙江开平企业管理咨询有限公司关于义乌市中心医院4K关节镜摄像、刨削手术系统采购中标(成交)结果公告
2025-07-03 17:19:56.859 | INFO     | __main__:process_one_record:2441 - 公告链接: http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250621_24822271.htm
2025-07-03 17:19:58.140 | INFO     | __main__:llm:885 - 正在调用LLM API (尝试 1/3)...
2025-07-03 17:19:58.140 | INFO     | __main__:llm:900 - enable_thinking=False
2025-07-03 17:19:58.561 | INFO     | __main__:llm:906 - extra_body={'enable_thinking': False}
2025-07-03 17:20:15.036 | INFO     | __main__:llm:910 - LLM API调用成功
2025-07-03 17:20:15.036 | INFO     | __main__:analyze_content:2374 - [
    {
        "bid_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
        "bid_number": "ZJKP2025YW097G",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "义乌市南门街519号",
        "prj_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
        "prj_number": "ZJKP2025YW097G",
        "prj_type": "货物",
        "release_time": "2025-06-21 15:08:00",
        "prj_approval_authority": null,
        "superintendent_office": "义乌市财政局",
        "superintendent_office_code": null,
        "tenderee": "义乌市中心医院(义乌市中心医院医共体)",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "浙江省",
        "city": "金华市",
        "county": "义乌市",
        "announcement_type": "004",
        "object_name": "4K关节镜摄像、刨削手术系统",
        "object_brand": "AR",
        "object_model": "200-0023等",
        "object_supplier": "金华市瑞信医疗器械有限公司",
        "object_produce_area": null,
        "object_conf": null,
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "套",
        "object_price": 1700000.0,
        "object_total_price": 1700000.0,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 1700000.0,
        "bidder_name": "金华市瑞信医疗器械有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "浙江开平企业管理咨询有限公司",
        "service_fee": 15890.0,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 17:20:15.036 | INFO     | __main__:process_one_record:2512 - ID: -55YlZcBfqDgtia4ifmt 发现附件，开始分析附件
2025-07-03 17:20:15.036 | INFO     | __main__:process_one_record:2513 - 附件列表: [{'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf', 'text': '评审专家抽取规则'}, {'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf', 'text': '标项'}, {'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf', 'text': '标项'}, {'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf', 'text': '标项'}, {'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf', 'text': '标项'}, {'url': 'https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf', 'text': '标项'}]
2025-07-03 17:20:15.041 | INFO     | __main__:process_one_record:2524 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf
2025-07-03 17:20:15.041 | INFO     | __main__:download_file:950 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf
2025-07-03 17:20:15.339 | INFO     | __main__:download_file:981 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf
2025-07-03 17:20:15.339 | INFO     | __main__:process_one_record:2566 - 附件已保存到: downloads\评审专家抽取规则.pdf
2025-07-03 17:20:15.339 | INFO     | __main__:process_one_record:2590 - 使用markitdown转换文件: 评审专家抽取规则.pdf
2025-07-03 17:20:15.601 | DEBUG    | __main__:convert_to_markdown_with_markitdown:1091 - markitdown成功转换.pdf文件，生成1263字符的Markdown
2025-07-03 17:20:15.601 | INFO     | __main__:process_one_record:2632 - 正在分析文件: 4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf
2025-07-03 17:20:15.711 | DEBUG    | __main__:extract_preview_text:1414 - Successfully extracted PDF preview with pdfplumber, 1346 characters
2025-07-03 17:20:15.711 | INFO     | __main__:detect_file_type:1508 - 此文件是其他文件: 评审专家抽取规则
2025-07-03 17:20:15.711 | INFO     | __main__:process_one_record:2716 - 跳过非招标/合同文件: 4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf
2025-07-03 17:20:15.711 | INFO     | __main__:process_one_record:2524 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf
2025-07-03 17:20:15.711 | INFO     | __main__:download_file:950 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf
2025-07-03 17:20:15.927 | INFO     | __main__:download_file:981 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf
2025-07-03 17:20:15.927 | INFO     | __main__:process_one_record:2566 - 附件已保存到: downloads\标项.pdf
2025-07-03 17:20:15.927 | INFO     | __main__:process_one_record:2590 - 使用markitdown转换文件: 标项.pdf
2025-07-03 17:20:16.019 | DEBUG    | __main__:convert_to_markdown_with_markitdown:1091 - markitdown成功转换.pdf文件，生成294字符的Markdown
2025-07-03 17:20:16.022 | INFO     | __main__:process_one_record:2632 - 正在分析文件: 1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf
2025-07-03 17:20:16.073 | DEBUG    | __main__:extract_preview_text:1414 - Successfully extracted PDF preview with pdfplumber, 270 characters
2025-07-03 17:20:16.073 | INFO     | __main__:detect_file_type:1508 - 此文件是其他文件: 标项
2025-07-03 17:20:16.078 | INFO     | __main__:process_one_record:2716 - 跳过非招标/合同文件: 1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf
2025-07-03 17:20:16.078 | INFO     | __main__:process_one_record:2524 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf
2025-07-03 17:20:16.078 | INFO     | __main__:download_file:950 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf
2025-07-03 17:20:16.263 | INFO     | __main__:download_file:981 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf
2025-07-03 17:20:16.263 | INFO     | __main__:process_one_record:2566 - 附件已保存到: downloads\标项.pdf
2025-07-03 17:20:16.263 | INFO     | __main__:process_one_record:2590 - 使用markitdown转换文件: 标项.pdf
2025-07-03 17:20:16.356 | DEBUG    | __main__:convert_to_markdown_with_markitdown:1091 - markitdown成功转换.pdf文件，生成245字符的Markdown
2025-07-03 17:20:16.372 | INFO     | __main__:process_one_record:2632 - 正在分析文件: c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf
2025-07-03 17:20:16.429 | DEBUG    | __main__:extract_preview_text:1414 - Successfully extracted PDF preview with pdfplumber, 223 characters
2025-07-03 17:20:16.429 | INFO     | __main__:detect_file_type:1508 - 此文件是其他文件: 标项
2025-07-03 17:20:16.434 | INFO     | __main__:process_one_record:2716 - 跳过非招标/合同文件: c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf
2025-07-03 17:20:16.434 | INFO     | __main__:process_one_record:2524 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf
2025-07-03 17:20:16.434 | INFO     | __main__:download_file:950 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf
2025-07-03 17:20:16.632 | INFO     | __main__:download_file:981 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf
2025-07-03 17:20:16.637 | INFO     | __main__:process_one_record:2566 - 附件已保存到: downloads\标项.pdf
2025-07-03 17:20:16.637 | INFO     | __main__:process_one_record:2590 - 使用markitdown转换文件: 标项.pdf
2025-07-03 17:20:16.732 | DEBUG    | __main__:convert_to_markdown_with_markitdown:1091 - markitdown成功转换.pdf文件，生成252字符的Markdown
2025-07-03 17:20:16.736 | INFO     | __main__:process_one_record:2632 - 正在分析文件: ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf
2025-07-03 17:20:16.807 | DEBUG    | __main__:extract_preview_text:1414 - Successfully extracted PDF preview with pdfplumber, 227 characters
2025-07-03 17:20:16.807 | INFO     | __main__:detect_file_type:1508 - 此文件是其他文件: 标项
2025-07-03 17:20:16.808 | INFO     | __main__:process_one_record:2716 - 跳过非招标/合同文件: ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf
2025-07-03 17:20:16.808 | INFO     | __main__:process_one_record:2524 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:16.808 | INFO     | __main__:download_file:950 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:17.004 | INFO     | __main__:download_file:981 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:17.005 | INFO     | __main__:process_one_record:2566 - 附件已保存到: downloads\标项.pdf
2025-07-03 17:20:17.005 | INFO     | __main__:process_one_record:2590 - 使用markitdown转换文件: 标项.pdf
2025-07-03 17:20:18.754 | DEBUG    | __main__:convert_to_markdown_with_markitdown:1091 - markitdown成功转换.pdf文件，生成13335字符的Markdown
2025-07-03 17:20:18.755 | INFO     | __main__:process_one_record:2632 - 正在分析文件: 1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:19.420 | DEBUG    | __main__:extract_preview_text:1414 - Successfully extracted PDF preview with pdfplumber, 4628 characters
2025-07-03 17:20:19.420 | INFO     | __main__:detect_file_type:1458 - 此文件是招标文件: 标项 (匹配关键词: ['采购文件'])
2025-07-03 17:20:19.420 | INFO     | __main__:process_one_record:2643 - 存储招标文件内容: 1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:19.420 | INFO     | __main__:process_one_record:2661 - 开始上传招标文件: 1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:19.421 | INFO     | file_upload_service:upload_file_with_retry:201 - 文件上传尝试 1/3: 招标文件_-55YlZcBfqDgtia4ifmt.pdf
2025-07-03 17:20:19.422 | INFO     | file_upload_service:upload_file:152 - 开始上传文件: 招标文件_-55YlZcBfqDgtia4ifmt.pdf, 大小: 139933 字节
2025-07-03 17:20:19.422 | INFO     | file_upload_service:get_presigned_url:48 - 获取预签名URL: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=139933&objectName=招标文件_-55YlZcBfqDgtia4ifmt.pdf
2025-07-03 17:20:22.079 | INFO     | file_upload_service:get_presigned_url:53 - 获取预签名URL成功: {'success': True, 'message': '操作成功！', 'code': 200, 'result': {'key': '0197cf9645657205bf216214ccc4bcd3', 'presignedUrl': 'https://test-minio.anhuibidding.com/provincial-budget/2025/07/03/0197cf9645657205bf216214ccc4bcd3/%E6%8B%9B%E6%A0%87%E6%96%87%E4%BB%B6_-55YlZcBfqDgtia4ifmt.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T092023Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=8595fa86c7eb80800bdc9422b5a31241dc3e703d749ba4fb42dc788ee9396592'}, 'timestamp': 1751534423591, 'callId': None}
2025-07-03 17:20:22.081 | INFO     | file_upload_service:upload_file_content:96 - 开始上传文件，大小: 139933 字节
2025-07-03 17:20:22.142 | INFO     | file_upload_service:upload_file_content:102 - 文件内容上传成功
2025-07-03 17:20:22.142 | INFO     | file_upload_service:complete_upload:122 - 完成文件上传: http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197cf9645657205bf216214ccc4bcd3
2025-07-03 17:20:22.434 | INFO     | file_upload_service:complete_upload:126 - 文件上传完成
2025-07-03 17:20:22.439 | INFO     | file_upload_service:upload_file:173 - 文件上传成功: 招标文件_-55YlZcBfqDgtia4ifmt.pdf
2025-07-03 17:20:22.439 | INFO     | __main__:process_one_record:2674 - ✓ 招标文件上传成功: 招标文件_-55YlZcBfqDgtia4ifmt.pdf, upload_id: 0197cf9645657205bf216214ccc4bcd3
2025-07-03 17:20:23.548 | INFO     | __main__:llm:885 - 正在调用LLM API (尝试 1/3)...
2025-07-03 17:20:23.548 | INFO     | __main__:llm:900 - enable_thinking=False
2025-07-03 17:20:23.548 | INFO     | __main__:llm:906 - extra_body={'enable_thinking': False}
2025-07-03 17:20:36.281 | INFO     | __main__:llm:910 - LLM API调用成功
2025-07-03 17:20:36.281 | INFO     | __main__:analyze_content:2374 - [
    {
        "bid_name": null,
        "bid_number": "ZJKP2025YW097G",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "义乌市",
        "prj_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
        "prj_number": "ZJKP2025YW097G",
        "prj_type": "货物",
        "release_time": null,
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": null,
        "tenderee": "义乌市中心医院",
        "bid_submission_deadline": null,
        "trade_platform": null,
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "浙江省",
        "city": "金华市",
        "county": "义乌市",
        "announcement_type": "004",
        "object_name": "4K关节镜摄像、刨削手术系统",
        "object_brand": null,
        "object_model": null,
        "object_supplier": "金华正博医疗设备有限公司",
        "object_produce_area": null,
        "object_conf": "技术参数要求详见评分明细表，包括设备的安装、调试、维护保养方案，产品结构及功能等综合评定内容。",
        "object_oem": null,
        "object_amount": 1,
        "object_unit": "套",
        "object_price": null,
        "object_total_price": null,
        "object_maintenance_period": null,
        "object_price_source": null,
        "object_quality": null,
        "bidder_price": 66200.0,
        "bidder_name": "金华正博医疗设备有限公司",
        "bidder_contact_person": null,
        "bidder_contact_phone_number": null,
        "bidder_contract_config_param": null,
        "agent": "浙江开平企业管理咨询有限公司",
        "service_fee": null,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": null
    }
]
2025-07-03 17:20:36.283 | INFO     | __main__:process_one_record:2795 - 已找到并分析招标文件: 1936c211-a613-4839-8a45-59e2945ea18b.pdf
2025-07-03 17:20:36.284 | INFO     | __main__:process_one_record:2524 - 正在下载附件: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf
2025-07-03 17:20:36.284 | INFO     | __main__:download_file:950 - 正在下载文件 (尝试 1/3): https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf
2025-07-03 17:20:36.432 | INFO     | __main__:download_file:981 - 文件下载成功: https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf
2025-07-03 17:20:36.433 | INFO     | __main__:process_one_record:2566 - 附件已保存到: downloads\标项.pdf
2025-07-03 17:20:36.434 | INFO     | __main__:process_one_record:2590 - 使用markitdown转换文件: 标项.pdf
2025-07-03 17:20:36.663 | DEBUG    | __main__:convert_to_markdown_with_markitdown:1091 - markitdown成功转换.pdf文件，生成338字符的Markdown
2025-07-03 17:20:36.666 | INFO     | __main__:process_one_record:2632 - 正在分析文件: 87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf
2025-07-03 17:20:36.793 | DEBUG    | __main__:extract_preview_text:1414 - Successfully extracted PDF preview with pdfplumber, 303 characters
2025-07-03 17:20:36.794 | INFO     | __main__:detect_file_type:1508 - 此文件是其他文件: 标项
2025-07-03 17:20:36.794 | INFO     | __main__:process_one_record:2716 - 跳过非招标/合同文件: 87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf
2025-07-03 17:20:36.795 | INFO     | __main__:process_one_record:2895 - ================================================================================
2025-07-03 17:20:36.795 | INFO     | __main__:process_one_record:2896 - 开始融合分析结果
2025-07-03 17:20:36.796 | INFO     | __main__:process_one_record:2897 - ================================================================================
2025-07-03 17:20:36.796 | INFO     | __main__:process_one_record:2898 - 主体解析结果数量: 1
2025-07-03 17:20:36.797 | INFO     | __main__:process_one_record:2899 - 招标文件解析结果数量: 1
2025-07-03 17:20:36.797 | INFO     | __main__:process_one_record:2900 - 合同文件解析结果数量: 0
2025-07-03 17:20:36.798 | INFO     | __main__:process_one_record:2903 - 主体解析结果:
2025-07-03 17:20:36.799 | INFO     | __main__:process_one_record:2905 - 主体结果 1:
2025-07-03 17:20:36.799 | INFO     | __main__:process_one_record:2906 - {
  "bid_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
  "bid_number": "ZJKP2025YW097G",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "义乌市南门街519号",
  "prj_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
  "prj_number": "ZJKP2025YW097G",
  "prj_type": "货物",
  "release_time": "2025-06-21 15:08:00",
  "prj_approval_authority": null,
  "superintendent_office": "义乌市财政局",
  "superintendent_office_code": null,
  "tenderee": "义乌市中心医院(义乌市中心医院医共体)",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "设备",
  "province": "浙江省",
  "city": "金华市",
  "county": "义乌市",
  "announcement_type": "004",
  "object_name": "4K关节镜摄像、刨削手术系统",
  "object_brand": "AR",
  "object_model": "200-0023等",
  "object_supplier": "金华市瑞信医疗器械有限公司",
  "object_produce_area": null,
  "object_conf": null,
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "套",
  "object_price": 1700000.0,
  "object_total_price": 1700000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 1700000.0,
  "bidder_name": "金华市瑞信医疗器械有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "浙江开平企业管理咨询有限公司",
  "service_fee": 15890.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-55YlZcBfqDgtia4ifmt",
  "source_title": "浙江开平企业管理咨询有限公司关于义乌市中心医院4K关节镜摄像、刨削手术系统采购中标(成交)结果公告",
  "source_create_time": "2025-06-22",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250621_24822271.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf",
      "text": "评审专家抽取规则"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf",
      "text": "标项"
    }
  ],
  "bid_doc_name": null,
  "bid_doc_ext": null,
  "bid_doc_link_out": null,
  "bid_doc_link_key": null,
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 17:20:15"
}
2025-07-03 17:20:36.800 | INFO     | __main__:process_one_record:2911 - 招标文件解析结果:
2025-07-03 17:20:36.801 | INFO     | __main__:process_one_record:2913 - 招标文件结果 1:
2025-07-03 17:20:36.801 | INFO     | __main__:process_one_record:2914 - {
  "bid_name": null,
  "bid_number": "ZJKP2025YW097G",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "义乌市",
  "prj_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
  "prj_number": "ZJKP2025YW097G",
  "prj_type": "货物",
  "release_time": null,
  "prj_approval_authority": null,
  "superintendent_office": null,
  "superintendent_office_code": null,
  "tenderee": "义乌市中心医院",
  "bid_submission_deadline": null,
  "trade_platform": null,
  "procurement_method": null,
  "prj_sub_type": "设备",
  "province": "浙江省",
  "city": "金华市",
  "county": "义乌市",
  "announcement_type": "004",
  "object_name": "4K关节镜摄像、刨削手术系统",
  "object_brand": null,
  "object_model": null,
  "object_supplier": "金华正博医疗设备有限公司",
  "object_produce_area": null,
  "object_conf": "技术参数要求详见评分明细表，包括设备的安装、调试、维护保养方案，产品结构及功能等综合评定内容。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "套",
  "object_price": null,
  "object_total_price": null,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 66200.0,
  "bidder_name": "金华正博医疗设备有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "浙江开平企业管理咨询有限公司",
  "service_fee": null,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-55YlZcBfqDgtia4ifmt",
  "source_title": "浙江开平企业管理咨询有限公司关于义乌市中心医院4K关节镜摄像、刨削手术系统采购中标(成交)结果公告",
  "source_create_time": "2025-06-22",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250621_24822271.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf",
      "text": "评审专家抽取规则"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf",
      "text": "标项"
    }
  ],
  "insert_time": "2025-07-03 17:20:36",
  "bid_doc_name": "标项.pdf",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf",
  "bid_doc_link_key": "0197cf9645657205bf216214ccc4bcd3",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null
}
2025-07-03 17:20:36.802 | INFO     | __main__:process_one_record:2930 - 使用智能融合分析
2025-07-03 17:20:36.803 | INFO     | __main__:intelligent_merge_analysis:1833 - 开始基于object_name的匹配融合分析
2025-07-03 17:20:36.803 | DEBUG    | __main__:merge_analysis:1621 - 招标文件可用于基础补充: object_name完全匹配
2025-07-03 17:20:36.803 | INFO     | __main__:merge_analysis:1661 - 招标文件已通过匹配验证，执行字段覆盖
2025-07-03 17:20:36.803 | INFO     | __main__:merge_analysis:1670 - 招标文件覆盖字段: object_name, object_supplier, object_conf, object_amount, object_unit
2025-07-03 17:20:36.804 | DEBUG    | __main__:merge_analysis:1674 -   object_name: '4K关节镜摄像、刨削手术系统' → '4K关节镜摄像、刨削手术系统'
2025-07-03 17:20:36.805 | DEBUG    | __main__:merge_analysis:1674 -   object_supplier: '金华市瑞信医疗器械有限公司' → '金华正博医疗设备有限公司'
2025-07-03 17:20:36.805 | DEBUG    | __main__:merge_analysis:1674 -   object_conf: '技术参数要求详见评分明细表，包括设备的安装、调试、维护保养方案，产品结构及功能等综合评定内容。' → '技术参数要求详见评分明细表，包括设备的安装、调试、维护保养方案，产品结构及功能等综合评定内容。'
2025-07-03 17:20:36.805 | DEBUG    | __main__:merge_analysis:1674 -   object_amount: '1' → '1'
2025-07-03 17:20:36.806 | DEBUG    | __main__:merge_analysis:1674 -   object_unit: '套' → '套'
2025-07-03 17:20:36.806 | INFO     | __main__:merge_analysis_by_object_name:1791 - 主体标的物 '4K关节镜摄像、刨削手术系统' 匹配结果:
2025-07-03 17:20:36.806 | INFO     | __main__:merge_analysis_by_object_name:1792 -   - 招标文件匹配: 是
2025-07-03 17:20:36.806 | INFO     | __main__:merge_analysis_by_object_name:1793 -   - 合同文件匹配: 否
2025-07-03 17:20:36.807 | INFO     | __main__:intelligent_merge_analysis:1841 - 基于object_name的匹配融合完成，产生1个结果
2025-07-03 17:20:36.807 | INFO     | __main__:intelligent_merge_analysis:1850 - 开始对融合结果进行智能补充
2025-07-03 17:20:36.807 | INFO     | __main__:intelligent_merge_analysis:1854 - 处理第1个融合结果的智能补充
2025-07-03 17:20:36.808 | INFO     | __main__:intelligent_merge_analysis:1867 - 发现空缺字段: ['bid_budget', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office_code', 'bid_submission_deadline', 'procurement_method', 'object_produce_area', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bidder_contact_person', 'bidder_contact_phone_number', 'bidder_contract_config_param', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 17:20:36.808 | INFO     | __main__:intelligent_merge_analysis:1912 - 从招标文件解析结果中获取字段: ['bid_budget', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office_code', 'bid_submission_deadline', 'procurement_method', 'object_produce_area', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bid_cancelled_flag', 'bid_cancelled_reason']
2025-07-03 17:20:36.809 | INFO     | __main__:intelligent_merge_analysis:1928 - 招标文件已解析但缺少字段 ['bid_budget', 'fiscal_delegation_number', 'prj_approval_authority', 'superintendent_office_code', 'bid_submission_deadline', 'procurement_method', 'object_produce_area', 'object_oem', 'object_maintenance_period', 'object_price_source', 'object_quality', 'bid_cancelled_flag', 'bid_cancelled_reason']，跳过LLM重新提取
2025-07-03 17:20:36.809 | INFO     | __main__:intelligent_merge_analysis:1931 - 建议：检查字段定义或文档内容是否包含这些信息
2025-07-03 17:20:36.809 | INFO     | __main__:intelligent_merge_analysis:1965 - 第1个融合结果智能补充完成
2025-07-03 17:20:36.810 | INFO     | __main__:intelligent_merge_analysis:1967 - 智能补充完成，返回1个增强结果
2025-07-03 17:20:36.810 | INFO     | __main__:process_one_record:2945 - ================================================================================
2025-07-03 17:20:36.810 | INFO     | __main__:process_one_record:2946 - 最终融合后的JSON结果列表:
2025-07-03 17:20:36.811 | INFO     | __main__:process_one_record:2947 - ================================================================================
2025-07-03 17:20:36.811 | INFO     | __main__:process_one_record:2949 - 融合结果 1:
2025-07-03 17:20:36.812 | INFO     | __main__:process_one_record:2950 - {
  "bid_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
  "bid_number": "ZJKP2025YW097G",
  "bid_budget": null,
  "fiscal_delegation_number": null,
  "prj_addr": "义乌市南门街519号",
  "prj_name": "义乌市中心医院4K关节镜摄像、刨削手术系统采购",
  "prj_number": "ZJKP2025YW097G",
  "prj_type": "货物",
  "release_time": "2025-06-21 15:08:00",
  "prj_approval_authority": null,
  "superintendent_office": "义乌市财政局",
  "superintendent_office_code": null,
  "tenderee": "义乌市中心医院(义乌市中心医院医共体)",
  "bid_submission_deadline": null,
  "trade_platform": "中国政府采购网",
  "procurement_method": null,
  "prj_sub_type": "设备",
  "province": "浙江省",
  "city": "金华市",
  "county": "义乌市",
  "announcement_type": "004",
  "object_name": "4K关节镜摄像、刨削手术系统",
  "object_brand": "AR",
  "object_model": "200-0023等",
  "object_supplier": "金华正博医疗设备有限公司",
  "object_produce_area": null,
  "object_conf": "技术参数要求详见评分明细表，包括设备的安装、调试、维护保养方案，产品结构及功能等综合评定内容。",
  "object_oem": null,
  "object_amount": 1,
  "object_unit": "套",
  "object_price": 1700000.0,
  "object_total_price": 1700000.0,
  "object_maintenance_period": null,
  "object_price_source": null,
  "object_quality": null,
  "bidder_price": 1700000.0,
  "bidder_name": "金华市瑞信医疗器械有限公司",
  "bidder_contact_person": null,
  "bidder_contact_phone_number": null,
  "bidder_contract_config_param": null,
  "agent": "浙江开平企业管理咨询有限公司",
  "service_fee": 15890.0,
  "bid_cancelled_flag": null,
  "bid_cancelled_reason": null,
  "source_id": "-55YlZcBfqDgtia4ifmt",
  "source_title": "浙江开平企业管理咨询有限公司关于义乌市中心医院4K关节镜摄像、刨削手术系统采购中标(成交)结果公告",
  "source_create_time": "2025-06-22",
  "source_category": "004",
  "source_url": "http://www.ccgp.gov.cn/cggg/dfgg/zbgg/202506/t20250621_24822271.htm",
  "source_appendix": [
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/4dce55c9-7e2c-4912-8641-640c3678d7ef.pdf",
      "text": "评审专家抽取规则"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1d71c75e-675a-4d23-b2df-6ea220fabf9b.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/c5db5005-9f11-4ef6-ab9a-3a83a8279a93.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/ca4e9285-0bb6-463a-8f55-c8566b1d98eb.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf",
      "text": "标项"
    },
    {
      "url": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/87bdb15b-32c1-4d16-b24c-7f75557ad44b.pdf",
      "text": "标项"
    }
  ],
  "bid_doc_name": "标项.pdf",
  "bid_doc_ext": ".pdf",
  "bid_doc_link_out": "https://zcy-gov-open-doc.oss-cn-north-2-gov-1.aliyuncs.com/1071PO/0/20256/1936c211-a613-4839-8a45-59e2945ea18b.pdf",
  "bid_doc_link_key": "0197cf9645657205bf216214ccc4bcd3",
  "contract_name": null,
  "contract_ext": null,
  "contract_link_out": null,
  "contract_link_key": null,
  "insert_time": "2025-07-03 17:20:15"
}
2025-07-03 17:20:36.813 | INFO     | __main__:process_one_record:2951 - ------------------------------------------------------------
2025-07-03 17:20:36.813 | INFO     | __main__:process_one_record:2952 - ================================================================================
2025-07-03 17:20:36.814 | INFO     | __main__:validate_and_normalize_fields:587 - 字段校验完成: 标准字段59个, 有效字段39个, 删除多余字段0个, 补全缺失字段0个
2025-07-03 17:20:36.814 | INFO     | es_deal:insert_document:408 - 开始向索引 markersweb_attachment_analysis_alias 插入文档，文档ID: -55YlZcBfqDgtia4ifmt_0
2025-07-03 17:20:36.866 | INFO     | es_deal:insert_document:410 - 文档插入成功，文档ID: -55YlZcBfqDgtia4ifmt_0
2025-07-03 17:20:36.866 | INFO     | __main__:process_one_record:2967 - 成功插入融合文档 -55YlZcBfqDgtia4ifmt_0
