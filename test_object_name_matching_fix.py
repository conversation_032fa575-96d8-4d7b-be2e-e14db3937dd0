#!/usr/bin/env python3
"""
测试任务1：LLM返回list时按object_name匹配的修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import match_extracted_data_by_object_name, calculate_text_similarity


def test_match_extracted_data_single_dict():
    """测试单个dict的情况"""
    print("=== 测试单个dict的情况 ===")
    
    extracted_data = {
        "object_name": "苯丙氨酸测定试剂盒",
        "object_model": "960人份/盒",
        "object_price": 5.0
    }
    
    target_object_name = "苯丙氨酸测定试剂盒（荧光分析法）"
    
    result = match_extracted_data_by_object_name(extracted_data, target_object_name)
    
    print(f"输入数据: {extracted_data}")
    print(f"目标object_name: {target_object_name}")
    print(f"匹配结果: {result}")
    
    if result == extracted_data:
        print("✅ 单个dict情况测试通过")
    else:
        print("❌ 单个dict情况测试失败")


def test_match_extracted_data_list_with_match():
    """测试list中有匹配的情况"""
    print("\n=== 测试list中有匹配的情况 ===")
    
    extracted_data = [
        {
            "object_name": "苯丙氨酸测定试剂盒",
            "object_model": "960人份/盒",
            "object_price": 5.0
        },
        {
            "object_name": "新生儿促甲状腺激素测定试剂盒",
            "object_model": "100人份/盒",
            "object_price": 8.0
        },
        {
            "object_name": "新生儿17α-羟孕酮测定试剂盒",
            "object_model": "200人份/盒",
            "object_price": 10.0
        }
    ]
    
    target_object_name = "新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）"
    
    result = match_extracted_data_by_object_name(extracted_data, target_object_name)
    
    print(f"输入数据: {len(extracted_data)}个dict")
    for i, item in enumerate(extracted_data):
        print(f"  {i+1}. {item['object_name']}")
    
    print(f"目标object_name: {target_object_name}")
    print(f"匹配结果: {result}")
    
    expected_match = extracted_data[1]  # 第二个应该匹配
    if result == expected_match:
        print("✅ list匹配情况测试通过")
    else:
        print("❌ list匹配情况测试失败")


def test_match_extracted_data_list_no_match():
    """测试list中没有匹配的情况"""
    print("\n=== 测试list中没有匹配的情况 ===")
    
    extracted_data = [
        {
            "object_name": "完全不相关的产品A",
            "object_model": "型号A",
            "object_price": 100.0
        },
        {
            "object_name": "完全不相关的产品B",
            "object_model": "型号B",
            "object_price": 200.0
        }
    ]
    
    target_object_name = "苯丙氨酸测定试剂盒（荧光分析法）"
    
    result = match_extracted_data_by_object_name(extracted_data, target_object_name)
    
    print(f"输入数据: {len(extracted_data)}个dict")
    for i, item in enumerate(extracted_data):
        print(f"  {i+1}. {item['object_name']}")
    
    print(f"目标object_name: {target_object_name}")
    print(f"匹配结果: {result}")
    
    if result == {}:
        print("✅ list无匹配情况测试通过")
    else:
        print("❌ list无匹配情况测试失败")


def test_similarity_calculation():
    """测试相似度计算"""
    print("\n=== 测试相似度计算 ===")
    
    test_cases = [
        {
            "text1": "苯丙氨酸测定试剂盒",
            "text2": "苯丙氨酸测定试剂盒（荧光分析法）",
            "expected_high": True
        },
        {
            "text1": "新生儿促甲状腺激素测定试剂盒",
            "text2": "新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）",
            "expected_high": True
        },
        {
            "text1": "完全不相关的产品",
            "text2": "苯丙氨酸测定试剂盒",
            "expected_high": False
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        similarity = calculate_text_similarity(case["text1"], case["text2"])
        print(f"测试{i}: {similarity:.3f}")
        print(f"  文本1: {case['text1']}")
        print(f"  文本2: {case['text2']}")
        
        if case["expected_high"]:
            if similarity >= 0.6:
                print(f"  ✅ 高相似度测试通过 (>= 0.6)")
            else:
                print(f"  ❌ 高相似度测试失败 (< 0.6)")
        else:
            if similarity < 0.6:
                print(f"  ✅ 低相似度测试通过 (< 0.6)")
            else:
                print(f"  ❌ 低相似度测试失败 (>= 0.6)")


def test_workflow_improvement():
    """测试工作流程改进"""
    print("\n=== 测试工作流程改进 ===")
    
    print("修复前的问题:")
    print("  - LLM返回list时，总是取第一个dict")
    print("  - 每个主体分析结果都与第一个dict融合")
    print("  - 无法保证匹配的正确性")
    
    print("\n修复后的改进:")
    print("  ✅ LLM返回list时，保留完整的list")
    print("  ✅ 使用object_name进行相似度匹配")
    print("  ✅ 相似度阈值0.6，确保匹配质量")
    print("  ✅ 无匹配时跳过融合，避免错误数据")
    print("  ✅ 详细日志记录匹配过程")
    
    print("\n预期效果:")
    print("  - 主体分析结果只与对应的招标/合同文件结果融合")
    print("  - 避免了不相关数据的错误融合")
    print("  - 提高了数据融合的准确性")


if __name__ == "__main__":
    test_match_extracted_data_single_dict()
    test_match_extracted_data_list_with_match()
    test_match_extracted_data_list_no_match()
    test_similarity_calculation()
    test_workflow_improvement()
