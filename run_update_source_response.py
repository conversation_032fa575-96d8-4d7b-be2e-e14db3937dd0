#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
执行更新markersweb_attachment_analysis_alias索引的source_response字段
"""

from update_source_response import main

if __name__ == "__main__":
    print("开始更新markersweb_attachment_analysis_alias索引，添加source_response字段...")
    print("这个操作会:")
    print("1. 查找所有没有source_response字段的文档")
    print("2. 根据source_id从chn_ylcg索引获取response内容")
    print("3. 批量更新文档，添加source_response字段")
    print()
    
    try:
        main()
        print("更新操作成功完成！")
    except Exception as e:
        print(f"更新操作失败: {e}")
        exit(1)
