#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
黑名单管理模块
用于管理处理失败的文档ID，避免重复处理
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from loguru import logger as log


class BlacklistManager:
    """黑名单管理器"""

    def __init__(self, db_path: str = "blacklist.db"):
        """
        初始化黑名单管理器

        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self._init_database()

    def _init_database(self):
        """初始化数据库和表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建黑名单表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS blacklist (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        document_id TEXT UNIQUE NOT NULL,
                        document_title TEXT,
                        document_url TEXT,
                        failure_reason TEXT,
                        failure_count INTEGER DEFAULT 1,
                        first_failure_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_failure_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # 创建索引以提高查询性能
                cursor.execute(
                    """
                    CREATE INDEX IF NOT EXISTS idx_document_id 
                    ON blacklist(document_id)
                """
                )

                conn.commit()
                log.info(f"黑名单数据库初始化成功: {self.db_path}")

        except Exception as e:
            log.error(f"初始化黑名单数据库失败: {e}")
            raise

    def add_to_blacklist(
        self,
        document_id: str,
        document_title: str = None,
        document_url: str = None,
        failure_reason: str = None,
    ) -> bool:
        """
        添加文档到黑名单

        Args:
            document_id: 文档ID
            document_title: 文档标题
            document_url: 文档URL
            failure_reason: 失败原因

        Returns:
            bool: 是否添加成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否已存在
                cursor.execute(
                    "SELECT failure_count FROM blacklist WHERE document_id = ?",
                    (document_id,),
                )
                result = cursor.fetchone()

                if result:
                    # 更新现有记录
                    new_count = result[0] + 1
                    cursor.execute(
                        """
                        UPDATE blacklist 
                        SET failure_count = ?, 
                            last_failure_time = CURRENT_TIMESTAMP,
                            failure_reason = ?
                        WHERE document_id = ?
                    """,
                        (new_count, failure_reason, document_id),
                    )
                    log.info(f"更新黑名单记录: {document_id} (失败次数: {new_count})")
                else:
                    # 插入新记录
                    cursor.execute(
                        """
                        INSERT INTO blacklist 
                        (document_id, document_title, document_url, failure_reason)
                        VALUES (?, ?, ?, ?)
                    """,
                        (document_id, document_title, document_url, failure_reason),
                    )
                    log.info(f"添加到黑名单: {document_id}")

                conn.commit()
                return True

        except Exception as e:
            log.error(f"添加到黑名单失败: {e}")
            return False

    def is_blacklisted(self, document_id: str) -> bool:
        """
        检查文档是否在黑名单中

        Args:
            document_id: 文档ID

        Returns:
            bool: 是否在黑名单中
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT 1 FROM blacklist WHERE document_id = ?", (document_id,)
                )
                return cursor.fetchone() is not None

        except Exception as e:
            log.error(f"检查黑名单状态失败: {e}")
            return False

    def get_blacklist(self, limit: int = None) -> List[Dict]:
        """
        获取黑名单列表

        Args:
            limit: 限制返回数量

        Returns:
            List[Dict]: 黑名单记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                query = """
                    SELECT document_id, document_title, document_url, 
                           failure_reason, failure_count, 
                           first_failure_time, last_failure_time
                    FROM blacklist 
                    ORDER BY last_failure_time DESC
                """

                if limit:
                    query += f" LIMIT {limit}"

                cursor.execute(query)
                rows = cursor.fetchall()

                return [
                    {
                        "document_id": row[0],
                        "document_title": row[1],
                        "document_url": row[2],
                        "failure_reason": row[3],
                        "failure_count": row[4],
                        "first_failure_time": row[5],
                        "last_failure_time": row[6],
                    }
                    for row in rows
                ]

        except Exception as e:
            log.error(f"获取黑名单失败: {e}")
            return []

    def remove_from_blacklist(self, document_id: str) -> bool:
        """
        从黑名单中移除文档

        Args:
            document_id: 文档ID

        Returns:
            bool: 是否移除成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "DELETE FROM blacklist WHERE document_id = ?", (document_id,)
                )

                if cursor.rowcount > 0:
                    log.info(f"从黑名单中移除: {document_id}")
                    conn.commit()
                    return True
                else:
                    log.warning(f"文档不在黑名单中: {document_id}")
                    return False

        except Exception as e:
            log.error(f"从黑名单移除失败: {e}")
            return False

    def clear_blacklist(self) -> bool:
        """
        清空黑名单

        Returns:
            bool: 是否清空成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM blacklist")
                count = cursor.rowcount
                conn.commit()
                log.info(f"清空黑名单，共移除 {count} 条记录")
                return True

        except Exception as e:
            log.error(f"清空黑名单失败: {e}")
            return False

    def get_blacklist_stats(self) -> Dict:
        """
        获取黑名单统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总数
                cursor.execute("SELECT COUNT(*) FROM blacklist")
                total_count = cursor.fetchone()[0]

                # 今天新增
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM blacklist 
                    WHERE DATE(first_failure_time) = DATE('now')
                """
                )
                today_count = cursor.fetchone()[0]

                # 最近失败的
                cursor.execute(
                    """
                    SELECT document_id, document_title, last_failure_time 
                    FROM blacklist 
                    ORDER BY last_failure_time DESC 
                    LIMIT 5
                """
                )
                recent_failures = cursor.fetchall()

                return {
                    "total_count": total_count,
                    "today_count": today_count,
                    "recent_failures": [
                        {
                            "document_id": row[0],
                            "document_title": row[1],
                            "last_failure_time": row[2],
                        }
                        for row in recent_failures
                    ],
                }

        except Exception as e:
            log.error(f"获取黑名单统计失败: {e}")
            return {"total_count": 0, "today_count": 0, "recent_failures": []}

    def get_blacklisted_ids(self) -> List[str]:
        """
        获取所有黑名单文档ID列表

        Returns:
            List[str]: 文档ID列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT document_id FROM blacklist")
                return [row[0] for row in cursor.fetchall()]

        except Exception as e:
            log.error(f"获取黑名单ID列表失败: {e}")
            return []


def print_blacklist_stats(blacklist_manager: BlacklistManager):
    """打印黑名单统计信息"""
    stats = blacklist_manager.get_blacklist_stats()

    print("\n" + "=" * 60)
    print("黑名单统计信息")
    print("=" * 60)
    print(f"总计黑名单文档: {stats['total_count']}")
    print(f"今日新增: {stats['today_count']}")

    if stats["recent_failures"]:
        print("\n最近失败的文档:")
        for i, failure in enumerate(stats["recent_failures"], 1):
            title = failure["document_title"] or "无标题"
            print(f"  {i}. {failure['document_id']} - {title}")
            print(f"     失败时间: {failure['last_failure_time']}")

    print("=" * 60)


if __name__ == "__main__":
    # 测试黑名单管理器
    manager = BlacklistManager()

    # 打印统计信息
    print_blacklist_stats(manager)
