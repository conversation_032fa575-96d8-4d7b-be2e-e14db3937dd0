#!/usr/bin/env python3
"""
执行删除操作的安全脚本

删除markersweb_attachment_analysis_alias索引中符合条件的文档：
1. insert_time在2025-07-04 12:00:00之前
2. source_appendix不等于[]（即有附件的文档）
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from delete_old_documents import DocumentDeleter
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def main():
    """安全执行删除操作"""
    cutoff_time = "2025-07-04 12:00:00"
    batch_size = 1000
    env_file = ".env_delete"
    
    print("=" * 80)
    print("删除旧文档脚本")
    print("=" * 80)
    print(f"删除条件：")
    print(f"  - insert_time < {cutoff_time}")
    print(f"  - source_appendix != []")
    print(f"  - 批量大小: {batch_size}")
    print(f"  - 配置文件: {env_file}")
    print("=" * 80)
    
    try:
        deleter = DocumentDeleter(env_file=env_file)
        
        # 第一步：干运行，查看会删除的文档
        print("\n第一步：查看会删除的文档...")
        deleter.dry_run(cutoff_time)
        
        # 第二步：询问用户确认
        print("\n" + "=" * 80)
        print("⚠️  警告：即将执行实际删除操作！")
        print("⚠️  此操作不可逆，请仔细确认上述文档是否可以删除！")
        print("=" * 80)
        
        while True:
            choice = input("\n请选择操作：\n1. 执行删除\n2. 取消操作\n请输入选择 (1/2): ").strip()
            
            if choice == "1":
                # 二次确认
                confirm = input("\n请再次确认：确定要删除这些文档吗？(yes/no): ").strip().lower()
                
                if confirm in ['yes', 'y']:
                    print("\n第三步：执行删除操作...")
                    deleter.execute_deletion(cutoff_time, batch_size)
                    print("\n✅ 删除操作完成！")
                else:
                    print("\n❌ 已取消删除操作")
                break
                
            elif choice == "2":
                print("\n❌ 已取消删除操作")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
                
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
