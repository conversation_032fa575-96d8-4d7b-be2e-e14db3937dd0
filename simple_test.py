#!/usr/bin/env python3
"""
Simple test to verify the fix for the NoneType error
"""

def test_none_handling():
    """Test the specific case that was causing the error"""
    
    # Test case 1: object_name is None (this was causing the error)
    test_dict = {"object_name": None, "other_field": "value"}
    
    print("Testing the problematic case:")
    print(f"test_dict = {test_dict}")
    
    # Old way (would cause error):
    # item_name = test_dict.get("object_name", "").lower().strip()
    
    # New way (should work):
    item_name = test_dict.get("object_name") or ""
    item_name = item_name.lower().strip()
    
    print(f"item_name after fix: '{item_name}'")
    print("✓ No error occurred!")
    
    # Test case 2: object_name is missing
    test_dict2 = {"other_field": "value"}
    item_name2 = test_dict2.get("object_name") or ""
    item_name2 = item_name2.lower().strip()
    print(f"Missing object_name case: '{item_name2}'")
    
    # Test case 3: object_name is empty string
    test_dict3 = {"object_name": "", "other_field": "value"}
    item_name3 = test_dict3.get("object_name") or ""
    item_name3 = item_name3.lower().strip()
    print(f"Empty string case: '{item_name3}'")
    
    # Test case 4: object_name is valid
    test_dict4 = {"object_name": "测试设备", "other_field": "value"}
    item_name4 = test_dict4.get("object_name") or ""
    item_name4 = item_name4.lower().strip()
    print(f"Valid case: '{item_name4}'")

if __name__ == "__main__":
    test_none_handling()
    print("All tests passed!")
