#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字段校验功能
"""

import os
import sys
import json

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关函数和常量
try:
    from analyse_appendix import validate_and_normalize_fields, STANDARD_FIELDS
    print("✓ 成功导入字段校验函数和常量")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_field_validation():
    """测试字段校验功能"""
    print("=" * 80)
    print("测试字段校验功能")
    print("=" * 80)
    
    # 测试用例1：完整的标准文档
    print("\n1. 测试完整的标准文档")
    complete_doc = {}
    for field in STANDARD_FIELDS:
        complete_doc[field] = f"test_value_{field}"
    
    result1 = validate_and_normalize_fields(complete_doc)
    print(f"   输入字段数: {len(complete_doc)}")
    print(f"   输出字段数: {len(result1)}")
    print(f"   字段顺序正确: {list(result1.keys()) == list(STANDARD_FIELDS)}")
    
    # 测试用例2：包含多余字段的文档
    print("\n2. 测试包含多余字段的文档")
    doc_with_extra = {
        "bid_name": "测试标段",
        "prj_name": "测试项目",
        "extra_field_1": "多余字段1",
        "extra_field_2": "多余字段2",
        "unknown_field": "未知字段"
    }
    
    result2 = validate_and_normalize_fields(doc_with_extra)
    print(f"   输入字段数: {len(doc_with_extra)}")
    print(f"   输出字段数: {len(result2)}")
    print(f"   多余字段已删除: {'extra_field_1' not in result2}")
    print(f"   标准字段保留: {result2.get('bid_name') == '测试标段'}")
    
    # 测试用例3：缺失字段的文档
    print("\n3. 测试缺失字段的文档")
    incomplete_doc = {
        "bid_name": "测试标段",
        "prj_name": "测试项目",
        "tenderee": "测试招标人"
    }
    
    result3 = validate_and_normalize_fields(incomplete_doc)
    print(f"   输入字段数: {len(incomplete_doc)}")
    print(f"   输出字段数: {len(result3)}")
    print(f"   缺失字段已补全: {len(result3) == len(STANDARD_FIELDS)}")
    print(f"   缺失字段为None: {result3.get('bid_number') is None}")
    
    # 测试用例4：空文档
    print("\n4. 测试空文档")
    empty_doc = {}
    
    result4 = validate_and_normalize_fields(empty_doc)
    print(f"   输入字段数: {len(empty_doc)}")
    print(f"   输出字段数: {len(result4)}")
    print(f"   所有字段为None: {all(v is None for v in result4.values())}")
    
    # 测试用例5：非字典类型
    print("\n5. 测试非字典类型")
    invalid_doc = "这不是字典"
    
    result5 = validate_and_normalize_fields(invalid_doc)
    print(f"   输入类型: {type(invalid_doc)}")
    print(f"   输出类型: {type(result5)}")
    print(f"   返回空字典: {result5 == {}}")
    
    return True


def test_standard_fields_integrity():
    """测试标准字段列表的完整性"""
    print("\n" + "=" * 80)
    print("测试标准字段列表完整性")
    print("=" * 80)
    
    print(f"标准字段总数: {len(STANDARD_FIELDS)}")
    print(f"字段类型: {type(STANDARD_FIELDS)}")
    print(f"是否有重复字段: {len(STANDARD_FIELDS) != len(set(STANDARD_FIELDS))}")
    
    # 检查字段分类
    business_fields = STANDARD_FIELDS[:44]  # 前44个是业务字段
    metadata_fields = STANDARD_FIELDS[44:50]  # 接下来6个是元数据字段
    appendix_fields = STANDARD_FIELDS[50:58]  # 接下来8个是附件字段
    system_fields = STANDARD_FIELDS[58:]  # 最后1个是系统字段
    
    print(f"\n字段分类:")
    print(f"  业务数据字段: {len(business_fields)}个")
    print(f"  源数据元数据字段: {len(metadata_fields)}个")
    print(f"  附件相关字段: {len(appendix_fields)}个")
    print(f"  系统字段: {len(system_fields)}个")
    
    # 显示一些关键字段
    key_fields = [
        "bid_name", "prj_name", "tenderee", "bidder_name", 
        "source_id", "bid_doc_name", "insert_time"
    ]
    
    print(f"\n关键字段检查:")
    for field in key_fields:
        exists = field in STANDARD_FIELDS
        print(f"  {field}: {'✓' if exists else '✗'}")
    
    return True


def test_field_order_consistency():
    """测试字段顺序一致性"""
    print("\n" + "=" * 80)
    print("测试字段顺序一致性")
    print("=" * 80)
    
    # 创建测试文档（乱序）
    test_doc = {
        "insert_time": "2025-07-03 12:00:00",
        "bid_name": "测试标段",
        "source_id": "test_001",
        "prj_name": "测试项目",
        "bidder_name": "测试中标单位",
        "contract_name": "测试合同",
    }
    
    print(f"输入字段顺序: {list(test_doc.keys())}")
    
    result = validate_and_normalize_fields(test_doc)
    output_order = [k for k, v in result.items() if v is not None]
    
    print(f"输出字段顺序: {output_order}")
    
    # 检查顺序是否符合标准
    expected_order = [field for field in STANDARD_FIELDS if field in test_doc]
    order_correct = output_order == expected_order
    
    print(f"字段顺序正确: {order_correct}")
    
    if not order_correct:
        print(f"期望顺序: {expected_order}")
        print(f"实际顺序: {output_order}")
    
    return order_correct


def test_real_world_scenario():
    """测试真实世界场景"""
    print("\n" + "=" * 80)
    print("测试真实世界场景")
    print("=" * 80)
    
    # 模拟真实的文档数据
    real_doc = {
        "bid_name": "第一标段",
        "bid_number": "ZBDW-2025-001-01",
        "prj_name": "医疗设备采购项目",
        "prj_number": "YLSB-2025-001",
        "tenderee": "某某医院",
        "object_name": "CT扫描仪",
        "object_brand": "西门子",
        "bidder_name": "某某医疗设备公司",
        "bidder_price": 2500000.0,
        "source_id": "-1pfuZcBsUtJ06NfZI4t",
        "source_title": "医疗设备采购公告",
        "source_url": "http://example.com/notice/123",
        "insert_time": "2025-07-03 12:00:00",
        # 一些多余的字段
        "temp_field": "临时字段",
        "debug_info": "调试信息",
        "processing_status": "已处理"
    }
    
    print(f"真实文档字段数: {len(real_doc)}")
    print(f"有效业务字段: {len([k for k in real_doc.keys() if k in STANDARD_FIELDS])}")
    print(f"多余字段: {len([k for k in real_doc.keys() if k not in STANDARD_FIELDS])}")
    
    result = validate_and_normalize_fields(real_doc)
    
    print(f"\n校验后:")
    print(f"总字段数: {len(result)}")
    print(f"非空字段数: {len([v for v in result.values() if v is not None])}")
    print(f"空字段数: {len([v for v in result.values() if v is None])}")
    
    # 验证关键字段
    key_validations = [
        ("bid_name", result.get("bid_name") == "第一标段"),
        ("bidder_price", result.get("bidder_price") == 2500000.0),
        ("source_id", result.get("source_id") == "-1pfuZcBsUtJ06NfZI4t"),
        ("temp_field", "temp_field" not in result),  # 多余字段应被删除
    ]
    
    print(f"\n关键字段验证:")
    for field, is_valid in key_validations:
        print(f"  {field}: {'✓' if is_valid else '✗'}")
    
    return all(is_valid for _, is_valid in key_validations)


def main():
    """运行所有测试"""
    print("字段校验功能测试")
    print("=" * 80)
    
    test_results = []
    
    try:
        # 运行各项测试
        test_results.append(test_field_validation())
        test_results.append(test_standard_fields_integrity())
        test_results.append(test_field_order_consistency())
        test_results.append(test_real_world_scenario())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "字段校验功能",
            "标准字段列表完整性",
            "字段顺序一致性",
            "真实世界场景"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！字段校验功能正常！")
            print("\n功能特性:")
            print("✅ 字段过滤：删除多余字段")
            print("✅ 字段补全：补全缺失字段为None")
            print("✅ 字段排序：按标准顺序重新组织")
            print("✅ 类型检查：处理非字典类型输入")
            print("✅ 日志记录：详细的操作日志")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
