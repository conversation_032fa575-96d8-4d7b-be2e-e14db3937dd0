#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整的文档处理流程，包括附件融合
"""

import os
from dotenv import load_dotenv
from es_deal import init_es_client
from analyse_appendix import DocumentAnalyzer

def test_complete_process():
    """
    测试完整的文档处理流程
    """
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        # 获取环境变量
        es_index_links = os.getenv("ES_INDEX_LINKS", "chn_ylcg")
        es_index_analysis = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
        model_apikey = os.getenv("MODEL_APIKEY")
        model_name = os.getenv("MODEL_NAME", "Qwen/Qwen2.5-32B-Instruct")
        model_url = os.getenv("MODEL_URL", "https://api-inference.modelscope.cn/v1")
        prompt_spec = os.getenv("PROMPT_SPEC", "")
        
        # 创建分析器实例
        analyzer = DocumentAnalyzer(
            es_client=es_client,
            es_index_links=es_index_links,
            es_index_analysis=es_index_analysis,
            model_apikey=model_apikey,
            model_name=model_name,
            model_url=model_url,
            prompt_spec=prompt_spec,
        )
        
        print("开始测试完整的文档处理流程...")
        print("=" * 80)
        
        # 查找一个有附件的文档进行测试
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "appendix"}},
                        {"range": {"appendix": {"from": 1}}}  # 至少有一个附件
                    ]
                }
            },
            "size": 1
        }
        
        response = es_client.search(
            index=es_index_links,
            body=query
        )
        
        hits = response.get("hits", {}).get("hits", [])
        if not hits:
            print("没有找到有附件的测试文档，使用无附件文档进行测试")
            # 使用无附件文档
            query = {
                "query": {"match_all": {}},
                "size": 1
            }
            response = es_client.search(
                index=es_index_links,
                body=query
            )
            hits = response.get("hits", {}).get("hits", [])
        
        if not hits:
            print("没有找到任何测试文档")
            return
        
        doc = hits[0]
        print(f"测试文档ID: {doc['_id']}")
        print(f"文档标题: {doc['_source'].get('title', 'N/A')}")
        
        appendix = doc['_source'].get('appendix', [])
        print(f"附件数量: {len(appendix) if appendix else 0}")
        
        if appendix:
            print("附件列表:")
            for i, item in enumerate(appendix[:3]):  # 只显示前3个
                print(f"  {i+1}. {item.get('file_name', 'N/A')} ({item.get('file_ext', 'N/A')})")
        
        print("\n开始处理文档...")
        
        # 调用process_one_record方法
        analyzer.process_one_record()
        
        print("文档处理完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_process()
