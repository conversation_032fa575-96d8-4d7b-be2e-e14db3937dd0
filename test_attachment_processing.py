#!/usr/bin/env python3
"""
测试脚本：验证新的附件处理工作流程

测试内容：
1. 验证所有附件都被下载和上传
2. 验证appendix_info字段正确填充
3. 验证早期退出逻辑在分析阶段正常工作
4. 验证招标/合同文件字段正确更新
"""

import os
import sys
import json
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import process_all_attachments, DocumentAnalyzer, STANDARD_FIELDS
from file_upload_service import upload_attachment_file

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def create_mock_document():
    """创建模拟的文档数据"""
    return {
        "_id": "test_doc_123",
        "_source": {
            "title": "测试招标公告",
            "create_time": "2024-01-01 10:00:00",
            "category": "001",
            "url": "http://test.com/doc/123",
            "appendix": [
                {"text": "招标文件.pdf", "url": "http://test.com/download/tender.pdf"},
                {
                    "text": "合同文件.docx",
                    "url": "http://test.com/download/contract.docx",
                },
                {"text": "其他文件.pdf", "url": "http://test.com/download/other.pdf"},
            ],
        },
    }


def mock_download_file(url):
    """模拟文件下载"""
    if "tender.pdf" in url:
        return b"PDF content for tender document"
    elif "contract.docx" in url:
        return b"DOCX content for contract document"
    elif "other.pdf" in url:
        return b"PDF content for other document"
    return None


def mock_upload_attachment_file(file_content, source_id, original_filename, file_ext):
    """模拟文件上传"""
    # 模拟成功上传，返回upload_id
    upload_id = (
        f"upload_{hash(original_filename + source_id)}_{abs(hash(file_content))}"
    )
    return True, upload_id


def mock_detect_file_type(filename, preview):
    """模拟文件类型检测"""
    if "招标" in filename or "tender" in filename.lower():
        return "招标文件"
    elif "合同" in filename or "contract" in filename.lower():
        return "合同文件"
    else:
        return "其他"


def test_process_all_attachments():
    """测试process_all_attachments函数"""
    log.info("=" * 60)
    log.info("测试 process_all_attachments 函数")
    log.info("=" * 60)

    doc = create_mock_document()

    with patch("analyse_appendix.download_file", side_effect=mock_download_file), patch(
        "analyse_appendix.upload_attachment_file",
        side_effect=mock_upload_attachment_file,
    ), patch("analyse_appendix.get_file_extension_from_url") as mock_get_ext:

        # 模拟文件扩展名检测
        def mock_ext(url):
            if ".pdf" in url:
                return ".pdf"
            elif ".docx" in url:
                return ".docx"
            return ""

        mock_get_ext.side_effect = mock_ext

        # 测试函数
        appendix_info, file_content_cache, file_type_cache = process_all_attachments(
            appendix_list=doc["_source"]["appendix"],
            source_id=doc["_id"],
            enable_file_upload=True,
        )

        # 验证结果
        assert len(appendix_info) == 3, f"期望3个附件，实际得到{len(appendix_info)}个"

        for i, attachment in enumerate(appendix_info):
            expected_keys = ["url", "text", "file_ext", "file_link_key"]
            for key in expected_keys:
                assert key in attachment, f"附件{i}缺少字段: {key}"

            assert (
                attachment["file_link_key"] is not None
            ), f"附件{i}的file_link_key为空"
            log.info(
                f"✓ 附件{i+1}: {attachment['text']} - upload_id: {attachment['file_link_key']}"
            )

        log.info("✓ process_all_attachments 测试通过")
        return appendix_info


def test_standard_fields():
    """测试STANDARD_FIELDS是否包含appendix_info"""
    log.info("=" * 60)
    log.info("测试 STANDARD_FIELDS 包含 appendix_info")
    log.info("=" * 60)

    assert "appendix_info" in STANDARD_FIELDS, "STANDARD_FIELDS中缺少appendix_info字段"
    log.info("✓ STANDARD_FIELDS包含appendix_info字段")


def test_file_upload_service():
    """测试文件上传服务"""
    log.info("=" * 60)
    log.info("测试文件上传服务")
    log.info("=" * 60)

    # 模拟文件内容
    file_content = b"test file content"
    source_id = "test_123"
    original_filename = "测试文件.pdf"
    file_ext = ".pdf"

    with patch("file_upload_service.file_upload_service") as mock_service:
        mock_service.upload_file_with_retry.return_value = (True, "test_upload_id_123")

        success, upload_id = upload_attachment_file(
            file_content=file_content,
            source_id=source_id,
            original_filename=original_filename,
            file_ext=file_ext,
        )

        assert success == True, "文件上传应该成功"
        assert (
            upload_id == "test_upload_id_123"
        ), f"期望upload_id为test_upload_id_123，实际为{upload_id}"

        # 验证调用参数
        mock_service.upload_file_with_retry.assert_called_once()
        call_args = mock_service.upload_file_with_retry.call_args

        assert call_args[1]["file_content"] == file_content
        assert "测试文件_test_123.pdf" in call_args[1]["object_name"]

        log.info("✓ 文件上传服务测试通过")


def test_early_exit_logic():
    """测试早期退出逻辑"""
    log.info("=" * 60)
    log.info("测试早期退出逻辑")
    log.info("=" * 60)

    # 创建包含多个招标文件的文档
    doc = {
        "_id": "test_doc_456",
        "_source": {
            "title": "测试多文件招标公告",
            "appendix": [
                {"text": "招标文件1.pdf", "url": "http://test.com/tender1.pdf"},
                {"text": "招标文件2.pdf", "url": "http://test.com/tender2.pdf"},
                {"text": "合同文件1.docx", "url": "http://test.com/contract1.docx"},
                {"text": "合同文件2.docx", "url": "http://test.com/contract2.docx"},
            ],
        },
    }

    # 模拟早期退出逻辑
    found_tender_file = False
    found_contract_file = False
    processed_files = []

    for appendix_item in doc["_source"]["appendix"]:
        filename = appendix_item["text"]
        file_type = mock_detect_file_type(filename, "")

        # 早期退出检查
        if file_type == "招标文件" and found_tender_file:
            log.info(f"✓ 早期退出：跳过后续招标文件 {filename}")
            continue
        elif file_type == "合同文件" and found_contract_file:
            log.info(f"✓ 早期退出：跳过后续合同文件 {filename}")
            continue

        # 处理文件
        processed_files.append(filename)

        # 设置标志
        if file_type == "招标文件":
            found_tender_file = True
            log.info(f"✓ 找到招标文件：{filename}")
        elif file_type == "合同文件":
            found_contract_file = True
            log.info(f"✓ 找到合同文件：{filename}")

    # 验证只处理了第一个招标文件和第一个合同文件
    expected_processed = ["招标文件1.pdf", "合同文件1.docx"]
    assert (
        processed_files == expected_processed
    ), f"期望处理{expected_processed}，实际处理{processed_files}"

    log.info("✓ 早期退出逻辑测试通过")


def run_all_tests():
    """运行所有测试"""
    log.info("开始运行附件处理工作流程测试")
    log.info("=" * 80)

    try:
        # 测试1：标准字段
        test_standard_fields()

        # 测试2：文件上传服务
        test_file_upload_service()

        # 测试3：处理所有附件
        appendix_info = test_process_all_attachments()

        # 测试4：早期退出逻辑
        test_early_exit_logic()

        log.info("=" * 80)
        log.info("🎉 所有测试通过！")
        log.info("=" * 80)

        # 输出示例appendix_info结构
        log.info("示例 appendix_info 结构:")
        log.info(json.dumps(appendix_info, ensure_ascii=False, indent=2))

        return True

    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
