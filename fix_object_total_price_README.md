# fix_object_total_price.py 使用说明

## 功能描述

该脚本用于修复 `markersweb_attachment_analysis_v3` 索引中的标的物总价字段数据不一致问题。

### 修复逻辑

当文档同时满足以下条件时，重新计算并更新 `object_total_price` 字段：
1. `object_price` 字段存在且不为空（不是 null、""、0）
2. `object_amount` 字段存在且不为空（不是 null、""、0）
3. 计算公式：`object_total_price = object_price × object_amount`

### 数据验证规则

- **有效数值**：非 null、非空字符串、非零的数值
- **精度处理**：使用 Decimal 进行精确计算，避免浮点数精度问题
- **误差容忍**：允许 0.01 的浮点数误差，避免重复更新已正确的数据

## 使用方法

### 基本用法

```bash
# 处理一个批次（默认100个文档）
python fix_object_total_price.py

# 指定批处理大小
python fix_object_total_price.py --batch-size 50

# 试运行模式（不执行实际更新）
python fix_object_total_price.py --dry-run

# 处理所有需要修复的文档
python fix_object_total_price.py --all

# 指定索引名称
python fix_object_total_price.py --index markersweb_attachment_analysis_v3
```

### 参数说明

- `--index`: 目标索引名称（默认 `markersweb_attachment_analysis_v3`）
- `--batch-size`: 批处理大小（默认100）
- `--dry-run`: 试运行模式，不执行实际更新
- `--all`: 处理所有需要修复的文档（分批处理）

### 环境变量配置

脚本需要以下环境变量（在 `.env` 文件中配置）：

```env
ES_HOST=http://**********:9200
ES_USER=elastic
ES_PASSWORD=your_password
```

## 执行流程

### 单批次处理模式（默认）

1. 连接到Elasticsearch
2. 查询同时具有 `object_price` 和 `object_amount` 字段的文档总数
3. 获取一个批次的文档（默认100个）
4. 对每个文档：
   - 验证 `object_price` 和 `object_amount` 是否为有效数值
   - 计算新的总价：`object_total_price = object_price × object_amount`
   - 检查是否需要更新（与当前值比较，允许0.01误差）
   - 准备更新操作
5. 批量更新文档
6. 输出统计信息

### 全量处理模式（--all）

1. 使用 Scroll API 分批处理所有文档
2. 每个批次处理指定数量的文档
3. 处理完一个批次后继续下一批次
4. 输出最终统计信息

## 数据处理规则

### 有效数值判断

```python
# 有效数值示例
100.0, "123.45", 99, "  456.78  ", -123.45

# 无效数值示例
None, "", "0", 0, 0.0, "abc", "  "
```

### 计算示例

```python
# 正常计算
price=100.0, amount=2 → total=200.0
price="99.99", amount="10" → total=999.9

# 跳过情况
price=0, amount=5 → 跳过（price为0）
price=100, amount=0 → 跳过（amount为0）
price=None, amount=5 → 跳过（price无效）
```

### 更新判断

```python
# 需要更新的情况
当前total=None, 计算total=200.0 → 更新
当前total=100.0, 计算total=200.0 → 更新
当前total=199.99, 计算total=200.0 → 更新（误差>0.01）

# 不需要更新的情况
当前total=200.0, 计算total=200.0 → 跳过
当前total=200.005, 计算total=200.0 → 跳过（误差<0.01）
```

## 输出信息

### 统计信息

```
==================================================
处理统计:
  本批次文档总数: 100
  需要修复的文档: 25
  已经正确的文档: 60
  数据无效跳过的文档: 10
  计算失败的文档: 5
==================================================
```

### 日志信息

- 处理进度：`[1/100] 准备修复文档 doc_id: price=100.0 × amount=2 = 200.0 (当前值: None)`
- 跳过信息：`跳过文档 doc_id: price=0, amount=5 (无效数据)`
- 更新结果：`批量更新完成 - 成功: 25, 失败: 0`

## 注意事项

1. **试运行模式**：建议首次运行时使用 `--dry-run` 参数查看处理结果
2. **批处理大小**：根据系统性能调整 `--batch-size` 参数
3. **数据精度**：使用 Decimal 进行精确计算，避免浮点数精度问题
4. **误差容忍**：允许 0.01 的误差，避免重复更新已正确的数据
5. **索引检查**：脚本会自动检查目标索引是否存在
6. **数据备份**：重要数据建议先备份

## 错误处理

- **连接失败**：检查ES配置和网络连接
- **索引不存在**：检查索引名称是否正确
- **权限错误**：检查ES用户权限
- **数据类型错误**：脚本会自动跳过无法转换的数据
- **计算失败**：记录错误日志并跳过该文档

## 测试验证

使用测试脚本验证功能：

```bash
python test_fix_object_total_price.py
```

测试内容包括：
- ES连接测试
- 数值验证函数测试
- 计算函数测试
- 索引映射测试
- 文档查询测试

## 示例输出

```
2025-01-07 10:00:00 - INFO - 正在初始化Elasticsearch客户端...
2025-01-07 10:00:01 - INFO - 目标索引: markersweb_attachment_analysis_v3
2025-01-07 10:00:01 - INFO - 批处理大小: 100
2025-01-07 10:00:01 - INFO - 试运行模式: False
2025-01-07 10:00:01 - INFO - 处理所有文档: True
2025-01-07 10:00:02 - INFO - 索引中同时具有object_price和object_amount字段的文档总数: 5000
2025-01-07 10:00:02 - INFO - 本批次检查 100 个文档（总共 5000 个）
2025-01-07 10:00:05 - INFO - 批量更新完成 - 成功: 25, 失败: 0
2025-01-07 10:00:05 - INFO - 修复完成 - 成功: 25, 失败: 0
```

## 性能优化

- 使用批量更新减少网络开销
- 使用 Scroll API 处理大量数据
- 智能跳过已正确的数据
- 精确的数值计算避免重复处理
