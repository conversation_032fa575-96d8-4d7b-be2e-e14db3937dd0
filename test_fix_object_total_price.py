#!/usr/bin/env python3
"""
测试 fix_object_total_price.py 脚本的功能

该脚本用于验证：
1. ES连接是否正常
2. 查询需要修复的文档是否正常
3. 计算逻辑是否正确
4. 数据类型验证是否正常
"""

import sys
from dotenv import load_dotenv
import os

from es_deal import init_es_client, search_documents
from utils.log_cfg import log
from fix_object_total_price import (
    is_valid_numeric_value,
    convert_to_numeric,
    calculate_total_price,
    get_total_documents_count
)


def test_es_connection():
    """测试ES连接"""
    try:
        log.info("测试ES连接...")
        es = init_es_client()
        
        # 测试连接
        if es.ping():
            log.info("✓ ES连接成功")
            return es
        else:
            log.error("✗ ES连接失败")
            return None
            
    except Exception as e:
        log.error(f"✗ ES连接异常: {e}")
        return None


def test_numeric_validation():
    """测试数值验证函数"""
    log.info("测试数值验证函数...")
    
    test_cases = [
        # (value, expected_is_valid, expected_numeric)
        (None, False, None),
        ("", False, None),
        ("0", False, 0.0),
        (0, False, 0.0),
        (0.0, False, 0.0),
        ("123.45", True, 123.45),
        (123.45, True, 123.45),
        (123, True, 123.0),
        ("abc", False, None),
        ("  456.78  ", True, 456.78),
        (-123.45, True, -123.45),
        ("-123.45", True, -123.45),
    ]
    
    success_count = 0
    for i, (value, expected_valid, expected_numeric) in enumerate(test_cases, 1):
        try:
            is_valid = is_valid_numeric_value(value)
            numeric_value = convert_to_numeric(value)
            
            if is_valid == expected_valid and numeric_value == expected_numeric:
                log.info(f"  ✓ 测试 {i}: {value} -> valid={is_valid}, numeric={numeric_value}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {value} -> 期望 valid={expected_valid}, numeric={expected_numeric}, "
                         f"实际 valid={is_valid}, numeric={numeric_value}")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {value} -> 异常: {e}")
    
    log.info(f"数值验证测试完成: {success_count}/{len(test_cases)} 通过")


def test_calculation():
    """测试计算函数"""
    log.info("测试计算函数...")
    
    test_cases = [
        # (price, amount, expected_total)
        (100.0, 2, 200.0),
        ("100.5", "3", 301.5),
        (99.99, 10, 999.9),
        ("0", "5", None),  # price为0，应该返回None
        ("100", "0", None),  # amount为0，应该返回None
        (None, 5, None),
        ("abc", 5, None),
        (100, "abc", None),
        ("123.456", "2.5", 308.64),
    ]
    
    success_count = 0
    for i, (price, amount, expected) in enumerate(test_cases, 1):
        try:
            result = calculate_total_price(price, amount)
            
            if expected is None:
                if result is None:
                    log.info(f"  ✓ 测试 {i}: {price} × {amount} = {result}")
                    success_count += 1
                else:
                    log.error(f"  ✗ 测试 {i}: {price} × {amount} -> 期望 None, 实际 {result}")
            else:
                if result is not None and abs(result - expected) < 0.001:
                    log.info(f"  ✓ 测试 {i}: {price} × {amount} = {result}")
                    success_count += 1
                else:
                    log.error(f"  ✗ 测试 {i}: {price} × {amount} -> 期望 {expected}, 实际 {result}")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {price} × {amount} -> 异常: {e}")
    
    log.info(f"计算函数测试完成: {success_count}/{len(test_cases)} 通过")


def test_query_documents(es_client, index_name: str):
    """测试查询需要修复的文档"""
    try:
        log.info("测试查询需要修复的文档...")
        
        # 获取文档总数
        total_count = get_total_documents_count(es_client, index_name)
        log.info(f"✓ 同时具有object_price和object_amount字段的文档总数: {total_count}")
        
        if total_count > 0:
            # 获取样本文档
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "object_price"}},
                            {"exists": {"field": "object_amount"}}
                        ]
                    }
                },
                "size": 5,
                "_source": ["object_price", "object_amount", "object_total_price"]
            }
            
            result = search_documents(es_client, index_name, query=query)
            
            if result and result.get("hits", {}).get("hits"):
                log.info("✓ 样本文档:")
                for i, hit in enumerate(result["hits"]["hits"], 1):
                    source = hit["_source"]
                    doc_id = hit["_id"]
                    price = source.get("object_price")
                    amount = source.get("object_amount")
                    total = source.get("object_total_price")
                    
                    # 计算期望的总价
                    calculated = calculate_total_price(price, amount)
                    
                    status = "需要修复" if calculated and (not total or abs(calculated - convert_to_numeric(total) or 0) > 0.01) else "正确"
                    
                    log.info(f"  {i}. doc_id: {doc_id}")
                    log.info(f"     price: {price}, amount: {amount}, total: {total}")
                    log.info(f"     计算值: {calculated}, 状态: {status}")
                    
                return True
            else:
                log.warning("✗ 未获取到样本文档")
                return False
        else:
            log.info("没有需要检查的文档")
            return True
            
    except Exception as e:
        log.error(f"✗ 查询文档失败: {e}")
        return False


def test_index_mapping(es_client, index_name: str):
    """测试索引映射"""
    try:
        log.info("测试索引映射...")
        
        # 检查索引是否存在
        if es_client.indices.exists(index=index_name):
            log.info(f"✓ 索引 {index_name} 存在")
            
            # 获取映射信息
            mapping = es_client.indices.get_mapping(index=index_name)
            
            # 检查相关字段的映射
            index_mapping = mapping.get(index_name, {}).get("mappings", {}).get("properties", {})
            
            required_fields = ["object_price", "object_amount", "object_total_price"]
            for field in required_fields:
                if field in index_mapping:
                    field_type = index_mapping[field].get("type", "unknown")
                    log.info(f"✓ {field} 字段存在，类型: {field_type}")
                else:
                    log.warning(f"✗ {field} 字段未在映射中定义")
                    
        else:
            log.error(f"✗ 索引 {index_name} 不存在")
            return False
            
        return True
            
    except Exception as e:
        log.error(f"✗ 测试索引映射失败: {e}")
        return False


def main():
    """主函数"""
    try:
        log.info("开始测试 fix_object_total_price.py 脚本功能...")
        log.info("=" * 60)
        
        # 加载环境变量
        load_dotenv()
        
        # 获取索引名称
        index_name = "markersweb_attachment_analysis_v3"
        log.info(f"目标索引: {index_name}")
        log.info("=" * 60)
        
        # 1. 测试数值验证和计算函数
        test_numeric_validation()
        log.info("=" * 60)
        
        test_calculation()
        log.info("=" * 60)
        
        # 2. 测试ES连接
        es = test_es_connection()
        if not es:
            log.error("ES连接失败，停止测试")
            sys.exit(1)
        
        log.info("=" * 60)
        
        # 3. 测试索引映射
        if not test_index_mapping(es, index_name):
            log.error("索引映射测试失败")
            sys.exit(1)
        
        log.info("=" * 60)
        
        # 4. 测试查询文档
        if not test_query_documents(es, index_name):
            log.error("查询文档测试失败")
            sys.exit(1)
        
        log.info("=" * 60)
        log.info("所有测试完成")
        
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
