#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 analyse_noappendix.py 的字段校验功能
"""

import os
import sys
import json

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关函数和常量
try:
    from analyse_noappendix import validate_and_normalize_fields, STANDARD_FIELDS
    print("✓ 成功导入 analyse_noappendix.py 的字段校验函数和常量")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_noappendix_field_validation():
    """测试 analyse_noappendix.py 的字段校验功能"""
    print("=" * 80)
    print("测试 analyse_noappendix.py 的字段校验功能")
    print("=" * 80)
    
    # 测试用例1：模拟无附件文档的解析结果
    print("\n1. 测试无附件文档的解析结果")
    noappendix_doc = {
        "bid_name": "医疗设备采购标段",
        "prj_name": "某医院设备采购项目",
        "tenderee": "某某医院",
        "object_name": "医疗设备",
        "source_id": "noappendix_test_001",
        "source_title": "医疗设备采购公告",
        "source_url": "http://example.com/notice/001",
        "insert_time": "2025-07-03 15:30:00",
        # 无附件文档不应该有这些附件字段
        # "bid_doc_name": None,
        # "contract_name": None,
    }
    
    result1 = validate_and_normalize_fields(noappendix_doc)
    print(f"   输入字段数: {len(noappendix_doc)}")
    print(f"   输出字段数: {len(result1)}")
    print(f"   附件字段已补全: {result1.get('bid_doc_name') is None}")
    print(f"   业务字段保留: {result1.get('bid_name') == '医疗设备采购标段'}")
    
    # 测试用例2：包含调试字段的文档
    print("\n2. 测试包含调试字段的文档")
    doc_with_debug = {
        "bid_name": "测试标段",
        "prj_name": "测试项目",
        "source_id": "debug_test_002",
        "debug_field": "调试信息",
        "temp_data": "临时数据",
        "processing_info": "处理信息"
    }
    
    result2 = validate_and_normalize_fields(doc_with_debug)
    print(f"   输入字段数: {len(doc_with_debug)}")
    print(f"   输出字段数: {len(result2)}")
    print(f"   调试字段已删除: {'debug_field' not in result2}")
    print(f"   标准字段保留: {result2.get('bid_name') == '测试标段'}")
    
    # 测试用例3：最小化的无附件文档
    print("\n3. 测试最小化的无附件文档")
    minimal_doc = {
        "source_id": "minimal_test_003",
        "source_title": "最小化测试文档",
        "insert_time": "2025-07-03 15:30:00"
    }
    
    result3 = validate_and_normalize_fields(minimal_doc)
    print(f"   输入字段数: {len(minimal_doc)}")
    print(f"   输出字段数: {len(result3)}")
    print(f"   所有标准字段已补全: {len(result3) == len(STANDARD_FIELDS)}")
    print(f"   源数据字段保留: {result3.get('source_id') == 'minimal_test_003'}")
    
    return True


def test_consistency_with_appendix():
    """测试与 analyse_appendix.py 的一致性"""
    print("\n" + "=" * 80)
    print("测试与 analyse_appendix.py 的一致性")
    print("=" * 80)
    
    try:
        # 导入 analyse_appendix.py 的常量
        from analyse_appendix import STANDARD_FIELDS as APPENDIX_STANDARD_FIELDS
        
        # 比较字段列表
        noappendix_fields = set(STANDARD_FIELDS)
        appendix_fields = set(APPENDIX_STANDARD_FIELDS)
        
        print(f"analyse_noappendix.py 字段数: {len(STANDARD_FIELDS)}")
        print(f"analyse_appendix.py 字段数: {len(APPENDIX_STANDARD_FIELDS)}")
        
        # 检查是否完全一致
        if noappendix_fields == appendix_fields:
            print("✓ 两个文件的标准字段列表完全一致")
            
            # 检查顺序是否一致
            if list(STANDARD_FIELDS) == list(APPENDIX_STANDARD_FIELDS):
                print("✓ 字段顺序也完全一致")
                return True
            else:
                print("⚠ 字段内容一致但顺序不同")
                return False
        else:
            # 找出差异
            only_in_noappendix = noappendix_fields - appendix_fields
            only_in_appendix = appendix_fields - noappendix_fields
            
            if only_in_noappendix:
                print(f"✗ 只在 noappendix 中的字段: {sorted(only_in_noappendix)}")
            if only_in_appendix:
                print(f"✗ 只在 appendix 中的字段: {sorted(only_in_appendix)}")
            
            return False
            
    except ImportError as e:
        print(f"✗ 无法导入 analyse_appendix.py: {e}")
        return False


def test_noappendix_specific_scenarios():
    """测试无附件文档特有的场景"""
    print("\n" + "=" * 80)
    print("测试无附件文档特有的场景")
    print("=" * 80)
    
    # 场景1：典型的无附件招标公告
    print("\n1. 典型的无附件招标公告")
    tender_notice = {
        "bid_name": "医疗设备采购",
        "prj_name": "2025年医疗设备采购项目",
        "prj_number": "YLSB-2025-001",
        "tenderee": "市人民医院",
        "object_name": "CT扫描仪",
        "object_brand": "西门子",
        "announcement_type": "001",  # 招标公告
        "province": "北京市",
        "city": "北京市",
        "source_id": "tender_notice_001",
        "source_title": "医疗设备采购招标公告",
        "source_url": "http://example.com/tender/001",
        "insert_time": "2025-07-03 15:30:00"
    }
    
    result1 = validate_and_normalize_fields(tender_notice)
    
    # 验证关键字段
    validations = [
        ("bid_name", result1.get("bid_name") == "医疗设备采购"),
        ("announcement_type", result1.get("announcement_type") == "001"),
        ("附件字段为空", result1.get("bid_doc_name") is None),
        ("合同字段为空", result1.get("contract_name") is None),
    ]
    
    for field, is_valid in validations:
        print(f"   {field}: {'✓' if is_valid else '✗'}")
    
    # 场景2：结果公告（无附件）
    print("\n2. 结果公告（无附件）")
    result_notice = {
        "bid_name": "医疗设备采购",
        "bidder_name": "某医疗设备公司",
        "bidder_price": 2500000.0,
        "announcement_type": "004",  # 结果公告
        "source_id": "result_notice_002",
        "source_title": "医疗设备采购结果公告",
        "insert_time": "2025-07-03 15:30:00"
    }
    
    result2 = validate_and_normalize_fields(result_notice)
    
    # 验证中标信息
    validations2 = [
        ("bidder_name", result2.get("bidder_name") == "某医疗设备公司"),
        ("bidder_price", result2.get("bidder_price") == 2500000.0),
        ("announcement_type", result2.get("announcement_type") == "004"),
    ]
    
    for field, is_valid in validations2:
        print(f"   {field}: {'✓' if is_valid else '✗'}")
    
    return all(is_valid for _, is_valid in validations + validations2)


def test_field_validation_integration():
    """测试字段校验集成效果"""
    print("\n" + "=" * 80)
    print("测试字段校验集成效果")
    print("=" * 80)
    
    # 模拟 analyse_noappendix.py 中的实际使用场景
    print("\n模拟实际使用场景:")
    
    # 模拟从LLM解析得到的结果
    llm_result = {
        "bid_name": "第一标段",
        "prj_name": "医疗设备采购项目",
        "tenderee": "市中心医院",
        "object_name": "核磁共振设备",
        "object_brand": "飞利浦",
        "bidder_name": "医疗设备供应商",
        "bidder_price": 3200000.0,
        "province": "上海市",
        "city": "上海市",
        # 模拟一些LLM可能产生的额外字段
        "confidence_score": 0.95,
        "extraction_method": "llm_analysis",
        "model_version": "qwen2.5"
    }
    
    # 模拟添加元数据
    analyzed_doc = llm_result.copy()
    analyzed_doc.update({
        "source_id": "integration_test_001",
        "source_title": "医疗设备采购公告",
        "source_create_time": "2025-07-03 10:00:00",
        "source_category": "招标公告",
        "source_url": "http://example.com/notice/integration_test",
        "source_appendix": None,  # 无附件
        "insert_time": "2025-07-03 15:30:00"
    })
    
    print(f"LLM解析结果字段数: {len(llm_result)}")
    print(f"添加元数据后字段数: {len(analyzed_doc)}")
    
    # 应用字段校验
    validated_doc = validate_and_normalize_fields(analyzed_doc)
    
    print(f"校验后字段数: {len(validated_doc)}")
    print(f"非空字段数: {len([v for v in validated_doc.values() if v is not None])}")
    
    # 验证关键功能
    checks = [
        ("删除了LLM额外字段", "confidence_score" not in validated_doc),
        ("保留了业务字段", validated_doc.get("bid_name") == "第一标段"),
        ("保留了元数据字段", validated_doc.get("source_id") == "integration_test_001"),
        ("补全了附件字段", validated_doc.get("bid_doc_name") is None),
        ("字段总数正确", len(validated_doc) == len(STANDARD_FIELDS)),
    ]
    
    print(f"\n集成效果验证:")
    for desc, is_valid in checks:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")
    
    return all(is_valid for _, is_valid in checks)


def main():
    """运行所有测试"""
    print("analyse_noappendix.py 字段校验功能测试")
    print("=" * 80)
    
    test_results = []
    
    try:
        # 运行各项测试
        test_results.append(test_noappendix_field_validation())
        test_results.append(test_consistency_with_appendix())
        test_results.append(test_noappendix_specific_scenarios())
        test_results.append(test_field_validation_integration())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "无附件文档字段校验",
            "与 analyse_appendix.py 一致性",
            "无附件特有场景",
            "字段校验集成效果"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！analyse_noappendix.py 的字段校验功能正常！")
            print("\n功能特性:")
            print("✅ 与 analyse_appendix.py 完全一致的字段校验")
            print("✅ 正确处理无附件文档的特殊情况")
            print("✅ 自动补全附件相关字段为None")
            print("✅ 删除LLM产生的额外字段")
            print("✅ 保持标准的字段顺序")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
