#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试有条件字段覆盖功能
"""

import os
import sys
import json

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关函数和常量
try:
    from analyse_appendix import (
        merge_analysis,
        is_valid_field_value,
        TENDER_OVERRIDE_FIELDS,
        CONTRACT_OVERRIDE_FIELDS,
    )

    print("✓ 成功导入字段覆盖相关函数和常量")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_is_valid_field_value():
    """测试字段值有效性检查函数"""
    print("=" * 80)
    print("测试字段值有效性检查函数")
    print("=" * 80)

    test_cases = [
        # (值, 期望结果, 描述)
        (None, False, "None值"),
        ("", False, "空字符串"),
        ("null", False, "字符串'null'"),
        ("NULL", False, "字符串'NULL'"),
        ("Null", False, "字符串'Null'"),
        (0, True, "数值0"),
        (0.0, True, "浮点数0.0"),
        ("有效值", True, "有效字符串"),
        (123, True, "有效整数"),
        (45.67, True, "有效浮点数"),
        ([], False, "空列表"),
        ([1, 2, 3], True, "非空列表"),
        ({}, False, "空字典"),
        ({"key": "value"}, True, "非空字典"),
        (False, True, "布尔值False"),
        (True, True, "布尔值True"),
    ]

    passed = 0
    for value, expected, description in test_cases:
        result = is_valid_field_value(value)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {description}: {value} → {result} (期望: {expected})")
        if result == expected:
            passed += 1

    print(f"\n有效性检查测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)


def test_tender_field_override():
    """测试招标文件字段覆盖"""
    print("\n" + "=" * 80)
    print("测试招标文件字段覆盖")
    print("=" * 80)

    # 测试用例1：招标文件有有效值，应该覆盖
    print("\n1. 测试招标文件有效值覆盖")

    main_result = {
        "bid_name": "第一标段",
        "object_name": "主体解析的设备名称",
        "object_brand": None,  # 主体解析为空
        "object_model": "主体型号",
        "bidder_name": "主体中标单位",
    }

    tender_result = {
        "object_name": "招标文件中的设备名称",  # 应该覆盖
        "object_brand": "西门子",  # 应该覆盖（主体为None）
        "object_model": "招标文件型号",  # 应该覆盖
        "object_conf": "详细技术配置参数",  # 新增字段
        "bidder_name": "招标文件中标单位",  # 不应该覆盖（不在TENDER_OVERRIDE_FIELDS中）
    }

    contract_result = {}

    merged = merge_analysis(main_result, tender_result, contract_result)

    # 验证覆盖结果
    checks = [
        ("object_name覆盖", merged.get("object_name") == "招标文件中的设备名称"),
        ("object_brand覆盖", merged.get("object_brand") == "西门子"),
        ("object_model覆盖", merged.get("object_model") == "招标文件型号"),
        ("object_conf新增", merged.get("object_conf") == "详细技术配置参数"),
        ("bidder_name不覆盖", merged.get("bidder_name") == "主体中标单位"),
        ("bid_name保留", merged.get("bid_name") == "第一标段"),
    ]

    for desc, is_valid in checks:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    # 测试用例2：招标文件有无效值，不应该覆盖
    print("\n2. 测试招标文件无效值不覆盖")

    main_result2 = {"object_name": "主体设备名称", "object_brand": "主体品牌"}

    tender_result2 = {
        "object_name": "",  # 空值，不应该覆盖
        "object_brand": None,  # None值，不应该覆盖
        "object_model": "null",  # null字符串，不应该覆盖
    }

    merged2 = merge_analysis(main_result2, tender_result2, {})

    checks2 = [
        ("object_name不覆盖", merged2.get("object_name") == "主体设备名称"),
        ("object_brand不覆盖", merged2.get("object_brand") == "主体品牌"),
        ("object_model不添加", merged2.get("object_model") is None),
    ]

    for desc, is_valid in checks2:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    return all(is_valid for _, is_valid in checks + checks2)


def test_contract_field_override():
    """测试合同文件字段覆盖"""
    print("\n" + "=" * 80)
    print("测试合同文件字段覆盖")
    print("=" * 80)

    # 测试用例1：合同文件有有效值，应该覆盖
    print("\n1. 测试合同文件有效值覆盖")

    main_result = {
        "bid_name": "第一标段",
        "bidder_name": "主体中标单位",
        "bidder_price": None,  # 主体解析为空
        "object_name": "主体设备名称",
    }

    tender_result = {
        "bidder_name": "招标文件中标单位",  # 不应该被招标文件覆盖
        "bidder_price": 1000000.0,  # 不应该被招标文件覆盖
    }

    contract_result = {
        "bidder_name": "合同文件中标单位",  # 应该覆盖
        "bidder_price": 2500000.0,  # 应该覆盖
        "bidder_contact_person": "张三",  # 新增字段
        "object_name": "合同文件设备名称",  # 不应该覆盖（不在CONTRACT_OVERRIDE_FIELDS中）
    }

    merged = merge_analysis(main_result, tender_result, contract_result)

    # 验证覆盖结果
    checks = [
        ("bidder_name覆盖", merged.get("bidder_name") == "合同文件中标单位"),
        ("bidder_price覆盖", merged.get("bidder_price") == 2500000.0),
        ("bidder_contact_person新增", merged.get("bidder_contact_person") == "张三"),
        ("object_name不覆盖", merged.get("object_name") == "主体设备名称"),
        ("bid_name保留", merged.get("bid_name") == "第一标段"),
    ]

    for desc, is_valid in checks:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    # 测试用例2：合同文件有无效值，不应该覆盖
    print("\n2. 测试合同文件无效值不覆盖")

    main_result2 = {"bidder_name": "主体中标单位", "bidder_price": 1500000.0}

    contract_result2 = {
        "bidder_name": "",  # 空值，不应该覆盖
        "bidder_price": None,  # None值，不应该覆盖
        "bidder_contact_person": "null",  # null字符串，不应该覆盖
    }

    merged2 = merge_analysis(main_result2, {}, contract_result2)

    checks2 = [
        ("bidder_name不覆盖", merged2.get("bidder_name") == "主体中标单位"),
        ("bidder_price不覆盖", merged2.get("bidder_price") == 1500000.0),
        ("bidder_contact_person不添加", merged2.get("bidder_contact_person") is None),
    ]

    for desc, is_valid in checks2:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    return all(is_valid for _, is_valid in checks + checks2)


def test_combined_override():
    """测试招标文件和合同文件同时覆盖"""
    print("\n" + "=" * 80)
    print("测试招标文件和合同文件同时覆盖")
    print("=" * 80)

    main_result = {
        "bid_name": "医疗设备采购",
        "object_name": "主体设备名称",
        "object_brand": "主体品牌",
        "bidder_name": "主体中标单位",
        "bidder_price": None,
    }

    tender_result = {
        "object_name": "招标文件设备名称",  # 应该覆盖
        "object_brand": "招标文件品牌",  # 应该覆盖
        "object_conf": "技术配置参数",  # 新增
        "bidder_name": "招标文件中标单位",  # 不应该覆盖（不在TENDER_OVERRIDE_FIELDS中）
    }

    contract_result = {
        "bidder_name": "合同文件中标单位",  # 应该覆盖
        "bidder_price": 3000000.0,  # 应该覆盖
        "bidder_contact_person": "李四",  # 新增
        "object_name": "合同文件设备名称",  # 不应该覆盖（不在CONTRACT_OVERRIDE_FIELDS中）
    }

    merged = merge_analysis(main_result, tender_result, contract_result)

    print(f"融合结果:")
    for key, value in merged.items():
        print(f"  {key}: {value}")

    # 验证覆盖结果
    checks = [
        ("招标文件object_name覆盖", merged.get("object_name") == "招标文件设备名称"),
        ("招标文件object_brand覆盖", merged.get("object_brand") == "招标文件品牌"),
        ("招标文件object_conf新增", merged.get("object_conf") == "技术配置参数"),
        ("合同文件bidder_name覆盖", merged.get("bidder_name") == "合同文件中标单位"),
        ("合同文件bidder_price覆盖", merged.get("bidder_price") == 3000000.0),
        (
            "合同文件bidder_contact_person新增",
            merged.get("bidder_contact_person") == "李四",
        ),
        ("主体bid_name保留", merged.get("bid_name") == "医疗设备采购"),
    ]

    print(f"\n验证结果:")
    for desc, is_valid in checks:
        print(f"   {desc}: {'✓' if is_valid else '✗'}")

    return all(is_valid for _, is_valid in checks)


def test_override_fields_constants():
    """测试覆盖字段常量定义"""
    print("\n" + "=" * 80)
    print("测试覆盖字段常量定义")
    print("=" * 80)

    print(f"招标文件覆盖字段数量: {len(TENDER_OVERRIDE_FIELDS)}")
    print("招标文件覆盖字段:")
    for i, field in enumerate(TENDER_OVERRIDE_FIELDS, 1):
        print(f"  {i:2d}. {field}")

    print(f"\n合同文件覆盖字段数量: {len(CONTRACT_OVERRIDE_FIELDS)}")
    print("合同文件覆盖字段:")
    for i, field in enumerate(CONTRACT_OVERRIDE_FIELDS, 1):
        print(f"  {i:2d}. {field}")

    # 检查是否有重叠字段
    overlap = set(TENDER_OVERRIDE_FIELDS) & set(CONTRACT_OVERRIDE_FIELDS)
    if overlap:
        print(f"\n⚠ 发现重叠字段: {overlap}")
        print("  这可能导致覆盖冲突，需要检查优先级")
        return False
    else:
        print("\n✓ 招标文件和合同文件覆盖字段无重叠")
        return True


def main():
    """运行所有测试"""
    print("有条件字段覆盖功能测试")
    print("=" * 80)

    test_results = []

    try:
        # 运行各项测试
        test_results.append(test_is_valid_field_value())
        test_results.append(test_tender_field_override())
        test_results.append(test_contract_field_override())
        test_results.append(test_combined_override())
        test_results.append(test_override_fields_constants())

        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)

        test_names = [
            "字段值有效性检查",
            "招标文件字段覆盖",
            "合同文件字段覆盖",
            "招标和合同同时覆盖",
            "覆盖字段常量定义",
        ]

        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")

        passed_count = sum(test_results)
        total_count = len(test_results)

        print(f"\n总计: {passed_count}/{total_count} 个测试通过")

        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！有条件字段覆盖功能正常！")
            print("\n功能特性:")
            print("✅ 招标文件字段有条件覆盖")
            print("✅ 合同文件字段有条件覆盖")
            print("✅ 字段值有效性检查")
            print("✅ 覆盖操作日志记录")
            print("✅ 不影响其他字段融合")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")

    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
