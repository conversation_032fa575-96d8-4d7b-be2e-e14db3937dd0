import requests

# 字节
url = "http://************:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize=1000&objectName=123.pdf"

payload = {}
headers = {}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)


import requests

url = "https://test-minio.anhuibidding.com/provincial-budget/2025/06/30/0197be9e848170218f7bf2331a0af3a3/123.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=2rL9zsQq77gRR1PsMlqF%2F20250630%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250630T021549Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=c62cd4583f191d53a1772025564900a92612bd542195c19aa33c42fccebbd574"

payload = "<file contents here>"
headers = {"Content-Type": "application/pdf"}

response = requests.request("PUT", url, headers=headers, data=payload)

print(response.text)


import requests

url = "http://************:4003/xinecaiFile/budget/minio-file/put-presign-url/complete/0197be9e848170218f7bf2331a0af3a3"

payload = {}
headers = {}

response = requests.request("PUT", url, headers=headers, data=payload)

print(response.text)
