#!/usr/bin/env python3
"""
测试analyse_appendix.py中最大重试次数修复

验证修复内容：
1. extract_missing_fields函数的max_retries从3改为2
2. download_file函数的max_retries从3改为2
3. intelligent_merge_analysis函数的max_retries从3改为2
4. DocumentAnalyzer类的max_retries从3改为2
5. 错误检查逻辑从"已重试3次"改为"已重试2次"
"""

import os
import sys
import logging
import inspect
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import (
    DocumentAnalyzer,
    llm,
    extract_fields_from_content,
    download_file,
    intelligent_merge_analysis,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def test_function_default_values():
    """测试各个函数的默认max_retries值"""
    log.info("=" * 80)
    log.info("测试analyse_appendix.py中各函数的默认max_retries值")
    log.info("=" * 80)

    # 测试1：extract_fields_from_content函数
    log.info("测试1：extract_fields_from_content函数")
    sig = inspect.signature(extract_fields_from_content)
    max_retries_param = sig.parameters["max_retries"]
    default_value = max_retries_param.default

    assert (
        default_value == 2
    ), f"期望extract_fields_from_content的max_retries默认值为2，实际为{default_value}"
    log.info(f"✓ extract_fields_from_content默认max_retries值正确: {default_value}")

    # 测试2：download_file函数
    log.info("\n测试2：download_file函数")
    sig = inspect.signature(download_file)
    max_retries_param = sig.parameters["max_retries"]
    default_value = max_retries_param.default

    assert (
        default_value == 2
    ), f"期望download_file的max_retries默认值为2，实际为{default_value}"
    log.info(f"✓ download_file默认max_retries值正确: {default_value}")

    # 测试3：intelligent_merge_analysis函数
    log.info("\n测试3：intelligent_merge_analysis函数")
    sig = inspect.signature(intelligent_merge_analysis)
    max_retries_param = sig.parameters["max_retries"]
    default_value = max_retries_param.default

    assert (
        default_value == 2
    ), f"期望intelligent_merge_analysis的max_retries默认值为2，实际为{default_value}"
    log.info(f"✓ intelligent_merge_analysis默认max_retries值正确: {default_value}")

    # 测试4：llm函数
    log.info("\n测试4：llm函数")
    sig = inspect.signature(llm)
    max_retries_param = sig.parameters["max_retries"]
    default_value = max_retries_param.default

    assert default_value == 2, f"期望llm的max_retries默认值为2，实际为{default_value}"
    log.info(f"✓ llm默认max_retries值正确: {default_value}")

    log.info("\n✅ 所有函数的默认max_retries值测试通过")


def test_document_analyzer_default_value():
    """测试DocumentAnalyzer类的默认max_retries值"""
    log.info("\n" + "=" * 80)
    log.info("测试DocumentAnalyzer类的默认max_retries值")
    log.info("=" * 80)

    # 创建模拟的ES客户端
    mock_es = Mock()

    # 创建DocumentAnalyzer实例，不传入max_retries参数，使用默认值
    analyzer = DocumentAnalyzer(
        es_client=mock_es,
        es_index_links="test_links",
        es_index_analysis="test_analysis",
        model_apikey="test_key",
        model_name="test_model",
        model_url="test_url",
        prompt_spec="test_prompt",
        # 注意：这里没有传入max_retries，应该使用默认值2
    )

    # 验证默认值
    assert (
        analyzer.max_retries == 2
    ), f"期望DocumentAnalyzer默认max_retries值为2，实际为{analyzer.max_retries}"
    log.info(f"✓ DocumentAnalyzer默认max_retries值正确: {analyzer.max_retries}")

    log.info("\n✅ DocumentAnalyzer默认max_retries值测试通过")


def test_llm_retry_behavior():
    """测试llm函数的重试行为"""
    log.info("\n" + "=" * 80)
    log.info("测试llm函数的重试行为")
    log.info("=" * 80)

    # 计数器，记录API调用次数
    call_count = 0

    def mock_openai_create(**kwargs):
        nonlocal call_count
        call_count += 1
        log.info(f"模拟API调用第 {call_count} 次")

        # 前2次调用失败，第3次不应该被调用（因为max_retries=2）
        if call_count <= 2:
            raise Exception("504 Gateway Timeout")
        else:
            # 这不应该被执行到
            return Mock(choices=[Mock(message=Mock(content="success"))])

    # 模拟OpenAI客户端
    mock_client = Mock()
    mock_client.chat.completions.create = mock_openai_create

    with patch("analyse_appendix.OpenAI", return_value=mock_client):
        try:
            # 调用llm函数，使用默认的max_retries=2
            result = llm(
                messages=[{"role": "user", "content": "test"}],
                model_name="test_model",
                model_apikey="test_key",
                model_url="test_url",
                # 不传入max_retries，使用默认值2
            )
            log.error("❌ 期望抛出异常，但函数成功返回了")
            assert False, "期望抛出异常"
        except Exception as e:
            error_msg = str(e)
            log.info(f"捕获到期望的异常: {error_msg}")

            # 验证错误消息
            assert (
                "已重试2次" in error_msg
            ), f"期望错误消息包含'已重试2次'，实际: {error_msg}"
            log.info("✓ 错误消息正确包含'已重试2次'")

            # 验证调用次数
            assert call_count == 2, f"期望API被调用2次，实际调用{call_count}次"
            log.info(f"✓ API调用次数正确: {call_count}次")

    log.info("\n✅ llm函数重试行为测试通过")


def test_download_file_retry_behavior():
    """测试download_file函数的重试行为"""
    log.info("\n" + "=" * 80)
    log.info("测试download_file函数的重试行为")
    log.info("=" * 80)

    # 计数器，记录请求次数
    call_count = 0

    def mock_requests_get(url, **kwargs):
        nonlocal call_count
        call_count += 1
        log.info(f"模拟下载请求第 {call_count} 次")

        # 前2次调用失败，第3次不应该被调用（因为max_retries=2）
        if call_count <= 2:
            import requests

            raise requests.exceptions.RequestException("Connection timeout")
        else:
            # 这不应该被执行到
            mock_response = Mock()
            mock_response.content = b"file content"
            mock_response.raise_for_status.return_value = None
            return mock_response

    with patch("analyse_appendix.requests.get", side_effect=mock_requests_get):
        # 调用download_file函数，使用默认的max_retries=2
        result = download_file("http://example.com/file.pdf")

        # download_file函数在失败时返回None
        assert result is None, f"期望返回None，实际返回{result}"

        # 验证调用次数
        assert call_count == 2, f"期望下载被尝试2次，实际尝试{call_count}次"
        log.info(f"✓ 下载尝试次数正确: {call_count}次")

    log.info("\n✅ download_file函数重试行为测试通过")


def test_consistency_across_files():
    """测试两个文件之间的一致性"""
    log.info("\n" + "=" * 80)
    log.info("测试analyse_appendix.py和analyse_noappendix.py的一致性")
    log.info("=" * 80)

    # 导入analyse_noappendix模块进行对比
    from analyse_noappendix import (
        DocumentAnalyzer as NoAppendixAnalyzer,
        llm as noappendix_llm,
    )

    # 测试llm函数的一致性
    appendix_llm_sig = inspect.signature(llm)
    noappendix_llm_sig = inspect.signature(noappendix_llm)

    appendix_max_retries = appendix_llm_sig.parameters["max_retries"].default
    noappendix_max_retries = noappendix_llm_sig.parameters["max_retries"].default

    assert (
        appendix_max_retries == noappendix_max_retries
    ), f"两个文件的llm函数max_retries不一致: appendix={appendix_max_retries}, noappendix={noappendix_max_retries}"
    log.info(f"✓ 两个文件的llm函数max_retries一致: {appendix_max_retries}")

    # 测试DocumentAnalyzer类的一致性
    mock_es = Mock()

    appendix_analyzer = DocumentAnalyzer(
        es_client=mock_es,
        es_index_links="test_links",
        es_index_analysis="test_analysis",
        model_apikey="test_key",
        model_name="test_model",
        model_url="test_url",
        prompt_spec="test_prompt",
    )

    noappendix_analyzer = NoAppendixAnalyzer(
        es_client=mock_es,
        es_index_links="test_links",
        es_index_analysis="test_analysis",
        model_apikey="test_key",
        model_name="test_model",
        model_url="test_url",
        prompt_spec="test_prompt",
    )

    assert (
        appendix_analyzer.max_retries == noappendix_analyzer.max_retries
    ), f"两个文件的DocumentAnalyzer max_retries不一致: appendix={appendix_analyzer.max_retries}, noappendix={noappendix_analyzer.max_retries}"
    log.info(
        f"✓ 两个文件的DocumentAnalyzer max_retries一致: {appendix_analyzer.max_retries}"
    )

    log.info("\n✅ 两个文件之间的一致性测试通过")


def main():
    """主测试函数"""
    log.info("开始analyse_appendix.py最大重试次数修复测试")
    log.info("=" * 100)

    try:
        # 运行所有测试
        test_function_default_values()
        test_document_analyzer_default_value()
        test_llm_retry_behavior()
        test_download_file_retry_behavior()
        test_consistency_across_files()

        log.info("\n" + "=" * 100)
        log.info("🎉 所有analyse_appendix.py最大重试次数修复测试通过！")
        log.info("=" * 100)

        log.info("\n修复总结:")
        log.info("✓ extract_fields_from_content函数max_retries从3改为2")
        log.info("✓ download_file函数max_retries从3改为2")
        log.info("✓ intelligent_merge_analysis函数max_retries从3改为2")
        log.info("✓ DocumentAnalyzer类max_retries从3改为2")
        log.info("✓ 错误检查逻辑从'已重试3次'改为'已重试2次'")
        log.info("✓ 与analyse_noappendix.py保持一致")

        return True

    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
