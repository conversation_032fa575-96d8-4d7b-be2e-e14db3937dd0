#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 analyse_noappendix.py 中的黑名单功能
"""

import subprocess
import sys
import os


def test_noappendix_blacklist_commands():
    """测试 analyse_noappendix.py 的黑名单命令"""
    print("=" * 80)
    print("测试 analyse_noappendix.py 的黑名单功能")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "查看黑名单统计信息",
            "cmd": ['python', 'analyse_noappendix.py', 'blacklist', '--action', 'stats'],
            "should_succeed": True
        },
        {
            "name": "添加测试文档到黑名单",
            "cmd": ['python', 'analyse_noappendix.py', 'blacklist', '--action', 'add', '--id=noappendix_test_001', '--title', '无附件测试文档', '--reason', '测试黑名单功能'],
            "should_succeed": True
        },
        {
            "name": "列出黑名单文档",
            "cmd": ['python', 'analyse_noappendix.py', 'blacklist', '--action', 'list', '--limit', '5'],
            "should_succeed": True
        },
        {
            "name": "添加以-开头的文档ID",
            "cmd": ['python', 'analyse_noappendix.py', 'blacklist', '--action', 'add', '--id=-1noappendix_test', '--title', '以-开头的测试文档'],
            "should_succeed": True
        },
        {
            "name": "移除测试文档",
            "cmd": ['python', 'analyse_noappendix.py', 'blacklist', '--action', 'remove', '--id=noappendix_test_001'],
            "should_succeed": True
        },
        {
            "name": "移除以-开头的文档",
            "cmd": ['python', 'analyse_noappendix.py', 'blacklist', '--action', 'remove', '--id=-1noappendix_test'],
            "should_succeed": True
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   命令: {' '.join(test_case['cmd'])}")
        
        try:
            result = subprocess.run(
                test_case['cmd'], 
                capture_output=True, 
                text=True, 
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            success = result.returncode == 0
            
            if test_case['should_succeed']:
                if success:
                    print(f"   ✓ 测试通过：命令执行成功")
                    # 显示关键输出
                    if result.stdout:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if any(keyword in line for keyword in ['✓', '已将文档', '已从黑名单', '总计黑名单', '黑名单文档列表']):
                                print(f"     {line}")
                    results.append(True)
                else:
                    print(f"   ✗ 测试失败：命令应该成功但失败了")
                    if result.stderr:
                        print(f"     错误: {result.stderr.strip()}")
                    if result.stdout:
                        print(f"     输出: {result.stdout.strip()}")
                    results.append(False)
            else:
                if not success:
                    print(f"   ✓ 测试通过：命令正确失败")
                    results.append(True)
                else:
                    print(f"   ✗ 测试失败：命令应该失败但成功了")
                    results.append(False)
                    
        except subprocess.TimeoutExpired:
            print(f"   ✗ 测试失败：命令超时")
            results.append(False)
        except Exception as e:
            print(f"   ✗ 测试失败：异常 - {e}")
            results.append(False)
    
    return results


def test_help_message():
    """测试帮助信息"""
    print("\n" + "=" * 80)
    print("测试帮助信息")
    print("=" * 80)
    
    try:
        result = subprocess.run(
            ['python', 'analyse_noappendix.py', 'blacklist', '--help'],
            capture_output=True,
            text=True,
            timeout=15,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            help_text = result.stdout
            if "黑名单管理工具" in help_text and "--action" in help_text:
                print("✓ 帮助信息正常显示")
                return True
            else:
                print("✗ 帮助信息内容不完整")
                return False
        else:
            print("✗ 获取帮助信息失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试帮助信息失败: {e}")
        return False


def test_import_functionality():
    """测试导入功能是否正常"""
    print("\n" + "=" * 80)
    print("测试导入功能")
    print("=" * 80)
    
    try:
        # 测试是否能正常导入黑名单管理器
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # 尝试导入
        from blacklist_manager import BlacklistManager
        
        # 创建实例
        manager = BlacklistManager("test_noappendix.db")
        
        # 测试基本功能
        stats = manager.get_blacklist_stats()
        
        print("✓ 黑名单管理器导入和初始化成功")
        print(f"  当前黑名单文档数量: {stats['total_count']}")
        
        # 清理测试数据库
        try:
            os.remove("test_noappendix.db")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"✗ 导入功能测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("测试 analyse_noappendix.py 的黑名单功能")
    print("=" * 80)
    
    try:
        # 测试导入功能
        import_result = test_import_functionality()
        
        # 测试命令行功能
        command_results = test_noappendix_blacklist_commands()
        
        # 测试帮助信息
        help_result = test_help_message()
        
        # 汇总结果
        all_results = [import_result] + command_results + [help_result]
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "导入功能测试",
            "查看黑名单统计信息",
            "添加测试文档到黑名单",
            "列出黑名单文档",
            "添加以-开头的文档ID",
            "移除测试文档",
            "移除以-开头的文档",
            "帮助信息测试"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, all_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(all_results)
        total_count = len(all_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！analyse_noappendix.py 的黑名单功能正常！")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
        # 显示使用建议
        print("\n" + "=" * 80)
        print("analyse_noappendix.py 黑名单使用方法:")
        print("=" * 80)
        print("# 查看统计信息")
        print("python analyse_noappendix.py blacklist --action stats")
        print("\n# 添加文档到黑名单")
        print('python analyse_noappendix.py blacklist --action add --id=文档ID --title "标题" --reason "原因"')
        print("\n# 列出黑名单文档")
        print("python analyse_noappendix.py blacklist --action list")
        print("\n# 移除文档")
        print("python analyse_noappendix.py blacklist --action remove --id=文档ID")
        print("\n# 清空黑名单")
        print("python analyse_noappendix.py blacklist --action clear")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
