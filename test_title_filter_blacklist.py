#!/usr/bin/env python3
"""
测试公告类型999标题过滤功能的黑名单集成

该脚本用于验证：
1. 跳过的文档是否正确添加到黑名单
2. 黑名单功能是否正常工作
3. 下次运行时是否会跳过黑名单中的文档
"""

import sys
import os
import tempfile

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from blacklist_manager import BlacklistManager
from utils.log_cfg import log


def test_blacklist_integration():
    """测试黑名单集成功能"""
    log.info("测试黑名单集成功能...")

    # 使用临时数据库进行测试
    temp_db = tempfile.NamedTemporaryFile(suffix=".db", delete=False)
    temp_db.close()

    try:
        # 创建黑名单管理器
        blacklist_manager = BlacklistManager(temp_db.name)

        # 模拟公告类型999标题过滤场景
        test_documents = [
            {
                "doc_id": "test_999_001",
                "title": "贵州中医药大学第一附属医院医疗设备维保（手术机器人维保）废标公告",
                "url": "http://example.com/test1",
                "should_be_blacklisted": True,
                "reason": "不包含合同关键词",
            },
            {
                "doc_id": "test_999_002",
                "title": "某某医院医疗设备采购招标公告",
                "url": "http://example.com/test2",
                "should_be_blacklisted": True,
                "reason": "不包含合同关键词",
            },
            {
                "doc_id": "test_999_003",
                "title": "绍兴市口腔医院医疗设备采购合同公告",
                "url": "http://example.com/test3",
                "should_be_blacklisted": False,
                "reason": "包含合同关键词",
            },
            {
                "doc_id": "test_999_004",
                "title": "某某医院设备维护服务协议公告",
                "url": "http://example.com/test4",
                "should_be_blacklisted": False,
                "reason": "包含服务协议关键词",
            },
        ]

        success_count = 0
        total_tests = 0

        # 测试1：模拟添加应该被过滤的文档到黑名单
        log.info("=" * 60)
        log.info("测试1：模拟添加被过滤的文档到黑名单")
        log.info("=" * 60)

        for doc in test_documents:
            if doc["should_be_blacklisted"]:
                total_tests += 1

                # 模拟标题过滤逻辑
                success = blacklist_manager.add_to_blacklist(
                    document_id=doc["doc_id"],
                    document_title=doc["title"],
                    document_url=doc["url"],
                    failure_reason="公告类型999：标题不包含合同关键词（合同、服务协议）",
                )

                if success:
                    log.info(f"✓ 成功添加到黑名单: {doc['doc_id']}")
                    log.info(f"  标题: {doc['title'][:50]}...")
                    log.info(f"  原因: {doc['reason']}")
                    success_count += 1
                else:
                    log.error(f"✗ 添加到黑名单失败: {doc['doc_id']}")

        # 测试2：验证文档是否在黑名单中
        log.info("=" * 60)
        log.info("测试2：验证文档是否在黑名单中")
        log.info("=" * 60)

        for doc in test_documents:
            total_tests += 1
            is_blacklisted = blacklist_manager.is_blacklisted(doc["doc_id"])

            if doc["should_be_blacklisted"]:
                # 应该在黑名单中
                if is_blacklisted:
                    log.info(f"✓ 文档正确在黑名单中: {doc['doc_id']}")
                    success_count += 1
                else:
                    log.error(f"✗ 文档应该在黑名单中但不在: {doc['doc_id']}")
            else:
                # 不应该在黑名单中
                if not is_blacklisted:
                    log.info(f"✓ 文档正确不在黑名单中: {doc['doc_id']}")
                    success_count += 1
                else:
                    log.error(f"✗ 文档不应该在黑名单中但在: {doc['doc_id']}")

        # 测试3：获取黑名单列表
        log.info("=" * 60)
        log.info("测试3：获取黑名单列表")
        log.info("=" * 60)

        blacklist = blacklist_manager.get_blacklist()
        expected_blacklist_count = sum(
            1 for doc in test_documents if doc["should_be_blacklisted"]
        )

        total_tests += 1
        if len(blacklist) == expected_blacklist_count:
            log.info(f"✓ 黑名单数量正确: {len(blacklist)} 个文档")
            success_count += 1

            for item in blacklist:
                log.info(f"  - {item['document_id']}: {item['document_title'][:50]}...")
                log.info(f"    原因: {item['failure_reason']}")
        else:
            log.error(
                f"✗ 黑名单数量错误: 期望 {expected_blacklist_count}, 实际 {len(blacklist)}"
            )

        # 测试4：获取黑名单ID列表（用于排除查询）
        log.info("=" * 60)
        log.info("测试4：获取黑名单ID列表")
        log.info("=" * 60)

        blacklisted_ids = blacklist_manager.get_blacklisted_ids()
        expected_ids = [
            doc["doc_id"] for doc in test_documents if doc["should_be_blacklisted"]
        ]

        total_tests += 1
        if set(blacklisted_ids) == set(expected_ids):
            log.info(f"✓ 黑名单ID列表正确: {blacklisted_ids}")
            success_count += 1
        else:
            log.error(f"✗ 黑名单ID列表错误:")
            log.error(f"  期望: {expected_ids}")
            log.error(f"  实际: {blacklisted_ids}")

        # 测试5：模拟重复添加（应该增加失败次数）
        log.info("=" * 60)
        log.info("测试5：测试重复添加文档")
        log.info("=" * 60)

        test_doc = test_documents[0]  # 选择第一个应该被黑名单的文档
        if test_doc["should_be_blacklisted"]:
            total_tests += 1

            # 再次添加同一个文档
            success = blacklist_manager.add_to_blacklist(
                document_id=test_doc["doc_id"],
                document_title=test_doc["title"],
                document_url=test_doc["url"],
                failure_reason="公告类型999：重复过滤",
            )

            if success:
                log.info(f"✓ 成功更新重复文档: {test_doc['doc_id']}")
                success_count += 1

                # 检查失败次数是否增加
                blacklist_item = next(
                    (
                        item
                        for item in blacklist_manager.get_blacklist()
                        if item["document_id"] == test_doc["doc_id"]
                    ),
                    None,
                )
                if blacklist_item and blacklist_item["failure_count"] > 1:
                    log.info(f"✓ 失败次数正确增加: {blacklist_item['failure_count']}")
                else:
                    log.warning("失败次数未正确增加")
            else:
                log.error(f"✗ 更新重复文档失败: {test_doc['doc_id']}")

        # 输出测试结果
        log.info("=" * 60)
        log.info("测试结果汇总")
        log.info("=" * 60)
        log.info(f"总测试数: {total_tests}")
        log.info(f"成功测试数: {success_count}")
        log.info(f"失败测试数: {total_tests - success_count}")
        log.info(f"成功率: {success_count/total_tests*100:.1f}%")

        return success_count == total_tests

    finally:
        # 清理临时数据库
        try:
            os.unlink(temp_db.name)
        except:
            pass


def test_blacklist_statistics():
    """测试黑名单统计功能"""
    log.info("测试黑名单统计功能...")

    # 使用临时数据库进行测试
    temp_db = tempfile.NamedTemporaryFile(suffix=".db", delete=False)
    temp_db.close()

    try:
        blacklist_manager = BlacklistManager(temp_db.name)

        # 添加一些测试数据
        test_data = [
            ("doc1", "标题1", "url1", "原因1"),
            ("doc2", "标题2", "url2", "原因2"),
            ("doc3", "标题3", "url3", "原因3"),
        ]

        for doc_id, title, url, reason in test_data:
            blacklist_manager.add_to_blacklist(doc_id, title, url, reason)

        # 测试统计功能
        stats = blacklist_manager.get_blacklist_stats()

        log.info("黑名单统计信息:")
        log.info(f"  总文档数: {stats['total_count']}")
        log.info(f"  今日新增: {stats['today_count']}")
        log.info(f"  最近失败的文档数: {len(stats['recent_failures'])}")

        return stats["total_count"] == len(test_data)

    finally:
        try:
            os.unlink(temp_db.name)
        except:
            pass


def main():
    """主函数"""
    try:
        log.info("开始测试公告类型999标题过滤的黑名单集成功能...")
        log.info("=" * 80)

        all_tests_passed = True

        # 测试1：黑名单集成功能
        if not test_blacklist_integration():
            all_tests_passed = False

        log.info("=" * 80)

        # 测试2：黑名单统计功能
        if not test_blacklist_statistics():
            all_tests_passed = False

        log.info("=" * 80)

        if all_tests_passed:
            log.info("✓ 所有测试通过！公告类型999标题过滤的黑名单集成功能正常工作。")
        else:
            log.error("✗ 部分测试失败，请检查代码实现。")
            sys.exit(1)

    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
