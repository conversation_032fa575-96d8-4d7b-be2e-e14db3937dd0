#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试复杂JSON修复功能
"""

import json
from analyse_noappendix import clean_json_data

def test_complex_json_issues():
    """
    测试复杂JSON问题的修复
    """
    print("=" * 80)
    print("测试复杂JSON问题修复")
    print("=" * 80)
    
    # 模拟实际遇到的问题JSON
    problematic_json = '''[
    {
        "bid_name": null,
        "bid_number": "采购计划-[2025]-00073号",
        "object_price_source":"嘉和美康",
        "service_fee": 137798.0
    },
    {
        "bid_name": null,
        "bid_number": "采购计划-[2025]-00073号",
        "service_fee": 13779.,
        "object_brand": "嘉和美康"
    },
    {
        "bid_name": null,
        "bid_number": "采购计划-[202500073000000000000000000000000000000000000000000]",
        ,
        "object_name": "医疗质量管理系统"
    },
    {
        "bid_name": null,
        "object_brand": "嘉和美",
        "object_brand": "嘉和美康",
         "object_amount": 1
    },
    {
        "bid_name": null,
        "object_amount": "",
        "object_amount": "",
        "object_quality": "",
        ,
        "service_fee": ""
    },
    {'''
    
    print("问题JSON包含:")
    print("1. 冒号后缺少空格: object_price_source:\"嘉和美康\"")
    print("2. 不完整数值: service_fee: 13779.")
    print("3. 多余逗号: , 单独成行")
    print("4. 重复字段: object_brand 出现两次")
    print("5. JSON截断: 最后一个对象不完整")
    print()
    
    # 测试修复
    try:
        print("开始修复...")
        cleaned_json = clean_json_data(problematic_json)
        
        print("✅ JSON清理完成")
        
        # 尝试解析
        parsed_data = json.loads(cleaned_json)
        print("✅ 修复后的JSON解析成功")
        
        print(f"\n修复结果:")
        print(f"  解析出 {len(parsed_data)} 个对象")
        
        # 检查具体修复效果
        for i, obj in enumerate(parsed_data, 1):
            print(f"\n  对象 {i}:")
            if "object_price_source" in obj:
                print(f"    ✅ object_price_source: {obj['object_price_source']}")
            if "service_fee" in obj:
                service_fee = obj["service_fee"]
                if isinstance(service_fee, (int, float)) and service_fee > 0:
                    print(f"    ✅ service_fee: {service_fee} (修复成功)")
                else:
                    print(f"    ⚠️  service_fee: {service_fee}")
            if "object_brand" in obj:
                print(f"    ✅ object_brand: {obj['object_brand']} (重复字段已处理)")
        
        print(f"\n✅ 所有问题都已修复！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()

def test_specific_issues():
    """
    测试特定问题的修复
    """
    print("\n" + "=" * 80)
    print("测试特定问题修复")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "冒号后缺少空格",
            "json": '{"test":"value"}',
            "expected_fix": True
        },
        {
            "name": "不完整数值",
            "json": '{"price": 123.}',
            "expected_fix": True
        },
        {
            "name": "多余逗号",
            "json": '{"a": 1,\n,\n"b": 2}',
            "expected_fix": True
        },
        {
            "name": "JSON截断",
            "json": '[{"a": 1}, {"b": 2',
            "expected_fix": True
        },
        {
            "name": "复合问题",
            "json": '{"price":123.,"name":"test",\n,\n"value"',
            "expected_fix": True
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        print(f"原始: {case['json']}")
        
        try:
            # 测试原始JSON
            try:
                json.loads(case['json'])
                print("  ⚠️  原始JSON竟然可以解析")
            except:
                print("  ✅ 原始JSON解析失败（预期）")
            
            # 测试修复
            cleaned = clean_json_data(case['json'])
            parsed = json.loads(cleaned)
            print(f"  ✅ 修复成功: {cleaned}")
            
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n" + "=" * 80)
    print("测试边界情况")
    print("=" * 80)
    
    edge_cases = [
        ("空字符串", ""),
        ("只有空格", "   "),
        ("只有逗号", ","),
        ("不完整的开始", "{"),
        ("不完整的数组", "["),
        ("多层嵌套截断", '[{"a": {"b": {"c"'),
    ]
    
    for name, test_json in edge_cases:
        print(f"\n测试: {name}")
        try:
            result = clean_json_data(test_json)
            print(f"  ✅ 处理成功: {repr(result)}")
            
            # 尝试解析结果
            if result.strip():
                try:
                    json.loads(result)
                    print("  ✅ 结果可以解析")
                except:
                    print("  ⚠️  结果无法解析，但没有崩溃")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")

def main():
    """
    主函数
    """
    test_complex_json_issues()
    test_specific_issues()
    test_edge_cases()
    
    print("\n" + "=" * 80)
    print("复杂JSON修复测试完成")
    print("现在定时任务应该能够处理各种JSON格式问题")
    print("=" * 80)

if __name__ == "__main__":
    main()
