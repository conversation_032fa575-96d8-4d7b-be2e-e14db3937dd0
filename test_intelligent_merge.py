#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能融合功能的脚本
"""

import json
from analyse_appendix import (
    identify_missing_fields,
    extract_fields_from_content,
    intelligent_merge_analysis,
    ALL_FIELDS,
    CONTRACT_FIELDS
)


def test_identify_missing_fields():
    """测试空缺字段识别功能"""
    print("=" * 50)
    print("测试空缺字段识别功能")
    print("=" * 50)
    
    # 测试数据：包含一些空缺字段的字典
    test_dict = {
        "prj_name": "测试项目",
        "prj_number": "TEST001",
        "tenderee": None,  # 空缺字段
        "object_name": "",  # 空缺字段
        "object_brand": "   ",  # 空白字符串，也算空缺
        "bidder_name": "测试公司",
        "bidder_price": 100000.0,
        "agent": None,  # 空缺字段
    }
    
    missing_fields = identify_missing_fields(test_dict)
    print(f"输入字典: {json.dumps(test_dict, ensure_ascii=False, indent=2)}")
    print(f"识别到的空缺字段: {missing_fields}")
    
    # 验证结果
    expected_missing = ["tenderee", "object_name", "object_brand", "agent"]
    # 还会包含其他未在test_dict中的字段
    for field in expected_missing:
        assert field in missing_fields, f"字段 {field} 应该被识别为空缺字段"
    
    print("✓ 空缺字段识别功能测试通过")


def test_field_classification():
    """测试字段分类功能"""
    print("=" * 50)
    print("测试字段分类功能")
    print("=" * 50)
    
    # 测试合同相关字段
    contract_fields_in_all = [f for f in ALL_FIELDS if f in CONTRACT_FIELDS]
    other_fields = [f for f in ALL_FIELDS if f not in CONTRACT_FIELDS]
    
    print(f"合同相关字段 ({len(contract_fields_in_all)}个): {contract_fields_in_all}")
    print(f"其他字段 ({len(other_fields)}个): {other_fields[:10]}...")  # 只显示前10个
    
    print("✓ 字段分类功能测试通过")


def test_intelligent_merge_basic():
    """测试基础智能融合功能（不调用LLM）"""
    print("=" * 50)
    print("测试基础智能融合功能")
    print("=" * 50)
    
    # 模拟主体解析结果
    main_results = [
        {
            "prj_name": "测试项目1",
            "prj_number": "TEST001",
            "tenderee": None,  # 空缺
            "object_name": "设备A",
            "object_brand": None,  # 空缺
            "bidder_name": None,  # 空缺
            "bidder_price": None,  # 空缺
        },
        {
            "prj_name": "测试项目1",
            "prj_number": "TEST001", 
            "tenderee": None,  # 空缺
            "object_name": "设备B",
            "object_brand": None,  # 空缺
            "bidder_name": None,  # 空缺
            "bidder_price": None,  # 空缺
        }
    ]
    
    # 模拟招标文件解析结果
    tender_results = [
        {
            "tenderee": "测试招标人",
            "object_brand": "品牌A",
        }
    ]
    
    # 模拟合同文件解析结果
    contract_results = [
        {
            "bidder_name": "中标公司",
            "bidder_price": 500000.0,
        }
    ]
    
    # 不提供文档内容和模型配置，只测试基础融合
    results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
    )
    
    print(f"融合前主体结果数量: {len(main_results)}")
    print(f"融合后结果数量: {len(results)}")
    
    # 验证融合结果
    for i, result in enumerate(results):
        print(f"\n融合结果 {i+1}:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 验证招标文件信息是否正确融合
        assert result.get("tenderee") == "测试招标人", "招标人信息应该从招标文件中获取"
        assert result.get("object_brand") == "品牌A", "品牌信息应该从招标文件中获取"
        
        # 验证合同文件信息是否正确融合
        assert result.get("bidder_name") == "中标公司", "中标公司信息应该从合同文件中获取"
        assert result.get("bidder_price") == 500000.0, "中标价格信息应该从合同文件中获取"
    
    print("✓ 基础智能融合功能测试通过")


def main():
    """运行所有测试"""
    print("开始测试智能融合功能...")
    
    try:
        test_identify_missing_fields()
        test_field_classification()
        test_intelligent_merge_basic()
        
        print("\n" + "=" * 50)
        print("所有测试通过！✓")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
