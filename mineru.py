import requests

url='https://mineru.net/api/v4/extract/task'
header = {
    'Content-Type':'application/json',
    "Authorization":"Bearer eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJqdGkiOiIyMTAwMDc1MiIsInJvbCI6IlJPTEVfUkVHSVNURVIiLCJpc3MiOiJPcGVuWExhYiIsImlhdCI6MTc0OTYzMzQ3MywiY2xpZW50SWQiOiJsa3pkeDU3bnZ5MjJqa3BxOXgydyIsInBob25lIjoiMTM5NTY2NTIwOTkiLCJvcGVuSWQiOm51bGwsInV1aWQiOiJiMDQ2MzljMS1kOWM1LTQ4NGItYTdkMi1jY2I4ZGJiMzhhMmQiLCJlbWFpbCI6IiIsImV4cCI6MTc1MDg0MzA3M30.IeDZu25pWZ-YQe3UjAoIiq26eg-CyGIZR4rdV6E7NEZ-L7mlAuXWCIr3XHm1fnDAy4GAS1jge-jtzEER7gM93g"
}
data = {
    'url':'https://cdn-mineru.openxlab.org.cn/demo/example.pdf',
    'is_ocr':True,
    'enable_formula': False,
}

res = requests.post(url,headers=header,json=data)
print(res.status_code)
print(res.json())
print(res.json()["data"])