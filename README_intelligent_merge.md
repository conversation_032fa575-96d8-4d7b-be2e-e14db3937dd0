# 智能融合功能说明

## 概述

智能融合功能是对原有文档解析和融合逻辑的重要增强，能够自动识别主体解析结果中的空缺字段，并从招标文件和合同文件中智能检索和补充这些字段的值。

## 功能特点

### 1. 自动空缺字段识别
- 自动识别主体解析结果中值为 `None`、空字符串或空白字符串的字段
- 支持所有预定义的字段类型（44个字段）

### 2. 智能字段分类
- **合同相关字段**：优先从合同文件中检索
  - `bidder_price`（中标金额）
  - `bidder_name`（中标单位名称）
  - `bidder_contact_person`（中标单位联系人）
  - `bidder_contact_phone_number`（中标单位联系人电话）
  - `bidder_contract_config_param`（中标合同配置参数）

- **其他字段**：优先从招标文件中检索
  - 项目信息字段（项目名称、编号、地址等）
  - 标的物信息字段（名称、品牌、型号等）
  - 招标相关字段（招标人、代理机构等）

### 3. 跨文档检索
- 如果在优先文档中找不到字段值，会自动从另一个文档中检索
- 确保最大化地补充空缺字段

### 4. LLM智能提取
- 使用大语言模型从文档内容中智能提取字段值
- 支持自然语言理解，能够处理各种格式的文档内容
- 自动进行数据类型转换和格式标准化

## 使用方法

### 1. 基础使用（不使用LLM）

```python
from analyse_appendix import intelligent_merge_analysis

# 主体解析结果
main_results = [
    {
        "prj_name": "医院设备采购项目",
        "object_name": "CT设备",
        "tenderee": None,  # 空缺字段
        "bidder_name": None,  # 空缺字段
    }
]

# 招标文件解析结果
tender_results = [{"tenderee": "某市人民医院"}]

# 合同文件解析结果  
contract_results = [{"bidder_name": "医疗设备公司"}]

# 执行智能融合
merged_results = intelligent_merge_analysis(
    main_list=main_results,
    tender_list=tender_results,
    contract_list=contract_results
)
```

### 2. 使用文档内容（需要LLM配置）

```python
# 使用文档内容进行智能提取
merged_results = intelligent_merge_analysis(
    main_list=main_results,
    tender_content="招标文件的完整内容...",
    contract_content="合同文件的完整内容...",
    model_apikey="your_api_key",
    model_name="your_model_name",
    model_url="your_model_url"
)
```

### 3. 在DocumentAnalyzer中使用

```python
from analyse_appendix import DocumentAnalyzer

analyzer = DocumentAnalyzer(
    es_client=es_client,
    es_index_links="links_index",
    es_index_analysis="analysis_index",
    model_apikey="your_api_key",
    model_name="your_model_name",
    model_url="your_model_url",
    prompt_spec="your_prompt"
)

# 使用智能融合（默认启用）
analyzer.process_one_record(use_intelligent_merge=True)

# 使用传统融合
analyzer.process_one_record(use_intelligent_merge=False)
```

## 核心函数说明

### `identify_missing_fields(result_dict: dict) -> List[str]`
识别字典中的空缺字段。

**参数：**
- `result_dict`: 解析结果字典

**返回：**
- 空缺字段名列表

### `extract_fields_from_content(content, missing_fields, model_config) -> dict`
从文档内容中提取指定字段的值。

**参数：**
- `content`: 文档内容
- `missing_fields`: 需要提取的字段列表
- `model_config`: LLM配置参数

**返回：**
- 提取到的字段值字典

### `intelligent_merge_analysis(main_list, tender_content, contract_content, ...) -> List[dict]`
主要的智能融合函数。

**参数：**
- `main_list`: 主体解析结果列表
- `tender_content`: 招标文件内容（可选）
- `contract_content`: 合同文件内容（可选）
- `tender_list`: 招标文件解析结果列表（可选）
- `contract_list`: 合同文件解析结果列表（可选）
- `model_apikey`: API密钥（可选）
- `model_name`: 模型名称（可选）
- `model_url`: 模型URL（可选）

**返回：**
- 融合后的结果列表

## 融合逻辑

1. **基础融合**：首先使用现有的 `merge_analysis` 函数进行基础融合
2. **空缺识别**：识别融合后仍然空缺的字段
3. **字段分类**：将空缺字段分为合同相关字段和其他字段
4. **优先检索**：
   - 合同相关字段优先从合同文件中检索
   - 其他字段优先从招标文件中检索
5. **跨文档检索**：如果优先文档中找不到，从另一个文档中检索
6. **结果融合**：将检索到的字段值融合到最终结果中

## 优势

1. **智能化**：自动识别和补充空缺字段，减少人工干预
2. **准确性**：基于字段类型的优先级检索，提高数据准确性
3. **完整性**：跨文档检索确保最大化地补充信息
4. **灵活性**：支持多种使用方式，可选择是否使用LLM
5. **兼容性**：保持向后兼容，可以选择传统融合方式

## 测试

运行测试脚本验证功能：

```bash
python test_intelligent_merge.py
```

运行示例脚本查看使用方法：

```bash
python example_intelligent_merge.py
```

## 注意事项

1. **LLM配置**：使用文档内容提取功能需要配置LLM相关参数
2. **性能考虑**：LLM调用会增加处理时间，建议根据实际需求选择使用
3. **数据质量**：提取效果依赖于文档内容的质量和LLM的理解能力
4. **成本控制**：LLM调用可能产生费用，建议合理控制使用频率

## 更新日志

- **v1.0**：实现基础智能融合功能
- 支持空缺字段自动识别
- 支持基于字段类型的优先级检索
- 支持LLM智能提取
- 支持跨文档检索
- 保持向后兼容性
