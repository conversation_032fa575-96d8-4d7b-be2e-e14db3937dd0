from elasticsearch import Elasticsearch, helpers
from elasticsearch.helpers import scan, bulk

# 源ES配置
SRC_ES_HOSTS = [
    "http://**********:9200",
    "http://**********:9200",
    "http://**********:9200",
]
SRC_ES_USER = "elastic"
SRC_ES_PASS = "W8DOwJ2xs4mBV4BcNBNi"
SRC_INDEX = "markersweb_attachment_analysis_v3"

# 目标ES配置
DST_ES_HOST = "http://***********:9200"
DST_ES_USER = "elastic"
DST_ES_PASS = "elastic"
DST_INDEX = "markersweb_attachment_analysis_v3"
DST_ALIAS = "markersweb_attachment_analysis_alias"

# 连接源ES
src_es = Elasticsearch(SRC_ES_HOSTS, http_auth=(SRC_ES_USER, SRC_ES_PASS))
# 连接目标ES
dst_es = Elasticsearch([DST_ES_HOST], http_auth=(DST_ES_USER, DST_ES_PASS))

# # 1. 获取mapping并创建目标索引
# if not dst_es.indices.exists(index=DST_INDEX):
#     mapping = src_es.indices.get_mapping(index=SRC_INDEX, request_timeout=60)
#     settings = src_es.indices.get_settings(index=SRC_INDEX)
#     body = {
#         "settings": settings[SRC_INDEX]["settings"]["index"],
#         "mappings": mapping[SRC_INDEX]["mappings"],
#     }
#     # 清理settings中无关参数
#     for k in ["creation_date", "uuid", "version", "provided_name"]:
#         body["settings"].pop(k, None)
#     dst_es.indices.create(index=DST_INDEX, body=body)
#     print(f"目标索引 {DST_INDEX} 已创建。")
# else:
#     print(f"目标索引 {DST_INDEX} 已存在。")


# # 2. 批量迁移数据
# def scan_docs(es, index):
#     return helpers.scan(es, index=index, query={"query": {"match_all": {}}})


# actions = (
#     {"_index": DST_INDEX, "_id": doc["_id"], "_source": doc["_source"]}
#     for doc in scan_docs(src_es, SRC_INDEX)
# )

# helpers.bulk(dst_es, actions)
# print("数据迁移完成。")

# # 3. 设置别名
# if not dst_es.indices.exists_alias(name=DST_ALIAS):
#     dst_es.indices.put_alias(index=DST_INDEX, name=DST_ALIAS)
#     print(f"别名 {DST_ALIAS} 已设置。")
# else:
#     print(f"别名 {DST_ALIAS} 已存在。")


def get_all_ids(es, index) -> set:
    """获取索引下所有文档的_id集合"""
    return set(doc["_id"] for doc in scan(es, index=index, _source=False))


def get_doc_by_id(es, index, doc_id):
    """获取单个文档内容"""
    try:
        return es.get(index=index, id=doc_id)["_source"]
    except Exception:
        return None


def sync_indices(src_es, dst_es, src_index, dst_index):
    src_ids = get_all_ids(src_es, src_index)
    dst_ids = get_all_ids(dst_es, dst_index)

    # 需要新增的
    to_add = src_ids - dst_ids
    # 需要删除的
    to_delete = dst_ids - src_ids
    # 需要比对内容的
    to_update = src_ids & dst_ids

    print(f"新增: {len(to_add)}, 删除: {len(to_delete)}, 检查更新: {len(to_update)}")

    # 新增
    add_actions = (
        {
            "_op_type": "index",
            "_index": dst_index,
            "_id": doc_id,
            "_source": get_doc_by_id(src_es, src_index, doc_id),
        }
        for doc_id in to_add
    )
    bulk(dst_es, add_actions)

    # 删除
    del_actions = (
        {
            "_op_type": "delete",
            "_index": dst_index,
            "_id": doc_id,
        }
        for doc_id in to_delete
    )
    bulk(dst_es, del_actions)

    # 更新
    update_actions = []
    for doc_id in to_update:
        src_doc = get_doc_by_id(src_es, src_index, doc_id)
        dst_doc = get_doc_by_id(dst_es, dst_index, doc_id)
        if src_doc != dst_doc:
            update_actions.append(
                {
                    "_op_type": "index",
                    "_index": dst_index,
                    "_id": doc_id,
                    "_source": src_doc,
                }
            )
    if update_actions:
        bulk(dst_es, update_actions)

    print("同步完成。")


# 用法
sync_indices(src_es, dst_es, SRC_INDEX, DST_INDEX)
