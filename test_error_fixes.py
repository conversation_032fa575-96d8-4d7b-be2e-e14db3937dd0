#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试错误修复是否有效
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import intelligent_merge_analysis


def test_intelligent_merge_with_missing_object_name():
    """测试主体解析结果中object_name为None时的智能融合"""
    print("=" * 80)
    print("测试：主体解析结果中object_name为None时的智能融合")
    print("=" * 80)
    
    # 模拟主体解析结果（object_name为None）
    main_results = [
        {
            "source_id": "test_001",
            "prj_name": "医院设备采购项目",
            "object_name": None,  # 主体解析中没有标的物名称
            "tenderee": "某市人民医院",
            "bid_budget": 1000000.0,
        }
    ]
    
    # 模拟招标文件解析结果（有object_name）
    tender_results = [
        {
            "source_id": "test_001",
            "object_name": "CT设备",
            "object_brand": "西门子",
            "object_model": "SOMATOM CT",
            "object_price": 2500000.0,
        }
    ]
    
    # 模拟合同文件解析结果
    contract_results = []
    
    print("输入数据:")
    print(f"  主体解析结果: object_name = {main_results[0]['object_name']}")
    print(f"  招标文件解析结果: object_name = {tender_results[0]['object_name']}")
    print(f"  合同文件解析结果: {len(contract_results)}个")
    
    try:
        # 执行智能融合分析（不提供文档内容，只测试基础融合）
        print("\n执行智能融合分析...")
        merged_results = intelligent_merge_analysis(
            main_list=main_results,
            tender_list=tender_results,
            contract_list=contract_results
            # 不提供文档内容和模型配置，避免LLM调用
        )
        
        print(f"\n融合结果数量: {len(merged_results)}")
        
        if merged_results:
            result = merged_results[0]
            print(f"\n融合结果:")
            print(f"  项目名称: {result.get('prj_name')}")
            print(f"  招标人: {result.get('tenderee')}")
            print(f"  标的物名称: {result.get('object_name')}")
            print(f"  标的物品牌: {result.get('object_brand')}")
            print(f"  标的物型号: {result.get('object_model')}")
            print(f"  标的物价格: {result.get('object_price')}")
            print(f"  项目预算: {result.get('bid_budget')}")
            
            # 验证融合结果
            # 主体的object_name为None，应该无法与招标文件匹配
            # 但基础融合仍应该成功
            if result.get('prj_name') == "医院设备采购项目":
                print("\n✓ 测试通过：基础融合成功，没有出现变量未定义错误")
                return True
            else:
                print("\n✗ 测试失败：融合结果不正确")
                return False
        else:
            print("\n✗ 测试失败：没有返回融合结果")
            return False
            
    except Exception as e:
        print(f"\n✗ 测试失败：出现异常 - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_intelligent_merge_with_smart_enhancement():
    """测试带有智能补充的融合（模拟场景，不实际调用LLM）"""
    print("\n" + "=" * 80)
    print("测试：带有智能补充的融合逻辑")
    print("=" * 80)
    
    # 模拟主体解析结果
    main_results = [
        {
            "source_id": "test_002",
            "prj_name": "医院设备采购项目",
            "object_name": "MRI设备",
            "tenderee": "某市人民医院",
            "object_brand": None,  # 空缺字段
            "bidder_name": None,   # 空缺字段
        }
    ]
    
    # 模拟招标文件解析结果
    tender_results = [
        {
            "source_id": "test_002",
            "object_name": "MRI设备",
            "object_brand": "飞利浦",
            "object_model": "Ingenia MRI",
        }
    ]
    
    # 模拟合同文件解析结果
    contract_results = [
        {
            "source_id": "test_002",
            "object_name": "MRI设备",
            "bidder_name": "医疗设备有限公司",
            "bidder_price": 3500000.0,
        }
    ]
    
    print("输入数据:")
    print(f"  主体: {main_results[0]['object_name']} (缺少品牌和中标信息)")
    print(f"  招标文件: {tender_results[0]['object_name']} (有品牌信息)")
    print(f"  合同文件: {contract_results[0]['object_name']} (有中标信息)")
    
    try:
        # 执行智能融合分析（不提供文档内容，只使用已有的解析结果）
        print("\n执行智能融合分析...")
        merged_results = intelligent_merge_analysis(
            main_list=main_results,
            tender_list=tender_results,
            contract_list=contract_results
            # 不提供文档内容和模型配置，只使用基础匹配融合
        )
        
        print(f"\n融合结果数量: {len(merged_results)}")
        
        if merged_results:
            result = merged_results[0]
            print(f"\n融合结果:")
            print(f"  项目名称: {result.get('prj_name')}")
            print(f"  招标人: {result.get('tenderee')}")
            print(f"  标的物名称: {result.get('object_name')}")
            print(f"  标的物品牌: {result.get('object_brand')}")
            print(f"  标的物型号: {result.get('object_model')}")
            print(f"  中标单位: {result.get('bidder_name')}")
            print(f"  中标金额: {result.get('bidder_price')}")
            
            # 验证融合结果
            expected_checks = [
                result.get('object_name') == 'MRI设备',
                result.get('object_brand') == '飞利浦',  # 来自招标文件
                result.get('bidder_name') == '医疗设备有限公司',  # 来自合同文件
                result.get('prj_name') == '医院设备采购项目',  # 来自主体
            ]
            
            if all(expected_checks):
                print("\n✓ 测试通过：基于object_name的匹配融合成功")
                return True
            else:
                print("\n✗ 测试失败：融合结果不符合预期")
                print(f"  检查结果: {expected_checks}")
                return False
        else:
            print("\n✗ 测试失败：没有返回融合结果")
            return False
            
    except Exception as e:
        print(f"\n✗ 测试失败：出现异常 - {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("测试错误修复是否有效")
    print("=" * 80)
    
    test_results = []
    
    try:
        test_results.append(test_intelligent_merge_with_missing_object_name())
        test_results.append(test_intelligent_merge_with_smart_enhancement())
        
        print("\n" + "=" * 80)
        print("测试结果总结:")
        print("=" * 80)
        
        test_names = [
            "主体object_name为None的融合",
            "基于object_name的匹配融合"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  测试 {i+1}: {name} - {status}")
        
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print(f"\n总计: {passed_count}/{total_count} 个测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试都通过了！错误修复成功！")
        else:
            print("\n❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
