# update_source_title.py 使用说明

## 功能描述

该脚本用于补充 `markersweb_attachment_analysis_alias` 索引中缺失的 `source_title` 字段。

### 工作原理

1. 查询 `markersweb_attachment_analysis_alias` 索引中 `source_title` 字段缺失的文档
2. 根据文档的 `source_id` 字段匹配 `chn_ylcg` 索引中的 `_id`
3. 将 `chn_ylcg` 索引中的 `title` 字段赋值给 `markersweb_attachment_analysis_alias` 索引的 `source_title` 字段

### 查询语句

脚本使用的查询语句：
```json
{
  "query": {
    "bool": {
      "must_not": {
        "exists": {
          "field": "source_title"
        }
      }
    }
  }
}
```

## 使用方法

### 基本用法

```bash
# 处理一个批次（默认100个文档）
python update_source_title.py

# 指定批处理大小
python update_source_title.py --batch-size 50

# 试运行模式（不执行实际更新）
python update_source_title.py --dry-run

# 处理所有缺失source_title字段的文档
python update_source_title.py --all

# 指定索引名称
python update_source_title.py --index markersweb_attachment_analysis_alias
```

### 参数说明

- `--index`: 目标索引名称（默认从环境变量 `ES_INDEX_ANALYSIS_ALIAS` 获取）
- `--batch-size`: 批处理大小（默认100）
- `--dry-run`: 试运行模式，不执行实际更新
- `--all`: 处理所有缺失source_title字段的文档（分批处理）

### 环境变量配置

脚本需要以下环境变量（在 `.env` 文件中配置）：

```env
ES_HOST=http://**********:9200
ES_USER=elastic
ES_PASSWORD=your_password
ES_INDEX_ANALYSIS_ALIAS=markersweb_attachment_analysis_alias
```

## 执行流程

### 单批次处理模式（默认）

1. 连接到Elasticsearch
2. 查询缺失 `source_title` 字段的文档总数
3. 获取一个批次的文档（默认100个）
4. 对每个文档：
   - 检查 `source_id` 是否为空
   - 从 `chn_ylcg` 索引获取对应的 `title`
   - 准备更新操作
5. 批量更新文档
6. 输出统计信息

### 全量处理模式（--all）

1. 连接到Elasticsearch
2. 查询缺失 `source_title` 字段的文档总数
3. 分批处理所有文档：
   - 每个批次处理指定数量的文档
   - 处理完一个批次后检查剩余数量
   - 继续处理直到所有文档处理完成
4. 输出最终统计信息

## 输出信息

### 统计信息

```
==================================================
处理统计:
  本批次文档总数: 100
  准备更新的文档: 85
  source_id为空的文档: 5
  未找到对应title的文档: 10
==================================================
```

### 日志信息

- 处理进度：`[1/100] 准备更新文档 doc_id: source_title = 'title_content...'`
- 警告信息：`文档 doc_id 的source_id为空，跳过`
- 错误信息：`未找到source_id xxx 对应的title，跳过文档 doc_id`

## 注意事项

1. **试运行模式**：建议首次运行时使用 `--dry-run` 参数查看处理结果
2. **批处理大小**：根据系统性能调整 `--batch-size` 参数
3. **网络连接**：确保能够连接到Elasticsearch集群
4. **权限检查**：确保ES用户有读写相关索引的权限
5. **数据备份**：重要数据建议先备份

## 错误处理

- 连接失败：检查ES配置和网络连接
- 权限错误：检查ES用户权限
- 文档不存在：某些 `source_id` 在 `chn_ylcg` 中可能不存在，这是正常情况
- 批量更新失败：检查ES集群状态和索引健康状况

## 示例输出

```
2025-01-07 10:00:00 - INFO - 正在初始化Elasticsearch客户端...
2025-01-07 10:00:01 - INFO - 目标索引: markersweb_attachment_analysis_alias
2025-01-07 10:00:01 - INFO - 批处理大小: 100
2025-01-07 10:00:01 - INFO - 试运行模式: False
2025-01-07 10:00:01 - INFO - 处理所有文档: True
2025-01-07 10:00:02 - INFO - 索引中缺失source_title字段的文档总数: 1500
2025-01-07 10:00:02 - INFO - 本批次处理 100 个文档（总共 1500 个）
2025-01-07 10:00:05 - INFO - 批量更新完成 - 成功: 85, 失败: 0
2025-01-07 10:00:05 - INFO - 更新完成
```
