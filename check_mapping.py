#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查markersweb_attachment_analysis索引的映射信息
"""

import os
from es_deal import init_es_client
from dotenv import load_dotenv
from utils.log_cfg import log


def check_index_mapping(es_client, index_name: str):
    """
    检查索引的映射信息
    """
    try:
        # 检查索引是否存在
        if not es_client.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            return
        
        # 获取映射信息
        mapping = es_client.indices.get_mapping(index=index_name)
        properties = mapping[index_name]["mappings"].get("properties", {})
        
        log.info(f"索引 {index_name} 的映射信息:")
        log.info("=" * 60)
        
        # 检查release_time字段
        if "release_time" in properties:
            release_time_mapping = properties["release_time"]
            log.info(f"release_time字段:")
            log.info(f"  类型: {release_time_mapping.get('type', 'unknown')}")
            if 'format' in release_time_mapping:
                log.info(f"  格式: {release_time_mapping['format']}")
        else:
            log.warning("release_time字段不存在于当前映射中")
        
        # 显示所有字段的类型
        log.info("\n所有字段类型:")
        for field_name, field_mapping in sorted(properties.items()):
            field_type = field_mapping.get('type', 'unknown')
            log.info(f"  {field_name}: {field_type}")
        
        # 统计字段类型
        type_counts = {}
        for field_mapping in properties.values():
            field_type = field_mapping.get('type', 'unknown')
            type_counts[field_type] = type_counts.get(field_type, 0) + 1
        
        log.info("\n字段类型统计:")
        for field_type, count in sorted(type_counts.items()):
            log.info(f"  {field_type}: {count}个字段")
        
        # 检查文档数量
        doc_count = es_client.count(index=index_name)["count"]
        log.info(f"\n文档总数: {doc_count}")
        
        return properties
        
    except Exception as e:
        log.error(f"检查映射失败: {e}")
        return None


def sample_release_time_values(es_client, index_name: str):
    """
    抽样检查release_time字段的值
    """
    try:
        log.info("\nrelease_time字段值抽样:")
        log.info("=" * 60)
        
        # 获取包含release_time字段的文档样本
        query = {
            "query": {
                "exists": {"field": "release_time"}
            },
            "_source": ["release_time", "prj_name"],
            "size": 10
        }
        
        response = es_client.search(index=index_name, body=query)
        hits = response.get("hits", {}).get("hits", [])
        
        if hits:
            for i, hit in enumerate(hits, 1):
                source = hit["_source"]
                release_time = source.get("release_time", "N/A")
                prj_name = source.get("prj_name", "N/A")[:50]
                log.info(f"  样本{i}: {release_time} | {prj_name}...")
        else:
            log.warning("没有找到包含release_time字段的文档")
        
        # 统计release_time字段的存在情况
        total_docs = es_client.count(index=index_name)["count"]
        with_release_time = es_client.count(
            index=index_name,
            body={"query": {"exists": {"field": "release_time"}}}
        )["count"]
        
        log.info(f"\nrelease_time字段统计:")
        log.info(f"  总文档数: {total_docs}")
        log.info(f"  包含release_time的文档数: {with_release_time}")
        log.info(f"  覆盖率: {with_release_time/total_docs*100:.2f}%")
        
    except Exception as e:
        log.error(f"抽样检查失败: {e}")


def main():
    """
    主函数
    """
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        # 索引名称
        index_name = "markersweb_attachment_analysis"
        
        log.info("检查索引映射信息")
        log.info("=" * 80)
        
        # 检查映射
        mapping = check_index_mapping(es_client, index_name)
        
        if mapping:
            # 抽样检查release_time值
            sample_release_time_values(es_client, index_name)
        
        log.info("=" * 80)
        log.info("检查完成")
        
    except Exception as e:
        log.error(f"检查失败: {e}")
        raise


if __name__ == "__main__":
    main()
