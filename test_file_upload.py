#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件上传功能
"""

import os
import requests
from file_upload_service import FileUploadService, upload_document_file, get_content_type
from utils.log_cfg import log


def test_file_server_api():
    """测试文件服务器API的基本功能"""
    print("=" * 60)
    print("测试1: 文件服务器API基本功能")
    print("=" * 60)
    
    try:
        # 测试获取预签名URL
        test_object_name = "test_file.pdf"
        test_file_size = 1000
        
        url = f"http://172.18.10.70:4003/xinecaiFile/budget/minio-file/put-presign-url?fileSize={test_file_size}&objectName={test_object_name}"
        
        print(f"请求URL: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 获取预签名URL成功")
            return True
        else:
            print("✗ 获取预签名URL失败")
            return False
            
    except Exception as e:
        print(f"✗ API测试失败: {e}")
        return False


def test_file_upload_service():
    """测试文件上传服务类"""
    print("\n" + "=" * 60)
    print("测试2: 文件上传服务类")
    print("=" * 60)
    
    try:
        # 创建测试文件内容
        test_content = b"This is a test file content for upload testing."
        test_object_name = "test_upload.txt"
        
        # 创建上传服务实例
        upload_service = FileUploadService()
        
        print(f"测试文件大小: {len(test_content)} 字节")
        print(f"测试文件名: {test_object_name}")
        
        # 测试获取预签名URL
        presign_result = upload_service.get_presigned_url(test_object_name, len(test_content))
        
        if presign_result:
            print("✓ 获取预签名URL成功")
            print(f"  上传URL: {presign_result['upload_url'][:100]}...")
            print(f"  上传ID: {presign_result['upload_id']}")
            return True
        else:
            print("✗ 获取预签名URL失败")
            return False
            
    except Exception as e:
        print(f"✗ 文件上传服务测试失败: {e}")
        return False


def test_content_type_mapping():
    """测试MIME类型映射"""
    print("\n" + "=" * 60)
    print("测试3: MIME类型映射")
    print("=" * 60)
    
    test_extensions = [".pdf", ".doc", ".docx", ".zip", ".rar", ".7z", ".txt", ".unknown"]
    
    for ext in test_extensions:
        content_type = get_content_type(ext)
        print(f"  {ext} -> {content_type}")
    
    print("✓ MIME类型映射测试完成")
    return True


def test_document_upload_function():
    """测试文档上传便捷函数"""
    print("\n" + "=" * 60)
    print("测试4: 文档上传便捷函数")
    print("=" * 60)
    
    try:
        # 创建测试文件内容
        test_content = b"Test document content for bidding file upload."
        source_id = "TEST_DOC_001"
        file_type = "招标文件"
        file_ext = ".txt"
        
        print(f"测试参数:")
        print(f"  文件内容大小: {len(test_content)} 字节")
        print(f"  源文档ID: {source_id}")
        print(f"  文件类型: {file_type}")
        print(f"  文件扩展名: {file_ext}")
        
        expected_object_name = f"{file_type}_{source_id}{file_ext}"
        print(f"  期望的对象名: {expected_object_name}")
        
        # 注意：这里只测试函数调用，不实际上传
        print("\n注意：实际上传测试需要确保文件服务器可用")
        print("如果要进行实际上传测试，请取消下面的注释：")
        print(f"# result = upload_document_file(test_content, source_id, file_type, file_ext)")
        
        print("✓ 文档上传函数接口测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 文档上传函数测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理机制"""
    print("\n" + "=" * 60)
    print("测试5: 错误处理机制")
    print("=" * 60)
    
    try:
        # 测试无效的服务器地址
        invalid_service = FileUploadService(base_url="http://invalid-server:9999")
        
        result = invalid_service.get_presigned_url("test.txt", 100)
        
        if result is None:
            print("✓ 无效服务器地址错误处理正确")
        else:
            print("✗ 错误处理异常")
            
        # 测试空文件内容
        try:
            upload_document_file(b"", "test_id", "招标文件", ".txt")
            print("✓ 空文件内容处理正常")
        except Exception as e:
            print(f"✓ 空文件内容错误处理: {e}")
        
        print("✓ 错误处理机制测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("开始测试文件上传功能...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("API基本功能", test_file_server_api()))
    test_results.append(("文件上传服务类", test_file_upload_service()))
    test_results.append(("MIME类型映射", test_content_type_mapping()))
    test_results.append(("文档上传便捷函数", test_document_upload_function()))
    test_results.append(("错误处理机制", test_error_handling()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件上传功能准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查文件服务器连接和配置。")
    
    return passed == total


if __name__ == "__main__":
    main()
