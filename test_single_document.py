#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试单个文档的JSON解析
"""

import os
import json
from dotenv import load_dotenv
from es_deal import init_es_client
from analyse_appendix import DocumentAnalyzer

def test_single_document():
    """测试单个文档的处理"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        # 获取环境变量
        es_index_links = os.getenv("ES_INDEX_LINKS", "chn_ylcg")
        es_index_analysis = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
        model_apikey = os.getenv("MODEL_APIKEY")
        model_name = os.getenv("MODEL_NAME", "Qwen/Qwen2.5-32B-Instruct")
        model_url = os.getenv("MODEL_URL", "https://api-inference.modelscope.cn/v1")
        prompt_spec = os.getenv("PROMPT_SPEC", "")
        
        # 创建分析器实例
        analyzer = DocumentAnalyzer(
            es_client=es_client,
            es_index_links=es_index_links,
            es_index_analysis=es_index_analysis,
            model_apikey=model_apikey,
            model_name=model_name,
            model_url=model_url,
            prompt_spec=prompt_spec,
        )
        
        # 获取一个测试文档
        query = {
            "query": {"match_all": {}},
            "size": 1
        }
        
        response = es_client.search(
            index=es_index_links,
            body=query
        )
        
        hits = response.get("hits", {}).get("hits", [])
        if not hits:
            print("没有找到测试文档")
            return
        
        doc = hits[0]
        print(f"测试文档ID: {doc['_id']}")
        print(f"文档标题: {doc['_source'].get('title', 'N/A')}")
        
        # 分析文档
        print("开始分析文档...")
        results = analyzer.analyze_document(doc)
        
        print(f"分析结果类型: {type(results)}")
        print(f"结果数量: {len(results) if isinstance(results, list) else 1}")
        
        if results:
            print("分析成功！")
            if isinstance(results, list):
                for i, result in enumerate(results):
                    print(f"结果 {i+1}: {type(result)}")
                    if isinstance(result, dict):
                        print(f"  字段数: {len(result)}")
                        print(f"  object_name: {result.get('object_name', 'N/A')}")
            else:
                print(f"单个结果: {type(results)}")
        else:
            print("分析结果为空")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_document()
