#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试pdfplumber PDF解析功能
"""

import io
from analyse_appendix import parse_pdf, extract_preview_text

def create_test_pdf():
    """
    创建一个简单的测试PDF
    """
    try:
        # 使用reportlab创建测试PDF
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 创建内存中的PDF
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        
        # 添加文本内容
        p.drawString(100, 750, "Test PDF Document")
        p.drawString(100, 730, "This is a test PDF created for testing pdfplumber")
        p.drawString(100, 710, "包含中文内容测试")
        p.drawString(100, 690, "Line 4: More test content")
        p.drawString(100, 670, "Line 5: Additional information")
        
        # 添加第二页
        p.showPage()
        p.drawString(100, 750, "Page 2 Content")
        p.drawString(100, 730, "Second page text")
        p.drawString(100, 710, "第二页中文内容")
        
        # 保存PDF
        p.save()
        
        # 获取PDF字节内容
        pdf_bytes = buffer.getvalue()
        buffer.close()
        
        return pdf_bytes
        
    except ImportError:
        print("reportlab not available, creating minimal PDF")
        # 创建一个最小的PDF
        minimal_pdf = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
        return minimal_pdf

def test_pdf_parsing():
    """
    测试PDF解析功能
    """
    print("=" * 60)
    print("测试pdfplumber PDF解析功能")
    print("=" * 60)
    
    # 创建测试PDF
    print("1. 创建测试PDF...")
    pdf_bytes = create_test_pdf()
    print(f"   PDF大小: {len(pdf_bytes)} 字节")
    
    # 测试parse_pdf函数
    print("\n2. 测试parse_pdf函数...")
    try:
        extracted_text = parse_pdf(pdf_bytes)
        print(f"   提取成功！")
        print(f"   提取的文本长度: {len(extracted_text)} 字符")
        if extracted_text.strip():
            print(f"   文本内容预览: {extracted_text[:200]}...")
        else:
            print("   提取的文本为空")
    except Exception as e:
        print(f"   提取失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试extract_preview_text函数
    print("\n3. 测试extract_preview_text函数...")
    try:
        preview_text = extract_preview_text(".pdf", pdf_bytes)
        print(f"   预览提取成功！")
        print(f"   预览文本长度: {len(preview_text)} 字符")
        if preview_text.strip():
            print(f"   预览内容: {preview_text[:200]}...")
        else:
            print("   预览文本为空")
    except Exception as e:
        print(f"   预览提取失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试空PDF
    print("\n4. 测试空PDF...")
    empty_result = parse_pdf(b"")
    print(f"   空PDF结果: '{empty_result}'")
    
    # 测试无效PDF
    print("\n5. 测试无效PDF...")
    invalid_result = parse_pdf(b"not a pdf")
    print(f"   无效PDF结果: '{invalid_result}'")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    test_pdf_parsing()
