#!/usr/bin/env python3
"""
Test script to verify the fix for the NoneType error in find_matching_by_object_name function
"""

from typing import List, Dict


def find_matching_by_object_name(
    target_object_name: str, source_list: List[dict]
) -> dict:
    """
    根据object_name在源列表中查找匹配的记录

    Args:
        target_object_name: 目标标的物名称
        source_list: 源记录列表

    Returns:
        dict: 匹配的记录，如果没找到则返回空字典
    """
    # 确保target_object_name不是None或空字符串，source_list不为空
    if not target_object_name or target_object_name is None or not source_list:
        return {}

    # 精确匹配
    for item in source_list:
        if item.get("object_name") == target_object_name:
            return item

    # 如果精确匹配失败，尝试模糊匹配（包含关系和关键词匹配）
    target_name_lower = target_object_name.lower().strip()

    # Test the problematic line that was causing the error
    for item in source_list:
        item_name = item.get("object_name") or ""
        item_name = item_name.lower().strip()
        if item_name:
            print(f"Checking item_name: {item_name}")

    print(f"Testing with target_name_lower: {target_name_lower}")
    return {}


def test_function():
    """Test the function with various inputs including None"""

    # Test data - including the problematic case where object_name is None
    source_list = [
        {"object_name": "测试设备", "other_field": "value1"},
        {"object_name": "医疗器械", "other_field": "value2"},
        {"object_name": None, "other_field": "value3"},  # This was causing the error
        {"other_field": "value4"},  # No object_name field
    ]

    # Test cases
    test_cases = [
        None,  # This should not cause an error
        "",  # Empty string
        "测试设备",  # Valid match
        "不存在的设备",  # No match
    ]

    print("Testing find_matching_by_object_name function:")
    print("=" * 50)

    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}: target_object_name = {repr(test_case)}")
        try:
            result = find_matching_by_object_name(test_case, source_list)
            print(f"Result: {result}")
            print("✓ Success - No error occurred")
        except Exception as e:
            print(f"✗ Error: {e}")

    print("\n" + "=" * 50)
    print("All tests completed!")


if __name__ == "__main__":
    test_function()
