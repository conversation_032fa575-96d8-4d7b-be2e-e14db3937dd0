#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断markersweb_attachment_analysis_alias和chn_ylcg索引之间的映射关系
"""

import os
from typing import Dict, List, Set
from es_deal import init_es_client
from dotenv import load_dotenv
from utils.log_cfg import log


def analyze_source_ids(es_client):
    """
    分析两个索引中的ID分布情况
    """
    try:
        log.info("开始分析索引映射关系...")
        
        # 1. 获取markersweb_attachment_analysis_alias中的source_id样本
        analysis_query = {
            "query": {"match_all": {}},
            "_source": ["source_id"],
            "size": 10
        }
        
        analysis_response = es_client.search(
            index="markersweb_attachment_analysis_alias",
            body=analysis_query
        )
        
        log.info("markersweb_attachment_analysis_alias索引中的source_id样本:")
        analysis_source_ids = []
        for hit in analysis_response.get("hits", {}).get("hits", []):
            source_id = hit["_source"].get("source_id")
            if source_id:
                analysis_source_ids.append(source_id)
                log.info(f"  - {source_id}")
        
        # 2. 获取chn_ylcg中的_id样本
        chn_query = {
            "query": {"match_all": {}},
            "_source": False,  # 只要_id
            "size": 10
        }
        
        chn_response = es_client.search(
            index="chn_ylcg",
            body=chn_query
        )
        
        log.info("chn_ylcg索引中的_id样本:")
        chn_ids = []
        for hit in chn_response.get("hits", {}).get("hits", []):
            doc_id = hit["_id"]
            chn_ids.append(doc_id)
            log.info(f"  - {doc_id}")
        
        # 3. 检查是否有匹配的ID
        log.info("检查ID匹配情况:")
        matches = set(analysis_source_ids) & set(chn_ids)
        if matches:
            log.info(f"找到匹配的ID: {matches}")
        else:
            log.warning("没有找到匹配的ID")
        
        # 4. 分析ID格式差异
        log.info("ID格式分析:")
        if analysis_source_ids:
            log.info(f"analysis索引source_id格式示例: {analysis_source_ids[0]} (长度: {len(analysis_source_ids[0])})")
        if chn_ids:
            log.info(f"chn_ylcg索引_id格式示例: {chn_ids[0]} (长度: {len(chn_ids[0])})")
        
        # 5. 统计总数
        analysis_total = es_client.count(index="markersweb_attachment_analysis_alias")["count"]
        chn_total = es_client.count(index="chn_ylcg")["count"]
        
        log.info(f"markersweb_attachment_analysis_alias总文档数: {analysis_total}")
        log.info(f"chn_ylcg总文档数: {chn_total}")
        
        return analysis_source_ids, chn_ids
        
    except Exception as e:
        log.error(f"分析失败: {e}")
        raise


def check_specific_mapping(es_client, source_ids: List[str]):
    """
    检查特定source_id在chn_ylcg中是否存在
    """
    try:
        log.info("检查特定source_id映射...")
        
        for source_id in source_ids[:5]:  # 只检查前5个
            try:
                response = es_client.get(
                    index="chn_ylcg",
                    id=source_id,
                    _source=False
                )
                log.info(f"✓ source_id {source_id} 在chn_ylcg中存在")
            except Exception as e:
                if "not_found" in str(e).lower():
                    log.warning(f"✗ source_id {source_id} 在chn_ylcg中不存在")
                else:
                    log.error(f"✗ 检查source_id {source_id} 时出错: {e}")
                    
    except Exception as e:
        log.error(f"检查映射失败: {e}")


def find_potential_matches(es_client):
    """
    尝试找到可能的匹配模式
    """
    try:
        log.info("寻找潜在的匹配模式...")
        
        # 获取更多样本进行分析
        analysis_query = {
            "query": {"match_all": {}},
            "_source": ["source_id", "source_title", "source_url"],
            "size": 20
        }
        
        analysis_response = es_client.search(
            index="markersweb_attachment_analysis_alias",
            body=analysis_query
        )
        
        # 分析source_id的模式
        source_ids = []
        for hit in analysis_response.get("hits", {}).get("hits", []):
            source_data = hit["_source"]
            source_id = source_data.get("source_id")
            title = source_data.get("source_title", "")
            url = source_data.get("source_url", "")
            
            if source_id:
                source_ids.append(source_id)
                log.info(f"source_id: {source_id}")
                log.info(f"  title: {title[:100]}...")
                log.info(f"  url: {url}")
                log.info("---")
        
        # 尝试在chn_ylcg中搜索相似的标题
        if source_ids:
            sample_id = source_ids[0]
            log.info(f"尝试在chn_ylcg中搜索包含'{sample_id}'的文档...")
            
            search_query = {
                "query": {
                    "bool": {
                        "should": [
                            {"wildcard": {"_id": f"*{sample_id}*"}},
                            {"wildcard": {"_id": f"{sample_id}*"}},
                            {"wildcard": {"_id": f"*{sample_id}"}}
                        ]
                    }
                },
                "size": 5
            }
            
            try:
                search_response = es_client.search(
                    index="chn_ylcg",
                    body=search_query
                )
                
                hits = search_response.get("hits", {}).get("hits", [])
                if hits:
                    log.info("在chn_ylcg中找到可能相关的文档:")
                    for hit in hits:
                        log.info(f"  _id: {hit['_id']}")
                else:
                    log.warning("未找到相关文档")
                    
            except Exception as e:
                log.error(f"搜索失败: {e}")
                
    except Exception as e:
        log.error(f"寻找匹配模式失败: {e}")


def main():
    """
    主函数
    """
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        log.info("=" * 80)
        log.info("开始诊断索引映射关系")
        log.info("=" * 80)
        
        # 分析source_id分布
        analysis_source_ids, chn_ids = analyze_source_ids(es_client)
        
        log.info("=" * 80)
        
        # 检查特定映射
        if analysis_source_ids:
            check_specific_mapping(es_client, analysis_source_ids)
        
        log.info("=" * 80)
        
        # 寻找潜在匹配
        find_potential_matches(es_client)
        
        log.info("=" * 80)
        log.info("诊断完成")
        
    except Exception as e:
        log.error(f"诊断失败: {e}")
        raise


if __name__ == "__main__":
    main()
