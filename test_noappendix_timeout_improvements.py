#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试analyse_noappendix.py的超时改进功能
验证超时时间增加、错误处理改进、内容长度限制等功能
"""

import time
from unittest.mock import Mock, patch
from analyse_noappendix import DocumentAnalyzer
from utils.log_cfg import log
import inspect


def test_timeout_settings():
    """测试超时时间设置"""
    print("=" * 60)
    print("测试1: 超时时间设置")
    print("=" * 60)
    
    try:
        # 创建DocumentAnalyzer实例，检查默认超时时间
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        print(f"DocumentAnalyzer默认超时时间: {analyzer.timeout}秒")
        assert analyzer.timeout == 300, f"期望300秒，实际{analyzer.timeout}秒"
        print("✓ DocumentAnalyzer超时时间设置正确")
        
        # 测试llm函数的默认超时时间
        from analyse_noappendix import llm
        sig = inspect.signature(llm)
        timeout_param = sig.parameters['timeout']
        default_timeout = timeout_param.default
        
        print(f"llm函数默认超时时间: {default_timeout}秒")
        assert default_timeout == 300, f"期望300秒，实际{default_timeout}秒"
        print("✓ llm函数超时时间设置正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 超时时间设置测试失败: {e}")
        return False


def test_content_length_limit():
    """测试内容长度限制"""
    print("\n" + "=" * 60)
    print("测试2: 内容长度限制")
    print("=" * 60)
    
    try:
        # 创建超长内容的模拟文档
        long_html_content = "<p>这是一个很长的HTML文档内容。</p>" * 10000  # 约50万字符
        
        mock_doc = {
            "_id": "test_doc_001",
            "_source": {
                "title": "测试文档标题",
                "response": long_html_content,
                "url": "http://test.com",
                "category": "001",
                "appendix": None
            }
        }
        
        print(f"原始HTML内容长度: {len(long_html_content)}字符")
        
        # 创建DocumentAnalyzer实例
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        # 模拟LLM调用，避免实际调用
        with patch('analyse_noappendix.llm') as mock_llm:
            mock_llm.return_value = '[]'  # 返回空的JSON数组
            
            try:
                result = analyzer.analyze_document(mock_doc)
                print("✓ analyze_document内容长度限制测试通过")
                return True
            except Exception as e:
                print(f"✗ analyze_document内容长度限制测试失败: {e}")
                return False
        
    except Exception as e:
        print(f"✗ 内容长度限制测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理改进"""
    print("\n" + "=" * 60)
    print("测试3: 错误处理改进")
    print("=" * 60)
    
    try:
        # 模拟LLM超时错误
        def mock_llm_timeout(*args, **kwargs):
            raise Exception("Request timed out.")
        
        # 创建测试文档
        mock_doc = {
            "_id": "test_doc_002",
            "_source": {
                "title": "测试文档标题",
                "response": "<p>测试内容</p>",
                "url": "http://test.com",
                "category": "001",
                "appendix": None
            }
        }
        
        # 创建DocumentAnalyzer实例
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        # 测试analyze_document的错误处理
        with patch('analyse_noappendix.llm', side_effect=mock_llm_timeout):
            try:
                result = analyzer.analyze_document(mock_doc)
                print("✗ analyze_document应该抛出异常")
                return False
            except Exception as e:
                if "Request timed out" in str(e):
                    print("✓ analyze_document正确抛出超时异常")
                else:
                    print(f"✗ analyze_document抛出了意外异常: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def test_process_error_handling():
    """测试process_one_record的错误处理"""
    print("\n" + "=" * 60)
    print("测试4: process_one_record错误处理")
    print("=" * 60)
    
    try:
        # 创建DocumentAnalyzer实例
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        # 模拟ES查询返回结果
        mock_search_result = {
            "hits": {
                "hits": [
                    {
                        "_id": "test_doc_003",
                        "_source": {
                            "title": "测试文档标题",
                            "response": "<p>测试内容</p>",
                            "url": "http://test.com",
                            "category": "001",
                            "appendix": None  # 无附件，会调用analyze_document
                        }
                    }
                ]
            }
        }
        
        # 模拟analyze_document抛出异常
        def mock_analyze_error(*args, **kwargs):
            raise Exception("LLM API调用失败: Request timed out.")
        
        with patch('analyse_noappendix.search_documents', return_value=mock_search_result):
            with patch.object(analyzer, 'get_processed_ids', return_value=[]):
                with patch.object(analyzer, 'analyze_document', side_effect=mock_analyze_error):
                    
                    # 这应该不会抛出异常，而是优雅地处理错误
                    try:
                        analyzer.process_one_record()
                        print("✓ process_one_record错误处理正确")
                        return True
                    except Exception as e:
                        print(f"✗ process_one_record应该优雅处理错误，但抛出了: {e}")
                        return False
        
    except Exception as e:
        print(f"✗ process_one_record错误处理测试失败: {e}")
        return False


def test_timeout_simulation():
    """模拟超时情况的处理"""
    print("\n" + "=" * 60)
    print("测试5: 超时情况模拟")
    print("=" * 60)
    
    try:
        # 模拟慢速LLM响应
        def mock_slow_llm(*args, **kwargs):
            print("  模拟LLM处理中...")
            time.sleep(1)  # 模拟1秒延迟
            return '[{"prj_name": "测试项目", "source_id": "test_001"}]'
        
        # 创建测试文档
        mock_doc = {
            "_id": "test_doc_004",
            "_source": {
                "title": "测试文档标题",
                "response": "<p>测试内容</p>",
                "url": "http://test.com",
                "category": "001",
                "appendix": None
            }
        }
        
        # 创建DocumentAnalyzer实例
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        start_time = time.time()
        
        with patch('analyse_noappendix.llm', side_effect=mock_slow_llm):
            result = analyzer.analyze_document(mock_doc)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"  处理耗时: {elapsed:.2f}秒")
        print(f"  解析结果数量: {len(result)}")
        
        if len(result) > 0 and result[0].get("prj_name") == "测试项目":
            print("✓ 超时情况模拟测试通过")
            return True
        else:
            print("✗ 超时情况模拟测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 超时情况模拟测试失败: {e}")
        return False


def main():
    """运行所有超时改进测试"""
    print("开始测试analyse_noappendix.py的超时改进功能...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("超时时间设置", test_timeout_settings()))
    test_results.append(("内容长度限制", test_content_length_limit()))
    test_results.append(("错误处理改进", test_error_handling()))
    test_results.append(("process_one_record错误处理", test_process_error_handling()))
    test_results.append(("超时情况模拟", test_timeout_simulation()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("analyse_noappendix.py超时改进测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 analyse_noappendix.py所有超时改进测试通过！")
        print("\n改进效果:")
        print("1. ✓ 超时时间从60秒增加到300秒（5分钟）")
        print("2. ✓ 添加了内容长度限制，避免过长文档")
        print("3. ✓ 改进了错误处理，LLM失败不会中断整个流程")
        print("4. ✓ process_one_record优雅处理错误")
        print("5. ✓ 与analyse_appendix.py保持一致的改进")
    else:
        print("⚠️  部分超时改进测试失败，请检查实现。")
    
    return passed == total


if __name__ == "__main__":
    main()
