#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量LLM调用优化效果
"""

import json
import time
from unittest.mock import Mock, patch
from analyse_appendix import (
    intelligent_merge_analysis,
    batch_extract_fields_from_content,
    identify_missing_fields,
    STANDARD_FIELDS
)


def create_test_data():
    """创建测试数据"""
    # 主体解析结果（包含多个标的物，但缺少很多字段）
    main_results = [
        {
            "prj_name": "医院试剂采购项目",
            "object_name": "苯丙氨酸测定试剂盒（荧光分析法）",
            "tenderee": "某市人民医院",
            "announcement_type": "004",
            # 其他字段为空，需要从文档中提取
        },
        {
            "prj_name": "医院试剂采购项目", 
            "object_name": "新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）",
            "tenderee": "某市人民医院",
            "announcement_type": "004",
            # 其他字段为空，需要从文档中提取
        },
        {
            "prj_name": "医院试剂采购项目",
            "object_name": "新生儿17α-羟孕酮测定试剂盒（时间分辨荧光免疫分析法）", 
            "tenderee": "某市人民医院",
            "announcement_type": "004",
            # 其他字段为空，需要从文档中提取
        }
    ]
    
    # 填充所有标准字段为None（模拟空缺字段）
    for result in main_results:
        for field in STANDARD_FIELDS:
            if field not in result:
                result[field] = None
    
    # 招标文件内容（模拟）
    tender_content = """
    招标文件
    
    项目名称：医院试剂采购项目
    招标人：某市人民医院
    
    标的物清单：
    1. 苯丙氨酸测定试剂盒（荧光分析法）
       - 型号：960人份/盒
       - 配置参数：荧光分析法测定新生儿足跟血滤纸干血片样品中PKU的含量
       - 单价：5.0元
       
    2. 新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）
       - 型号：960人份/盒
       - 配置参数：时间分辨荧光免疫分析法测定新生儿足跟血滤纸干血片样品中TSH的含量
       - 单价：6.0元
       
    3. 新生儿17α-羟孕酮测定试剂盒（时间分辨荧光免疫分析法）
       - 型号：960人份/盒
       - 配置参数：时间分辨荧光免疫分析法测定新生儿足跟血滤纸干血片样品中17α-羟孕酮的含量
       - 单价：7.0元
    """
    
    return main_results, tender_content


def mock_llm_response(*args, **kwargs):
    """模拟LLM响应"""
    # 模拟批量提取的响应
    return json.dumps([
        {
            "object_name": "苯丙氨酸测定试剂盒（荧光分析法）",
            "object_model": "960人份/盒",
            "object_conf": "荧光分析法测定新生儿足跟血滤纸干血片样品中PKU的含量",
            "object_price": 5.0
        },
        {
            "object_name": "新生儿促甲状腺激素测定试剂盒（时间分辨荧光免疫分析法）",
            "object_model": "960人份/盒", 
            "object_conf": "时间分辨荧光免疫分析法测定新生儿足跟血滤纸干血片样品中TSH的含量",
            "object_price": 6.0
        },
        {
            "object_name": "新生儿17α-羟孕酮测定试剂盒（时间分辨荧光免疫分析法）",
            "object_model": "960人份/盒",
            "object_conf": "时间分辨荧光免疫分析法测定新生儿足跟血滤纸干血片样品中17α-羟孕酮的含量", 
            "object_price": 7.0
        }
    ], ensure_ascii=False)


def test_batch_extraction():
    """测试批量字段提取函数"""
    print("=== 测试批量字段提取函数 ===")
    
    main_results, tender_content = create_test_data()
    
    # 构建提取请求
    extraction_requests = []
    for i, result in enumerate(main_results):
        missing_fields = identify_missing_fields(result)
        # 只测试部分字段
        test_fields = ['object_model', 'object_conf', 'object_price']
        test_missing_fields = [f for f in test_fields if f in missing_fields]
        
        if test_missing_fields:
            extraction_requests.append({
                'object_name': result['object_name'],
                'missing_fields': test_missing_fields,
                'result_index': i
            })
    
    print(f"提取请求数量: {len(extraction_requests)}")
    for i, req in enumerate(extraction_requests):
        print(f"  请求{i+1}: {req['object_name']} -> {req['missing_fields']}")
    
    # 模拟批量提取
    with patch('analyse_appendix.llm', side_effect=mock_llm_response):
        start_time = time.time()
        batch_results = batch_extract_fields_from_content(
            tender_content,
            extraction_requests,
            model_apikey="test_key",
            model_name="test_model", 
            model_url="test_url"
        )
        end_time = time.time()
    
    print(f"\n批量提取耗时: {end_time - start_time:.3f}秒")
    print(f"批量提取结果数量: {len(batch_results)}")
    
    for i, result in enumerate(batch_results):
        print(f"  结果{i+1}: {result}")
    
    return batch_results


def test_intelligent_merge_optimization():
    """测试智能融合分析的LLM调用优化"""
    print("\n=== 测试智能融合分析LLM调用优化 ===")
    
    main_results, tender_content = create_test_data()
    
    # 统计LLM调用次数
    llm_call_count = 0
    
    def counting_llm_mock(*args, **kwargs):
        nonlocal llm_call_count
        llm_call_count += 1
        print(f"  LLM调用 #{llm_call_count}")
        return mock_llm_response(*args, **kwargs)
    
    # 测试优化后的智能融合分析
    with patch('analyse_appendix.llm', side_effect=counting_llm_mock):
        start_time = time.time()
        
        merged_results = intelligent_merge_analysis(
            main_list=main_results,
            tender_content=tender_content,
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url"
        )
        
        end_time = time.time()
    
    print(f"\n优化后总LLM调用次数: {llm_call_count}")
    print(f"处理耗时: {end_time - start_time:.3f}秒")
    print(f"融合结果数量: {len(merged_results)}")
    
    # 验证结果
    for i, result in enumerate(merged_results):
        print(f"\n融合结果 {i+1}:")
        print(f"  object_name: {result.get('object_name')}")
        print(f"  object_model: {result.get('object_model')}")
        print(f"  object_conf: {result.get('object_conf')}")
        print(f"  object_price: {result.get('object_price')}")
    
    return merged_results, llm_call_count


def test_efficiency_comparison():
    """对比优化前后的效率"""
    print("\n=== 效率对比分析 ===")
    
    main_results, tender_content = create_test_data()
    
    # 模拟优化前的调用次数（每个主体一次LLM调用）
    expected_old_calls = len(main_results)  # 3次调用
    
    # 测试优化后的调用次数
    _, actual_new_calls = test_intelligent_merge_optimization()
    
    print(f"\n效率对比:")
    print(f"  优化前预期LLM调用次数: {expected_old_calls}")
    print(f"  优化后实际LLM调用次数: {actual_new_calls}")
    print(f"  效率提升: {((expected_old_calls - actual_new_calls) / expected_old_calls * 100):.1f}%")
    
    if actual_new_calls <= 1:
        print("✅ 优化成功！LLM调用次数大幅减少")
    else:
        print("❌ 优化效果不明显，需要进一步检查")


if __name__ == "__main__":
    print("开始测试批量LLM调用优化...")
    
    # 测试批量提取函数
    test_batch_extraction()
    
    # 测试智能融合分析优化
    test_intelligent_merge_optimization()
    
    # 效率对比
    test_efficiency_comparison()
    
    print("\n测试完成！")
