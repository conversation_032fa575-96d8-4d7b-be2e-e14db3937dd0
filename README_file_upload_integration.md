# 文件上传功能集成说明

## 概述

文件上传功能已成功集成到文档解析系统中，当系统识别到招标文件或合同文件时，会自动将文件上传到MinIO文件服务器。

## 功能特点

### 1. 自动文件上传
- 在文档处理过程中自动识别招标文件和合同文件
- 自动上传到MinIO文件服务器
- 支持多种文件格式（PDF、DOC、DOCX等）

### 2. 智能文件命名
- **招标文件命名规则**：`招标文件_{source_id}{file_ext}`
- **合同文件命名规则**：`合同文件_{source_id}{file_ext}`
- 其中`source_id`是文档的唯一标识符，`file_ext`是文件扩展名

### 3. 可配置的上传控制
- 支持通过环境变量或参数控制是否启用文件上传
- 默认启用文件上传功能
- 可以在运行时动态控制

### 4. 完善的错误处理
- 上传失败不影响主要的文档解析流程
- 支持重试机制（最多3次重试）
- 详细的日志记录

## 集成架构

```
文档解析流程
    ↓
附件处理
    ↓
文件类型检测
    ↓
文件内容存储（智能融合用）
    ↓
文件上传（新增）
    ↓
文档解析和分析
    ↓
智能融合
    ↓
结果存储
```

## 配置方法

### 1. 环境变量配置

在`.env`文件中添加：

```bash
# 文件上传功能开关（可选，默认为true）
ENABLE_FILE_UPLOAD=true

# 文件服务器配置（在file_upload_service.py中配置）
FILE_SERVER_BASE_URL=http://172.18.10.70:4003
FILE_SERVER_BASE_URL=http://172.18.7.27:4003
```

### 2. 代码配置

```python
from analyse_appendix import DocumentAnalyzer

# 创建分析器实例，启用文件上传
analyzer = DocumentAnalyzer(
    es_client=es_client,
    es_index_links="links_index",
    es_index_analysis="analysis_index",
    model_apikey="your_api_key",
    model_name="your_model_name",
    model_url="your_model_url",
    prompt_spec="your_prompt",
    enable_file_upload=True  # 启用文件上传
)

# 处理记录
analyzer.process_one_record()
```

## 文件上传流程

### 1. 文件识别
系统使用`detect_file_type()`函数识别文件类型：
- 招标文件：包含"招标"、"采购"等关键词
- 合同文件：包含"合同"、"协议"等关键词
- 其他文件：不进行上传

### 2. 上传触发
当识别到招标文件或合同文件时：
```python
if file_type in ["招标文件", "合同文件"] and self.enable_file_upload:
    # 执行文件上传
    upload_success = upload_document_file(
        file_content=file_bytes,
        source_id=source_id,
        file_type=file_type,
        file_ext=file_ext,
    )
```

### 3. 上传过程
1. **获取预签名URL**：从文件服务器获取上传URL
2. **上传文件内容**：将文件内容上传到MinIO
3. **完成上传**：通知服务器上传完成

### 4. 错误处理
- 上传失败时记录警告日志，但不中断主流程
- 支持自动重试机制
- 网络异常时优雅降级

## 使用示例

### 1. 基本使用

```python
from analyse_appendix import get_one_record

# 使用默认配置（从环境变量读取）
get_one_record()
```

### 2. 自定义配置

```python
from analyse_appendix import DocumentAnalyzer
from es_deal import init_es_client

# 初始化ES客户端
es = init_es_client()

# 创建分析器，禁用文件上传
analyzer = DocumentAnalyzer(
    es_client=es,
    es_index_links="your_links_index",
    es_index_analysis="your_analysis_index",
    model_apikey="your_api_key",
    model_name="your_model_name",
    model_url="your_model_url",
    prompt_spec="your_prompt",
    enable_file_upload=False  # 禁用文件上传
)

# 处理记录
analyzer.process_one_record()
```

### 3. 单独使用文件上传功能

```python
from file_upload_service import upload_document_file

# 直接上传文件
success = upload_document_file(
    file_content=file_bytes,
    source_id="DOC_001",
    file_type="招标文件",
    file_ext=".pdf"
)

if success:
    print("文件上传成功")
else:
    print("文件上传失败")
```

## 日志输出示例

```
2025-07-02 12:29:16 | INFO | 此文件是招标文件: 招标文件.pdf
2025-07-02 12:29:16 | INFO | 存储招标文件内容: 招标文件.pdf
2025-07-02 12:29:16 | INFO | 开始上传招标文件: 招标文件.pdf
2025-07-02 12:29:16 | INFO | 开始上传文件: 招标文件_DOC001.pdf, 大小: 1024 字节
2025-07-02 12:29:16 | INFO | 获取预签名URL成功
2025-07-02 12:29:17 | INFO | 文件内容上传成功
2025-07-02 12:29:17 | INFO | 文件上传完成
2025-07-02 12:29:17 | INFO | ✓ 招标文件上传成功: 招标文件_DOC001.pdf
```

## 测试验证

### 1. 运行基础测试
```bash
python test_file_upload.py
```

### 2. 运行完整上传测试
```bash
python test_complete_upload.py
```

### 3. 运行集成测试
```bash
python test_integration_upload.py
```

## 性能考虑

### 1. 上传优化
- 使用预签名URL直接上传到MinIO，减少服务器负载
- 支持并发上传（如果有多个文件）
- 自动重试机制提高成功率

### 2. 错误恢复
- 上传失败不影响文档解析主流程
- 详细的错误日志便于问题排查
- 支持手动重新上传

### 3. 资源管理
- 及时释放文件内容内存
- 合理的超时设置
- 连接池复用

## 注意事项

1. **网络依赖**：文件上传功能依赖网络连接，确保服务器可达
2. **存储空间**：注意MinIO存储空间的使用情况
3. **权限配置**：确保有MinIO的上传权限
4. **文件大小**：大文件上传可能需要更长时间，注意超时设置
5. **并发控制**：高并发场景下注意文件服务器的负载

## 故障排除

### 1. 上传失败
- 检查网络连接
- 验证文件服务器地址和端口
- 检查MinIO权限配置

### 2. 文件命名冲突
- 系统使用source_id确保文件名唯一性
- 如有冲突，新文件会覆盖旧文件

### 3. 性能问题
- 检查文件大小和网络带宽
- 考虑调整重试次数和超时时间
- 监控MinIO服务器性能

## 更新日志

- **v1.0**：实现基础文件上传功能
- 支持招标文件和合同文件自动上传
- 集成到文档解析流程
- 完善的错误处理和日志记录
- 可配置的上传控制
- 完整的测试覆盖
