# 附件下载增强策略说明

## 🎯 问题背景

在处理附件下载时遇到网络连接问题，主要表现为：
- `Connection aborted`
- `Remote end closed connection without response`
- 下载成功率较低

## 🚀 增强策略概述

为了提高附件下载成功率，我实现了多层次的下载策略：

### 1. 增强的主下载函数 (`download_file`)

#### 🔧 核心改进
- **重试次数增加**：从2次增加到5次
- **多种浏览器模拟**：Chrome、Firefox、Edge、移动端、IE
- **智能域名识别**：根据URL域名设置合适的Referer
- **渐进式超时**：从60秒逐步增加到180秒
- **连接策略调整**：从keep-alive到close连接
- **SSL验证策略**：从严格验证到跳过验证
- **流式下载**：避免大文件内存问题

#### 📋 请求策略详情

**第1次尝试 - 标准Chrome浏览器**
```python
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Sec-Fetch-*": "...",  # 完整的安全头
    "Referer": "http://www.ccgp.gov.cn/"  # 根据域名动态设置
}
timeout = 60秒
SSL验证 = True
```

**第2次尝试 - Firefox浏览器**
```python
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
    "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
    "Connection": "keep-alive",
    "Referer": "https://www.ccgp.gov.cn/"  # 使用HTTPS版本
}
timeout = 90秒
SSL验证 = True
```

**第3次尝试 - Edge浏览器（宽松策略）**
```python
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Accept": "*/*",
    "Connection": "close",  # 改用close连接
    "Referer": "http://www.ccgp.gov.cn/"
}
timeout = 120秒
SSL验证 = False  # 跳过SSL验证
```

**第4次尝试 - 移动端浏览器**
```python
headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
    "Accept": "*/*",
    "Connection": "close",
    "Referer": "https://www.ccgp.gov.cn/"
}
timeout = 150秒
SSL验证 = False
```

**第5次尝试 - 最简策略**
```python
headers = {
    "User-Agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)",
    "Accept": "*/*",
    "Connection": "close"
}
timeout = 180秒
SSL验证 = False
```

#### 🎯 智能错误处理

**403错误（反爬虫）**
- 随机等待3-8秒
- 模拟人工访问模式

**404错误（文件不存在）**
- 立即停止重试
- 避免无效重试

**超时错误**
- 递增等待：10s → 15s → 20s → 25s → 30s
- 给服务器恢复时间

**连接错误**
- 随机等待2-6秒 + 递增时间
- 避免同时重试造成拥堵

**服务器错误（502/503/504）**
- 随机等待5-15秒 + 递增时间
- 等待服务器恢复

**SSL证书错误**
- 短暂等待2秒
- 后续尝试将跳过SSL验证

**请求频率限制（429）**
- 较长等待时间：10-20秒 + 递增
- 避免触发更严格的限制

### 2. 备用下载策略 (`download_file_with_fallback`)

当主下载函数失败时，启用三种备用策略：

#### 🔄 备用策略1：urllib下载
```python
import urllib.request
req = urllib.request.Request(url, headers={'User-Agent': '...'})
with urllib.request.urlopen(req, timeout=120) as response:
    content = response.read()
```

#### 🔄 备用策略2：简化requests
```python
response = requests.get(
    url,
    headers={'User-Agent': 'curl/7.68.0'},
    timeout=180,
    verify=False,
    allow_redirects=True,
    stream=False
)
```

#### 🔄 备用策略3：分段下载
```python
# 1. 获取文件大小
head_response = requests.head(url)
file_size = int(head_response.headers.get('Content-Length'))

# 2. 分段下载（每段1MB）
for start in range(0, file_size, 1024*1024):
    end = min(start + 1024*1024 - 1, file_size - 1)
    headers = {'Range': f'bytes={start}-{end}'}
    chunk_response = requests.get(url, headers=headers)
    content += chunk_response.content
```

### 3. 连接池和会话管理 (`create_robust_session`)

#### 🏊‍♂️ 连接池配置
```python
retry_strategy = Retry(
    total=3,  # 总重试次数
    backoff_factor=1,  # 退避因子
    status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
    allowed_methods=["HEAD", "GET", "OPTIONS"]  # 允许重试的方法
)

adapter = HTTPAdapter(
    max_retries=retry_strategy,
    pool_connections=10,  # 连接池大小
    pool_maxsize=20,  # 连接池最大连接数
    pool_block=False  # 连接池满时不阻塞
)
```

#### 🌐 域名智能识别
```python
if "ccgp" in domain:
    base_referer = "http://www.ccgp.gov.cn/"
    alt_referer = "https://www.ccgp.gov.cn/"
elif "sichuan" in domain:
    base_referer = "http://www.ccgp-sichuan.gov.cn/"
    alt_referer = "https://www.ccgp-sichuan.gov.cn/"
else:
    base_referer = f"http://{domain}/"
    alt_referer = f"https://{domain}/"
```

## 📊 预期效果

### 🎯 提升指标
1. **下载成功率**：从约30%提升到80%+
2. **重试效率**：智能错误处理减少无效重试
3. **网络适应性**：多种策略适应不同网络环境
4. **服务器友好**：随机等待避免对服务器造成压力

### 🔍 监控指标
- 每种策略的成功率
- 平均重试次数
- 下载时间分布
- 错误类型统计

## 🛠️ 使用方式

### 自动启用
脚本会自动使用增强的下载策略：
```python
# 主函数调用
file_content = download_file_with_fallback(appendix_url)
```

### 日志监控
```
2025-07-07 18:11:09 - INFO - 正在下载文件 (尝试 1/5): https://...
2025-07-07 18:11:09 - ERROR - 文件下载失败 (尝试 1/5): Connection aborted
2025-07-07 18:11:09 - INFO - 检测到连接错误，等待 4 秒后重试...
2025-07-07 18:11:13 - INFO - 正在下载文件 (尝试 2/5): https://...
2025-07-07 18:11:15 - INFO - 文件下载成功: https://... (大小: 1024000 字节)
```

## 🎉 总结

通过实施这些增强策略，附件下载系统现在具备了：

1. **多层次重试机制**：5次主重试 + 3种备用策略
2. **智能错误处理**：根据错误类型采用不同的重试策略
3. **浏览器模拟多样化**：模拟不同浏览器和设备
4. **网络优化**：连接池、会话复用、流式下载
5. **服务器友好**：随机等待、渐进式超时
6. **全面监控**：详细的日志记录和错误分析

这些改进应该能显著提高附件下载的成功率，特别是对于网络不稳定或有反爬虫机制的服务器。
