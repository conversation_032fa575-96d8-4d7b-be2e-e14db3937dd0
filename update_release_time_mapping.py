#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新markersweb_attachment_analysis索引的release_time字段类型
从text类型改为date类型
"""

import os
from datetime import datetime
from es_deal import init_es_client
from dotenv import load_dotenv
from utils.log_cfg import log


def check_current_mapping(es_client, index_name: str):
    """
    检查当前索引的映射
    """
    try:
        mapping = es_client.indices.get_mapping(index=index_name)
        properties = mapping[index_name]["mappings"].get("properties", {})

        log.info(f"当前索引 {index_name} 的映射信息:")
        if "release_time" in properties:
            release_time_mapping = properties["release_time"]
            log.info(f"release_time字段当前类型: {release_time_mapping}")
        else:
            log.warning("release_time字段不存在于当前映射中")

        return properties

    except Exception as e:
        log.error(f"检查映射失败: {e}")
        return None


def create_new_index_with_date_mapping(es_client, old_index: str, new_index: str):
    """
    创建新索引，将release_time字段设置为date类型
    """
    try:
        # 获取原索引的映射
        old_mapping = es_client.indices.get_mapping(index=old_index)
        old_properties = old_mapping[old_index]["mappings"].get("properties", {})

        # 创建新的映射，修改release_time字段类型
        new_properties = old_properties.copy()
        new_properties["release_time"] = {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd HH:mm||yyyy-MM-dd||epoch_millis",
        }

        # 创建新索引
        new_mapping = {"mappings": {"properties": new_properties}}

        # 获取原索引的设置
        old_settings = es_client.indices.get_settings(index=old_index)
        old_index_settings = old_settings[old_index]["settings"]["index"]

        # 过滤掉不能复制的设置
        new_settings = {}
        allowed_settings = ["number_of_shards", "number_of_replicas", "analysis"]
        for key, value in old_index_settings.items():
            if key in allowed_settings:
                new_settings[key] = value

        new_index_config = {
            "settings": new_settings,
            "mappings": new_mapping["mappings"],
        }

        log.info(f"创建新索引 {new_index}...")
        es_client.indices.create(index=new_index, body=new_index_config)
        log.info(f"新索引 {new_index} 创建成功")

        return True

    except Exception as e:
        log.error(f"创建新索引失败: {e}")
        return False


def reindex_data(es_client, source_index: str, dest_index: str):
    """
    将数据从源索引重新索引到目标索引
    """
    try:
        log.info(f"开始重新索引数据从 {source_index} 到 {dest_index}...")

        # 使用reindex API（简化版本，不进行复杂的数据转换）
        reindex_body = {
            "source": {"index": source_index},
            "dest": {"index": dest_index},
        }

        response = es_client.reindex(body=reindex_body, wait_for_completion=True)

        if response.get("failures"):
            log.error(f"重新索引过程中出现错误: {response['failures']}")
            return False

        log.info(f"重新索引完成:")
        log.info(f"  总数: {response.get('total', 0)}")
        log.info(f"  创建: {response.get('created', 0)}")
        log.info(f"  更新: {response.get('updated', 0)}")
        log.info(f"  删除: {response.get('deleted', 0)}")
        log.info(f"  版本冲突: {response.get('version_conflicts', 0)}")

        return True

    except Exception as e:
        log.error(f"重新索引失败: {e}")
        return False


def update_alias(es_client, old_index: str, new_index: str, alias_name: str):
    """
    更新别名指向新索引
    """
    try:
        log.info(f"更新别名 {alias_name}...")

        # 检查别名是否存在
        alias_exists = es_client.indices.exists_alias(name=alias_name)

        if alias_exists:
            # 获取当前别名指向的索引
            current_aliases = es_client.indices.get_alias(name=alias_name)

            # 原子性地更新别名
            actions = []

            # 移除旧的别名
            for index in current_aliases.keys():
                actions.append({"remove": {"index": index, "alias": alias_name}})

            # 添加新的别名
            actions.append({"add": {"index": new_index, "alias": alias_name}})

            es_client.indices.update_aliases(body={"actions": actions})
            log.info(f"别名 {alias_name} 已更新为指向 {new_index}")
        else:
            # 创建新别名
            es_client.indices.put_alias(index=new_index, name=alias_name)
            log.info(f"创建新别名 {alias_name} 指向 {new_index}")

        return True

    except Exception as e:
        log.error(f"更新别名失败: {e}")
        return False


def cleanup_old_index(es_client, old_index: str):
    """
    清理旧索引（可选）
    """
    try:
        log.info(f"是否删除旧索引 {old_index}? (y/N): ")
        # 在生产环境中，建议手动确认
        # response = input().strip().lower()
        # if response == 'y':
        #     es_client.indices.delete(index=old_index)
        #     log.info(f"旧索引 {old_index} 已删除")
        # else:
        #     log.info(f"保留旧索引 {old_index}")

        log.info(f"建议手动确认后删除旧索引 {old_index}")

    except Exception as e:
        log.error(f"清理旧索引失败: {e}")


def main():
    """
    主函数
    """
    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        es_client = init_es_client()

        # 索引名称
        old_index = "markersweb_attachment_analysis"
        new_index = f"markersweb_attachment_analysis_v2_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        alias_name = "markersweb_attachment_analysis_alias"

        log.info("=" * 80)
        log.info("开始更新release_time字段类型")
        log.info("=" * 80)
        log.info(f"源索引: {old_index}")
        log.info(f"新索引: {new_index}")
        log.info(f"别名: {alias_name}")

        # 1. 检查当前映射
        log.info("步骤1: 检查当前映射")
        current_mapping = check_current_mapping(es_client, old_index)
        if not current_mapping:
            log.error("无法获取当前映射，退出")
            return

        # 2. 创建新索引
        log.info("步骤2: 创建新索引")
        if not create_new_index_with_date_mapping(es_client, old_index, new_index):
            log.error("创建新索引失败，退出")
            return

        # 3. 重新索引数据
        log.info("步骤3: 重新索引数据")
        if not reindex_data(es_client, old_index, new_index):
            log.error("重新索引失败，退出")
            return

        # 4. 更新别名
        log.info("步骤4: 更新别名")
        if not update_alias(es_client, old_index, new_index, alias_name):
            log.error("更新别名失败")
            return

        # 5. 验证新索引
        log.info("步骤5: 验证新索引")
        new_mapping = check_current_mapping(es_client, new_index)
        if new_mapping and "release_time" in new_mapping:
            release_time_type = new_mapping["release_time"].get("type")
            if release_time_type == "date":
                log.info("✓ release_time字段类型已成功更新为date")
            else:
                log.warning(f"✗ release_time字段类型为: {release_time_type}")

        # 6. 清理旧索引（可选）
        log.info("步骤6: 清理旧索引")
        cleanup_old_index(es_client, old_index)

        log.info("=" * 80)
        log.info("字段类型更新完成！")
        log.info(f"新索引: {new_index}")
        log.info(f"别名: {alias_name}")
        log.info("=" * 80)

    except Exception as e:
        log.error(f"更新失败: {e}")
        raise


if __name__ == "__main__":
    main()
