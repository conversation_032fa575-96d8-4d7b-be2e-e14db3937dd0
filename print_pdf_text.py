#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
打印PDF文件中解析出的所有文字
"""

import os
from analyse_appendix import parse_pdf


def print_pdf_full_text(pdf_path):
    """
    打印PDF文件的完整文本内容
    """
    print("=" * 80)
    print(f"PDF文件完整文本内容: {os.path.basename(pdf_path)}")
    print("=" * 80)

    try:
        # 读取PDF文件
        with open(pdf_path, "rb") as f:
            pdf_content = f.read()

        print(f"文件大小: {len(pdf_content):,} 字节")

        # 解析PDF文本
        extracted_text = parse_pdf(pdf_content)

        if extracted_text.strip():
            print(f"提取文本长度: {len(extracted_text)} 字符")
            print(f"提取文本行数: {len(extracted_text.splitlines())} 行")
            print("\n" + "=" * 80)
            print("完整文本内容:")
            print("=" * 80)

            # 打印完整文本，保持原有格式
            print(extracted_text)

            print("=" * 80)
            print("文本内容结束")
            print("=" * 80)

            # 额外分析
            print(f"\n📊 文本统计:")
            lines = extracted_text.splitlines()
            non_empty_lines = [line for line in lines if line.strip()]
            print(f"  总行数: {len(lines)}")
            print(f"  非空行数: {len(non_empty_lines)}")
            print(f"  总字符数: {len(extracted_text)}")
            clean_text = (
                extracted_text.replace(" ", "").replace("\n", "").replace("\t", "")
            )
            print(f"  去除空白后字符数: {len(clean_text)}")

            # 显示每一行的内容（便于分析结构）
            print(f"\n📝 逐行内容分析:")
            for i, line in enumerate(lines, 1):
                if line.strip():  # 只显示非空行
                    print(f"  第{i:2d}行: {repr(line)}")
                else:
                    print(f"  第{i:2d}行: [空行]")
        else:
            print("❌ 未能提取到任何文本内容")
            print("可能原因:")
            print("  - PDF是扫描版图像")
            print("  - PDF使用了特殊编码")
            print("  - PDF文件损坏")

    except Exception as e:
        print(f"❌ 处理PDF文件时出错: {e}")
        import traceback

        traceback.print_exc()


def main():
    """
    主函数
    """
    # 查找"中小企业声明函.pdf"文件
    target_file = None
    downloads_dir = "downloads"

    if os.path.exists(downloads_dir):
        for file in os.listdir(downloads_dir):
            if "中小企业声明函" in file and file.lower().endswith(".pdf"):
                target_file = os.path.join(downloads_dir, file)
                break

    if target_file:
        print_pdf_full_text(target_file)
    else:
        print("❌ 未找到'中小企业声明函.pdf'文件")
        print("\n可用的PDF文件:")
        if os.path.exists(downloads_dir):
            pdf_files = [
                f for f in os.listdir(downloads_dir) if f.lower().endswith(".pdf")
            ]
            if pdf_files:
                for i, file in enumerate(pdf_files, 1):
                    print(f"  {i}. {file}")

                # 如果只有一个PDF文件，直接处理它
                if len(pdf_files) == 1:
                    print(f"\n处理唯一的PDF文件: {pdf_files[0]}")
                    print_pdf_full_text(os.path.join(downloads_dir, pdf_files[0]))
            else:
                print("  无PDF文件")
        else:
            print("  downloads目录不存在")


if __name__ == "__main__":
    main()
