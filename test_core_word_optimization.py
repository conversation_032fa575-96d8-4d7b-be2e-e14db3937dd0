#!/usr/bin/env python3
"""
测试核心词汇匹配优化

专门验证"清洁服务外包" vs "物业清洁服务项目"这类案例的优化效果
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要测试的模块
from analyse_appendix import calculate_text_similarity

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def test_core_word_matching_optimization():
    """测试核心词汇匹配优化效果"""
    log.info("=" * 80)
    log.info("测试核心词汇匹配优化效果")
    log.info("=" * 80)
    
    # 重点测试案例
    target_case = {
        "name": "核心词汇匹配案例",
        "text1": "清洁服务外包",
        "text2": "物业清洁服务项目",
        "description": "有共同核心词汇但无包含关系的案例"
    }
    
    log.info("重点案例分析:")
    log.info(f"文本1: '{target_case['text1']}'")
    log.info(f"文本2: '{target_case['text2']}'")
    log.info(f"说明: {target_case['description']}")
    log.info("")
    
    # 计算相似度
    similarity = calculate_text_similarity(target_case["text1"], target_case["text2"])
    
    log.info("详细分析:")
    log.info("1. 文本标准化:")
    log.info(f"   文本1标准化: '清洁服务外包' (去除'项目'等词汇)")
    log.info(f"   文本2标准化: '物业清洁服务' (去除'项目'等词汇)")
    log.info("")
    
    log.info("2. 包含关系检查:")
    log.info("   '清洁服务外包' 不包含在 '物业清洁服务' 中")
    log.info("   '物业清洁服务' 不包含在 '清洁服务外包' 中")
    log.info("   包含关系相似度: 0.0")
    log.info("")
    
    log.info("3. 核心词汇匹配:")
    log.info("   文本1核心词汇: {'清洁', '服务', '外包'}")
    log.info("   文本2核心词汇: {'物业', '清洁', '服务'}")
    log.info("   交集: {'清洁', '服务'} (2个重要词汇)")
    log.info("   并集: {'清洁', '服务', '外包', '物业'} (4个词汇)")
    log.info("   基础相似度: 2/4 = 0.5")
    log.info("   多词汇匹配加分: +0.2")
    log.info("   核心词汇相似度: 0.7")
    log.info("")
    
    log.info("4. 语义相关性检查:")
    log.info("   检查词汇组合: '清洁服务' 是重要的语义组合")
    log.info("   语义相关性加分: +0.3")
    log.info("")
    
    log.info("5. 动态权重分配:")
    log.info("   核心词汇相似度 0.7 >= 0.6，使用高匹配权重:")
    log.info("   - 包含关系: 0.0 × 30% = 0.0")
    log.info("   - 核心词汇: 0.7 × 50% = 0.35")
    log.info("   - 编辑距离: ~0.3 × 15% = ~0.045")
    log.info("   - Jaccard: ~0.4 × 5% = ~0.02")
    log.info("   - 基础相似度: ~0.415")
    log.info("   - 语义加分: +0.3")
    log.info("   - 最终相似度: ~0.715")
    log.info("")
    
    log.info(f"实际计算结果:")
    log.info(f"相似度: {similarity}")
    log.info(f"匹配状态: {'✅ 匹配' if similarity >= 0.5 else '❌ 不匹配'} ({'≥' if similarity >= 0.5 else '<'} 0.5)")
    log.info("")
    
    # 验证优化效果
    expected_improvement = similarity > 0.075  # 应该比原来的0.075有显著提升
    expected_match = similarity >= 0.5  # 应该能够匹配
    
    if expected_improvement:
        improvement = similarity - 0.075
        improvement_percent = (improvement / 0.075) * 100
        log.info(f"✅ 优化效果验证:")
        log.info(f"   相似度提升: +{improvement:.3f}")
        log.info(f"   提升幅度: +{improvement_percent:.1f}%")
        log.info(f"   匹配状态: 从不匹配 → {'匹配' if expected_match else '仍需优化'}")
    else:
        log.error(f"❌ 优化失败: 相似度{similarity}没有显著提升")
    
    return similarity, expected_match

def test_similar_cases():
    """测试类似的案例"""
    log.info("\n" + "=" * 80)
    log.info("测试类似的核心词汇匹配案例")
    log.info("=" * 80)
    
    similar_cases = [
        {
            "name": "医疗设备案例",
            "text1": "CT设备采购",
            "text2": "医院CT设备维护项目",
            "expected_high": True
        },
        {
            "name": "保安服务案例",
            "text1": "保安服务外包",
            "text2": "物业保安服务项目",
            "expected_high": True
        },
        {
            "name": "食堂管理案例",
            "text1": "食堂管理服务",
            "text2": "员工食堂管理外包项目",
            "expected_high": True
        },
        {
            "name": "完全不相关案例",
            "text1": "清洁服务外包",
            "text2": "医疗设备采购",
            "expected_high": False
        }
    ]
    
    log.info("类似案例测试结果:")
    log.info("-" * 60)
    
    for i, case in enumerate(similar_cases, 1):
        similarity = calculate_text_similarity(case["text1"], case["text2"])
        is_high = similarity >= 0.5
        is_correct = is_high == case["expected_high"]
        
        status = "✓" if is_correct else "❌"
        expectation = "高相似度" if case["expected_high"] else "低相似度"
        
        log.info(f"{status} 案例{i}: {case['name']}")
        log.info(f"    文本1: '{case['text1']}'")
        log.info(f"    文本2: '{case['text2']}'")
        log.info(f"    相似度: {similarity}")
        log.info(f"    期望: {expectation}, 实际: {'高相似度' if is_high else '低相似度'}")
        log.info("")
    
    log.info("✅ 类似案例测试完成")

def test_weight_adjustment_logic():
    """测试动态权重调整逻辑"""
    log.info("\n" + "=" * 80)
    log.info("测试动态权重调整逻辑")
    log.info("=" * 80)
    
    test_cases = [
        {
            "name": "高核心词汇匹配（应使用30%包含+50%核心词汇权重）",
            "text1": "清洁服务外包",
            "text2": "物业清洁服务项目",
            "expected_weight": "高匹配权重"
        },
        {
            "name": "低核心词汇匹配（应使用50%包含+30%核心词汇权重）",
            "text1": "保安服务",
            "text2": "医疗设备",
            "expected_weight": "低匹配权重"
        },
        {
            "name": "包含关系优先（应使用50%包含权重）",
            "text1": "保安服务",
            "text2": "绍兴市口腔医院关于保安服务项目",
            "expected_weight": "包含关系优先"
        }
    ]
    
    log.info("权重调整逻辑测试:")
    log.info("-" * 50)
    
    for case in test_cases:
        similarity = calculate_text_similarity(case["text1"], case["text2"])
        log.info(f"案例: {case['name']}")
        log.info(f"  文本1: '{case['text1']}'")
        log.info(f"  文本2: '{case['text2']}'")
        log.info(f"  相似度: {similarity}")
        log.info(f"  期望权重策略: {case['expected_weight']}")
        log.info("")
    
    log.info("✅ 权重调整逻辑测试完成")

def main():
    """主测试函数"""
    log.info("开始核心词汇匹配优化测试")
    log.info("=" * 100)
    
    try:
        # 运行所有测试
        similarity, is_match = test_core_word_matching_optimization()
        test_similar_cases()
        test_weight_adjustment_logic()
        
        log.info("\n" + "=" * 100)
        if is_match:
            log.info("🎉 核心词汇匹配优化测试完全成功！")
        else:
            log.info("⚠️  核心词汇匹配优化有改善但仍需进一步优化")
        log.info("=" * 100)
        
        log.info("\n优化总结:")
        log.info("✓ 实现动态权重调整机制")
        log.info("✓ 高核心词汇匹配时降低包含关系权重")
        log.info("✓ 增加语义相关性检查和加分")
        log.info("✓ 识别重要词汇组合匹配")
        log.info(f"✓ 目标案例相似度: {similarity}")
        log.info(f"✓ 匹配状态: {'成功匹配' if is_match else '仍需优化'}")
        
        return is_match
        
    except Exception as e:
        log.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
