#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试早期退出机制
"""

import os
from dotenv import load_dotenv
from es_deal import init_es_client
from analyse_appendix import DocumentAnalyzer

def test_early_exit_mechanism():
    """
    测试早期退出机制的效果
    """
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        # 获取环境变量
        es_index_links = os.getenv("ES_INDEX_LINKS", "chn_ylcg")
        es_index_analysis = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
        model_apikey = os.getenv("MODEL_APIKEY")
        model_name = os.getenv("MODEL_NAME", "Qwen/Qwen2.5-32B-Instruct")
        model_url = os.getenv("MODEL_URL", "https://api-inference.modelscope.cn/v1")
        prompt_spec = os.getenv("PROMPT_SPEC", "")
        
        # 创建分析器实例
        analyzer = DocumentAnalyzer(
            es_client=es_client,
            es_index_links=es_index_links,
            es_index_analysis=es_index_analysis,
            model_apikey=model_apikey,
            model_name=model_name,
            model_url=model_url,
            prompt_spec=prompt_spec,
        )
        
        print("=" * 80)
        print("测试早期退出机制")
        print("=" * 80)
        
        # 查找一个有多个附件的文档进行测试
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "appendix"}},
                        {"range": {"appendix": {"from": 2}}}  # 至少有2个附件
                    ]
                }
            },
            "size": 1
        }
        
        response = es_client.search(
            index=es_index_links,
            body=query
        )
        
        hits = response.get("hits", {}).get("hits", [])
        if not hits:
            print("没有找到有多个附件的测试文档，使用任意文档进行测试")
            # 使用任意有附件的文档
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "appendix"}},
                            {"range": {"appendix": {"from": 1}}}
                        ]
                    }
                },
                "size": 1
            }
            response = es_client.search(
                index=es_index_links,
                body=query
            )
            hits = response.get("hits", {}).get("hits", [])
        
        if not hits:
            print("没有找到任何有附件的测试文档")
            return
        
        doc = hits[0]
        print(f"测试文档ID: {doc['_id']}")
        print(f"文档标题: {doc['_source'].get('title', 'N/A')}")
        
        appendix = doc['_source'].get('appendix', [])
        print(f"附件数量: {len(appendix) if appendix else 0}")
        
        if appendix:
            print("附件列表:")
            for i, item in enumerate(appendix):
                print(f"  {i+1}. {item.get('text', 'N/A')} - {item.get('url', 'N/A')}")
        
        print("\n开始处理文档（观察早期退出机制）...")
        print("注意观察日志中的以下信息:")
        print("1. '已找到并分析招标文件' - 第一个招标文件被处理")
        print("2. '已找到招标文件，跳过后续招标文件' - 后续招标文件被跳过")
        print("3. '已找到并分析合同文件' - 第一个合同文件被处理")
        print("4. '已找到合同文件，跳过后续合同文件' - 后续合同文件被跳过")
        print("5. '招标文件和合同文件都已找到，提前结束附件遍历' - 完全早期退出")
        print("\n" + "="*80)
        
        # 调用process_one_record方法
        analyzer.process_one_record()
        
        print("="*80)
        print("文档处理完成！")
        print("早期退出机制测试完成")
        print("="*80)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_efficiency_improvement():
    """
    分析效率提升效果
    """
    print("\n" + "=" * 80)
    print("早期退出机制效率分析")
    print("=" * 80)
    
    print("🚀 效率提升效果:")
    print("1. **减少文件下载**: 跳过不必要的同类型文件下载")
    print("2. **减少LLM调用**: 避免重复分析同类型文件")
    print("3. **节省处理时间**: 找到目标文件后立即停止搜索")
    print("4. **降低成本**: 减少API调用次数和计算资源消耗")
    
    print("\n📊 预期效果:")
    print("- 如果文档有5个附件，其中前2个是招标和合同文件")
    print("  * 修改前: 处理5个文件")
    print("  * 修改后: 处理2个文件，跳过3个文件")
    print("  * 效率提升: 60%")
    
    print("\n⚙️ 实现机制:")
    print("1. **found_tender_file标志**: 跟踪是否已找到招标文件")
    print("2. **found_contract_file标志**: 跟踪是否已找到合同文件")
    print("3. **类型检测跳过**: 已找到对应类型时跳过后续同类型文件")
    print("4. **完全早期退出**: 两种文件都找到时结束整个附件遍历")
    
    print("\n🎯 业务逻辑保持:")
    print("- ✅ 每个记录仍然只有一个招标文件分析结果")
    print("- ✅ 每个记录仍然只有一个合同文件分析结果")
    print("- ✅ 融合逻辑保持不变")
    print("- ✅ 数据完整性不受影响")

def main():
    """
    主函数
    """
    test_early_exit_mechanism()
    analyze_efficiency_improvement()

if __name__ == "__main__":
    main()
