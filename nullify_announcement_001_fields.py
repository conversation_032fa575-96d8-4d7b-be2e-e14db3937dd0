#!/usr/bin/env python3
"""
批量处理公告类型为"001"的数据，将指定字段设置为null

使用方法:
1. 试运行模式（只统计，不实际更新）:
   python nullify_announcement_001_fields.py --dry-run

2. 实际更新模式:
   python nullify_announcement_001_fields.py

3. 指定批处理大小:
   python nullify_announcement_001_fields.py --batch-size 500

4. 指定索引名称:
   python nullify_announcement_001_fields.py --index custom_index_name
"""

import argparse
import sys
import os
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from es_deal import init_es_client, nullify_fields_for_announcement_001
from utils.log_cfg import log


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="批量处理公告类型为'001'的数据，将指定字段设置为null",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --dry-run                    # 试运行模式，只统计不更新
  %(prog)s                              # 实际更新模式
  %(prog)s --batch-size 500             # 指定批处理大小
  %(prog)s --index custom_index         # 指定索引名称
  
需要设置为null的字段:
  - bidder_price (中标金额)
  - bidder_name (中标单位名称)
  - bidder_contact_person (中标单位联系人)
  - bidder_contact_phone_number (中标单位联系人电话)
  - bidder_contract_config_param (中标合同配置参数)
  - bid_cancelled_flag (标段是否废标标记)
  - bid_cancelled_reason (标段废标原因)
  - contract_name (合同文件名称)
  - contract_ext (合同文件扩展名)
  - contract_link_out (合同文件外部链接)
  - contract_link_key (合同文件上传ID)
        """
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="试运行模式，只统计需要更新的文档数量，不实际更新数据"
    )
    
    parser.add_argument(
        "--batch-size",
        type=int,
        default=1000,
        help="批处理大小，默认1000"
    )
    
    parser.add_argument(
        "--index",
        type=str,
        help="索引名称，如果不指定则从环境变量ES_INDEX_ANALYSIS_ALIAS获取"
    )
    
    parser.add_argument(
        "--confirm",
        action="store_true",
        help="跳过确认提示，直接执行（仅在非试运行模式下有效）"
    )
    
    args = parser.parse_args()
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()
        
        # 确定索引名称
        if args.index:
            index_name = args.index
        else:
            index_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
            if not index_name:
                log.error("未指定索引名称，且环境变量ES_INDEX_ANALYSIS_ALIAS未设置")
                log.error("请使用 --index 参数指定索引名称，或在.env文件中设置ES_INDEX_ANALYSIS_ALIAS")
                sys.exit(1)
        
        log.info(f"目标索引: {index_name}")
        log.info(f"批处理大小: {args.batch_size}")
        log.info(f"试运行模式: {args.dry_run}")
        
        # 在非试运行模式下进行确认
        if not args.dry_run and not args.confirm:
            print("\n" + "="*60)
            print("⚠️  警告：您即将执行实际的数据更新操作！")
            print("="*60)
            print(f"目标索引: {index_name}")
            print("操作内容: 将公告类型为'001'的文档中以下字段设置为null:")
            print("  - bidder_price (中标金额)")
            print("  - bidder_name (中标单位名称)")
            print("  - bidder_contact_person (中标单位联系人)")
            print("  - bidder_contact_phone_number (中标单位联系人电话)")
            print("  - bidder_contract_config_param (中标合同配置参数)")
            print("  - bid_cancelled_flag (标段是否废标标记)")
            print("  - bid_cancelled_reason (标段废标原因)")
            print("  - contract_name (合同文件名称)")
            print("  - contract_ext (合同文件扩展名)")
            print("  - contract_link_out (合同文件外部链接)")
            print("  - contract_link_key (合同文件上传ID)")
            print("\n建议先使用 --dry-run 参数进行试运行！")
            print("="*60)
            
            confirm = input("\n确认要继续吗？(输入 'yes' 确认): ")
            if confirm.lower() != 'yes':
                log.info("操作已取消")
                sys.exit(0)
        
        # 执行处理
        log.info("\n开始处理...")
        stats = nullify_fields_for_announcement_001(
            es=es,
            index_name=index_name,
            batch_size=args.batch_size,
            dry_run=args.dry_run
        )
        
        # 输出结果摘要
        print("\n" + "="*60)
        print("🎉 处理完成！")
        print("="*60)
        print(f"找到的文档总数: {stats['total_found']}")
        print(f"处理的文档总数: {stats['total_processed']}")
        print(f"更新的文档总数: {stats['total_updated']}")
        print(f"错误的文档总数: {stats['total_errors']}")
        print(f"设置为null的字段数: {len(stats['fields_nullified'])}")
        
        if args.dry_run:
            print("\n💡 这是试运行结果，没有实际更新数据")
            print("   如需实际更新，请去掉 --dry-run 参数重新运行")
        else:
            print(f"\n✅ 已成功更新 {stats['total_updated']} 个文档")
            if stats['total_errors'] > 0:
                print(f"⚠️  有 {stats['total_errors']} 个文档更新失败，请检查日志")
        
        print("="*60)
        
    except KeyboardInterrupt:
        log.info("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        log.error(f"处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
