#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超时改进功能
验证超时时间增加、错误处理改进、内容长度限制等功能
"""

import time
from unittest.mock import Mock, patch
from analyse_appendix import DocumentAnalyzer, extract_fields_from_content
from utils.log_cfg import log


def test_timeout_settings():
    """测试超时时间设置"""
    print("=" * 60)
    print("测试1: 超时时间设置")
    print("=" * 60)
    
    try:
        # 创建DocumentAnalyzer实例，检查默认超时时间
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        print(f"DocumentAnalyzer默认超时时间: {analyzer.timeout}秒")
        assert analyzer.timeout == 300, f"期望300秒，实际{analyzer.timeout}秒"
        print("✓ DocumentAnalyzer超时时间设置正确")
        
        # 测试extract_fields_from_content的默认超时时间
        # 通过检查函数签名的默认值
        import inspect
        sig = inspect.signature(extract_fields_from_content)
        timeout_param = sig.parameters['timeout']
        default_timeout = timeout_param.default
        
        print(f"extract_fields_from_content默认超时时间: {default_timeout}秒")
        assert default_timeout == 300, f"期望300秒，实际{default_timeout}秒"
        print("✓ extract_fields_from_content超时时间设置正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 超时时间设置测试失败: {e}")
        return False


def test_content_length_limit():
    """测试内容长度限制"""
    print("\n" + "=" * 60)
    print("测试2: 内容长度限制")
    print("=" * 60)
    
    try:
        # 创建超长内容
        long_content = "这是一个很长的文档内容。" * 10000  # 约25万字符
        print(f"原始内容长度: {len(long_content)}字符")
        
        # 测试analyze_content的内容长度限制
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        # 模拟LLM调用，避免实际调用
        with patch('analyse_appendix.llm') as mock_llm:
            mock_llm.return_value = '[]'  # 返回空的JSON数组
            
            try:
                result = analyzer.analyze_content(long_content, "测试标题")
                print("✓ analyze_content内容长度限制测试通过")
            except Exception as e:
                print(f"✗ analyze_content内容长度限制测试失败: {e}")
                return False
        
        # 测试extract_fields_from_content的内容长度限制
        with patch('analyse_appendix.llm') as mock_llm:
            mock_llm.return_value = '{}'  # 返回空的JSON对象
            
            try:
                result = extract_fields_from_content(
                    content=long_content,
                    missing_fields=["prj_name", "tenderee"],
                    model_apikey="test_key",
                    model_name="test_model",
                    model_url="test_url"
                )
                print("✓ extract_fields_from_content内容长度限制测试通过")
            except Exception as e:
                print(f"✗ extract_fields_from_content内容长度限制测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 内容长度限制测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理改进"""
    print("\n" + "=" * 60)
    print("测试3: 错误处理改进")
    print("=" * 60)
    
    try:
        # 模拟LLM超时错误
        def mock_llm_timeout(*args, **kwargs):
            raise Exception("Request timed out.")
        
        # 测试extract_fields_from_content的错误处理
        with patch('analyse_appendix.llm', side_effect=mock_llm_timeout):
            result = extract_fields_from_content(
                content="测试内容",
                missing_fields=["prj_name"],
                model_apikey="test_key",
                model_name="test_model",
                model_url="test_url"
            )
            
            # 应该返回空字典，而不是抛出异常
            assert result == {}, f"期望空字典，实际{result}"
            print("✓ extract_fields_from_content错误处理正确")
        
        # 测试analyze_content的错误处理（通过DocumentAnalyzer）
        mock_es = Mock()
        analyzer = DocumentAnalyzer(
            es_client=mock_es,
            es_index_links="test_links",
            es_index_analysis="test_analysis",
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url",
            prompt_spec="test_prompt"
        )
        
        with patch('analyse_appendix.llm', side_effect=mock_llm_timeout):
            try:
                result = analyzer.analyze_content("测试内容", "测试标题")
                print("✗ analyze_content应该抛出异常")
                return False
            except Exception as e:
                if "Request timed out" in str(e):
                    print("✓ analyze_content正确抛出超时异常")
                else:
                    print(f"✗ analyze_content抛出了意外异常: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def test_timeout_simulation():
    """模拟超时情况的处理"""
    print("\n" + "=" * 60)
    print("测试4: 超时情况模拟")
    print("=" * 60)
    
    try:
        # 模拟慢速LLM响应
        def mock_slow_llm(*args, **kwargs):
            print("  模拟LLM处理中...")
            time.sleep(1)  # 模拟1秒延迟
            return '{"prj_name": "测试项目"}'
        
        start_time = time.time()
        
        with patch('analyse_appendix.llm', side_effect=mock_slow_llm):
            result = extract_fields_from_content(
                content="测试内容",
                missing_fields=["prj_name"],
                model_apikey="test_key",
                model_name="test_model",
                model_url="test_url",
                timeout=300  # 5分钟超时
            )
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"  处理耗时: {elapsed:.2f}秒")
        print(f"  提取结果: {result}")
        
        if result.get("prj_name") == "测试项目":
            print("✓ 超时情况模拟测试通过")
            return True
        else:
            print("✗ 超时情况模拟测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 超时情况模拟测试失败: {e}")
        return False


def test_graceful_degradation():
    """测试优雅降级"""
    print("\n" + "=" * 60)
    print("测试5: 优雅降级")
    print("=" * 60)
    
    try:
        # 模拟没有模型配置的情况
        result = extract_fields_from_content(
            content="测试内容",
            missing_fields=["prj_name"],
            model_apikey=None,  # 没有API密钥
            model_name="test_model",
            model_url="test_url"
        )
        
        # 应该返回空字典
        assert result == {}, f"期望空字典，实际{result}"
        print("✓ 没有API密钥时优雅降级")
        
        # 模拟空内容的情况
        result = extract_fields_from_content(
            content="",  # 空内容
            missing_fields=["prj_name"],
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url"
        )
        
        # 应该返回空字典
        assert result == {}, f"期望空字典，实际{result}"
        print("✓ 空内容时优雅降级")
        
        # 模拟没有缺失字段的情况
        result = extract_fields_from_content(
            content="测试内容",
            missing_fields=[],  # 没有缺失字段
            model_apikey="test_key",
            model_name="test_model",
            model_url="test_url"
        )
        
        # 应该返回空字典
        assert result == {}, f"期望空字典，实际{result}"
        print("✓ 没有缺失字段时优雅降级")
        
        return True
        
    except Exception as e:
        print(f"✗ 优雅降级测试失败: {e}")
        return False


def main():
    """运行所有超时改进测试"""
    print("开始测试超时改进功能...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("超时时间设置", test_timeout_settings()))
    test_results.append(("内容长度限制", test_content_length_limit()))
    test_results.append(("错误处理改进", test_error_handling()))
    test_results.append(("超时情况模拟", test_timeout_simulation()))
    test_results.append(("优雅降级", test_graceful_degradation()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("超时改进测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有超时改进测试通过！")
        print("\n改进效果:")
        print("1. ✓ 超时时间从60秒增加到300秒（5分钟）")
        print("2. ✓ 添加了内容长度限制，避免过长文档")
        print("3. ✓ 改进了错误处理，LLM失败不会中断整个流程")
        print("4. ✓ 添加了优雅降级机制")
        print("5. ✓ 保留主体解析结果，即使附件处理失败")
    else:
        print("⚠️  部分超时改进测试失败，请检查实现。")
    
    return passed == total


if __name__ == "__main__":
    main()
