#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试fitz修复
"""

def test_fitz_import():
    """
    测试fitz导入和基本功能
    """
    try:
        print("测试fitz导入...")
        
        # 尝试导入fitz
        try:
            import fitz
            print(f"✓ 成功导入fitz")
            print(f"  fitz模块路径: {fitz.__file__ if hasattr(fitz, '__file__') else 'N/A'}")
        except ImportError as e:
            print(f"✗ 导入fitz失败: {e}")
            return False
        
        # 检查可用的属性和方法
        print("fitz可用的属性和方法:")
        attrs = [attr for attr in dir(fitz) if not attr.startswith('_')]
        print(f"  总共 {len(attrs)} 个公共属性/方法")
        print(f"  前10个: {attrs[:10]}")
        
        # 检查关键方法
        has_document = hasattr(fitz, 'Document')
        has_open = hasattr(fitz, 'open')
        has_version = hasattr(fitz, 'version')
        
        print(f"  Document方法: {'✓' if has_document else '✗'}")
        print(f"  open方法: {'✓' if has_open else '✗'}")
        print(f"  version属性: {'✓' if has_version else '✗'}")
        
        if has_version:
            try:
                print(f"  版本信息: {fitz.version}")
            except:
                print(f"  版本信息: 无法获取")
        
        # 测试基本功能
        if has_document:
            try:
                doc = fitz.Document()
                print("✓ fitz.Document() 工作正常")
                doc.close()
                return True
            except Exception as e:
                print(f"✗ fitz.Document() 失败: {e}")
        
        if has_open:
            try:
                # 这里不能直接测试open，因为需要文件参数
                print("✓ fitz.open 方法存在")
                return True
            except Exception as e:
                print(f"✗ fitz.open 测试失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False

def test_parse_pdf_function():
    """
    测试parse_pdf函数
    """
    try:
        print("\n测试parse_pdf函数...")
        
        from analyse_appendix import parse_pdf
        
        # 测试空字节
        result = parse_pdf(b"")
        print(f"✓ 空字节测试: 返回 '{result}'")
        
        # 测试无效PDF
        result = parse_pdf(b"not a pdf")
        print(f"✓ 无效PDF测试: 返回 '{result}'")
        
        return True
        
    except Exception as e:
        print(f"✗ parse_pdf函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("fitz修复测试")
    print("=" * 60)
    
    # 测试fitz导入
    fitz_ok = test_fitz_import()
    
    # 测试parse_pdf函数
    pdf_ok = test_parse_pdf_function()
    
    print("\n" + "=" * 60)
    if fitz_ok and pdf_ok:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败")
        if not fitz_ok:
            print("  - fitz导入/功能测试失败")
        if not pdf_ok:
            print("  - parse_pdf函数测试失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
