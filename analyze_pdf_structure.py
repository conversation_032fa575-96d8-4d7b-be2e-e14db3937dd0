#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析PDF文件结构，解释文件大小与文本长度的差异
"""

import os
import pdfplumber
from analyse_appendix import parse_pdf

def analyze_pdf_structure(pdf_path):
    """
    详细分析PDF文件结构
    """
    print("=" * 80)
    print(f"PDF文件结构分析: {os.path.basename(pdf_path)}")
    print("=" * 80)
    
    # 基本文件信息
    file_size = os.path.getsize(pdf_path)
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    # 读取文件内容
    with open(pdf_path, 'rb') as f:
        pdf_content = f.read()
    
    # 提取文本
    extracted_text = parse_pdf(pdf_content)
    text_length = len(extracted_text)
    print(f"提取文本长度: {text_length:,} 字符")
    print(f"文本/文件大小比例: {text_length/file_size*100:.3f}%")
    
    # 使用pdfplumber进行详细分析
    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"\n📄 PDF基本信息:")
            print(f"  页数: {len(pdf.pages)}")
            
            # 元数据
            metadata = pdf.metadata or {}
            print(f"  创建者: {metadata.get('Creator', 'N/A')}")
            print(f"  生产者: {metadata.get('Producer', 'N/A')}")
            print(f"  创建时间: {metadata.get('CreationDate', 'N/A')}")
            
            # 分析每一页
            total_chars = 0
            total_images = 0
            total_lines = 0
            total_rects = 0
            
            for i, page in enumerate(pdf.pages):
                page_text = page.extract_text() or ""
                page_chars = len(page_text)
                total_chars += page_chars
                
                # 获取页面对象
                images = page.images
                lines = page.lines
                rects = page.rects
                
                total_images += len(images)
                total_lines += len(lines)
                total_rects += len(rects)
                
                print(f"\n  第{i+1}页:")
                print(f"    文本字符数: {page_chars}")
                print(f"    图像数量: {len(images)}")
                print(f"    线条数量: {len(lines)}")
                print(f"    矩形数量: {len(rects)}")
                
                # 显示页面文本预览
                if page_text.strip():
                    preview = page_text.strip()[:100].replace('\n', ' ')
                    print(f"    文本预览: {preview}...")
                else:
                    print(f"    文本预览: [无文本或扫描版]")
            
            print(f"\n📊 总计:")
            print(f"  总文本字符: {total_chars}")
            print(f"  总图像数: {total_images}")
            print(f"  总线条数: {total_lines}")
            print(f"  总矩形数: {total_rects}")
            
            # 分析文件大小构成
            print(f"\n🔍 文件大小分析:")
            
            # 估算各部分占用
            estimated_text_size = total_chars * 2  # UTF-8编码，平均2字节/字符
            estimated_structure_size = file_size - estimated_text_size
            
            print(f"  估算纯文本大小: {estimated_text_size:,} 字节 ({estimated_text_size/file_size*100:.1f}%)")
            print(f"  估算结构/格式大小: {estimated_structure_size:,} 字节 ({estimated_structure_size/file_size*100:.1f}%)")
            
            # 可能的原因分析
            print(f"\n💡 大小差异可能原因:")
            
            if total_images > 0:
                print(f"  ✓ 包含 {total_images} 个图像对象")
            
            if total_lines > 10:
                print(f"  ✓ 包含大量线条/边框 ({total_lines} 个)")
            
            if total_rects > 10:
                print(f"  ✓ 包含大量矩形/表格 ({total_rects} 个)")
            
            if estimated_structure_size > file_size * 0.8:
                print(f"  ✓ 主要为格式化信息和结构数据")
            
            if total_chars < 1000 and file_size > 100000:
                print(f"  ✓ 可能是扫描版PDF或图像为主的文档")
            
            # 字体信息
            try:
                # 尝试获取字体信息
                fonts_used = set()
                for page in pdf.pages:
                    if hasattr(page, 'chars'):
                        for char in page.chars:
                            if 'fontname' in char:
                                fonts_used.add(char['fontname'])
                
                if fonts_used:
                    print(f"  ✓ 使用了 {len(fonts_used)} 种字体")
                    for font in list(fonts_used)[:3]:  # 显示前3种
                        print(f"    - {font}")
                    if len(fonts_used) > 3:
                        print(f"    - ... 还有 {len(fonts_used)-3} 种")
            except:
                pass
                
    except Exception as e:
        print(f"详细分析失败: {e}")
    
    print("\n" + "=" * 80)

def main():
    """
    主函数
    """
    # 查找PDF文件
    downloads_dir = "downloads"
    pdf_files = []
    
    if os.path.exists(downloads_dir):
        for file in os.listdir(downloads_dir):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(downloads_dir, file))
    
    if pdf_files:
        # 分析第一个PDF文件
        analyze_pdf_structure(pdf_files[0])
        
        # 如果有多个文件，也分析第二个
        if len(pdf_files) > 1:
            print("\n" + "="*80)
            print("分析第二个PDF文件...")
            analyze_pdf_structure(pdf_files[1])
    else:
        print("未找到PDF文件进行分析")

if __name__ == "__main__":
    main()
