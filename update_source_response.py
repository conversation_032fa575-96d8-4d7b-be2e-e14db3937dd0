#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新markersweb_attachment_analysis_alias索引，添加source_response字段
通过source_id与chn_ylcg索引的_id进行关联，获取response字段内容
"""

import os
from typing import Dict, List, Optional
from es_deal import init_es_client, search_documents
from dotenv import load_dotenv
from utils.log_cfg import log


def get_response_from_chn_ylcg(es_client, source_id: str) -> Optional[str]:
    """
    从chn_ylcg索引中根据_id获取response字段内容

    Args:
        es_client: Elasticsearch客户端
        source_id: 文档ID

    Returns:
        response字段内容，如果未找到则返回None
    """
    try:
        # 直接通过文档ID获取文档
        response = es_client.get(
            index="chn_ylcg", id=source_id, _source=["response"]  # 只获取response字段
        )

        if response and response.get("_source"):
            return response["_source"].get("response")
        else:
            log.warning(f"未找到ID为 {source_id} 的文档")
            return None

    except Exception as e:
        log.error(f"获取文档 {source_id} 失败: {e}")
        return None


def batch_get_responses_from_chn_ylcg(
    es_client, source_ids: List[str]
) -> Dict[str, str]:
    """
    批量从chn_ylcg索引中获取response字段内容

    Args:
        es_client: Elasticsearch客户端
        source_ids: 文档ID列表

    Returns:
        字典，key为source_id，value为response内容
    """
    result = {}

    if not source_ids:
        return result

    try:
        # 使用mget批量获取文档
        mget_body = {
            "docs": [
                {"_index": "chn_ylcg", "_id": source_id, "_source": ["response"]}
                for source_id in source_ids
            ]
        }

        response = es_client.mget(body=mget_body)

        for doc in response.get("docs", []):
            if doc.get("found") and doc.get("_source"):
                source_id = doc["_id"]
                response_content = doc["_source"].get("response")
                if response_content:
                    result[source_id] = response_content
                else:
                    log.warning(f"文档 {source_id} 的response字段为空")
            else:
                source_id = doc.get("_id", "unknown")
                log.warning(f"未找到ID为 {source_id} 的文档")

        log.info(
            f"批量获取完成: 请求 {len(source_ids)} 个文档，成功获取 {len(result)} 个"
        )

    except Exception as e:
        log.error(f"批量获取文档失败: {e}")

    return result


def bulk_update_documents(es_client, index_name: str, updates: List[Dict]):
    """
    批量更新文档，添加source_response字段

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        updates: 更新操作列表，格式为[{"doc_id": "xxx", "source_response": "xxx"}, ...]
    """
    if not updates:
        return

    try:
        # 构建bulk更新操作
        bulk_body = []
        for update in updates:
            # 添加更新操作的元数据
            bulk_body.append(
                {"update": {"_index": index_name, "_id": update["doc_id"]}}
            )
            # 添加更新的文档内容
            bulk_body.append({"doc": {"source_response": update["source_response"]}})

        # 执行bulk更新
        response = es_client.bulk(body=bulk_body)

        # 检查更新结果
        success_count = 0
        error_count = 0

        for item in response.get("items", []):
            if "update" in item:
                if item["update"].get("status") in [200, 201]:
                    success_count += 1
                else:
                    error_count += 1
                    log.error(f"更新失败: {item['update']}")

        log.info(f"批量更新完成: 成功 {success_count}, 失败 {error_count}")

    except Exception as e:
        log.error(f"批量更新失败: {e}")
        raise


def process_batch_update(es_client, analysis_index: str, batch_size: int = 100):
    """
    批量处理更新操作

    Args:
        es_client: Elasticsearch客户端
        analysis_index: 分析结果索引名
        batch_size: 批处理大小
    """
    try:
        # 构建查询，获取所有需要更新的文档
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "source_id"}},  # 必须有source_id字段
                    ],
                    "must_not": [
                        {
                            "exists": {"field": "source_response"}
                        }  # 没有source_response字段的文档
                    ],
                }
            },
            "_source": ["source_id"],  # 只获取source_id字段
            "size": batch_size,
        }

        # 使用scroll API处理大量数据
        scroll_response = es_client.search(
            index=analysis_index, body=query, scroll="5m"
        )

        scroll_id = scroll_response.get("_scroll_id")
        hits = scroll_response.get("hits", {}).get("hits", [])

        total_processed = 0
        total_updated = 0

        while hits:
            log.info(f"处理批次，文档数量: {len(hits)}")

            # 收集所有source_id
            doc_source_mapping = {}  # doc_id -> source_id
            source_ids = []

            for hit in hits:
                doc_id = hit["_id"]
                source_id = hit["_source"].get("source_id")

                if not source_id:
                    log.warning(f"文档 {doc_id} 没有source_id字段")
                    total_processed += 1
                    continue

                doc_source_mapping[doc_id] = source_id
                source_ids.append(source_id)
                total_processed += 1

            # 批量获取response内容
            if source_ids:
                responses = batch_get_responses_from_chn_ylcg(es_client, source_ids)

                # 准备批量更新数据
                batch_updates = []
                for doc_id, source_id in doc_source_mapping.items():
                    if source_id in responses:
                        batch_updates.append(
                            {"doc_id": doc_id, "source_response": responses[source_id]}
                        )
                    else:
                        log.warning(f"未能获取source_id {source_id} 的response内容")

                # 执行批量更新
                if batch_updates:
                    bulk_update_documents(es_client, analysis_index, batch_updates)
                    total_updated += len(batch_updates)

            # 获取下一批数据
            try:
                scroll_response = es_client.scroll(scroll_id=scroll_id, scroll="5m")
                hits = scroll_response.get("hits", {}).get("hits", [])
            except Exception as e:
                log.error(f"Scroll查询失败: {e}")
                break

        # 清理scroll
        try:
            es_client.clear_scroll(scroll_id=scroll_id)
        except Exception as e:
            log.warning(f"清理scroll失败: {e}")

        log.info(
            f"批量更新完成。总处理文档数: {total_processed}, 成功更新数: {total_updated}"
        )

    except Exception as e:
        log.error(f"批量更新失败: {e}")
        raise


def main():
    """
    主函数
    """
    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        es_client = init_es_client()

        # 获取索引名称
        analysis_index = "markersweb_attachment_analysis_alias"

        log.info(
            "开始更新markersweb_attachment_analysis_alias索引，添加source_response字段"
        )
        log.info(f"目标索引: {analysis_index}")
        log.info(f"源索引: chn_ylcg")

        # 执行批量更新
        process_batch_update(es_client, analysis_index)

        log.info("更新操作完成")

    except Exception as e:
        log.error(f"更新失败: {e}")
        raise


if __name__ == "__main__":
    main()
