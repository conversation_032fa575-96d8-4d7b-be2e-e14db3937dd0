#!/usr/bin/env python3
"""
测试 clean_file_fields_by_keywords.py 脚本的功能

该脚本用于验证：
1. 关键词检查函数是否正确工作
2. 清理逻辑是否按预期工作
3. 各种边界情况的处理
"""

import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from clean_file_fields_by_keywords import contains_keywords, BID_DOC_KEYWORDS, CONTRACT_KEYWORDS
from utils.log_cfg import log


def test_bid_doc_keywords():
    """测试招标文件关键词检查"""
    log.info("测试招标文件关键词检查...")
    
    test_cases = [
        # (filename, expected_result, description)
        ("医疗设备招标文件.pdf", True, "包含'招标文件'关键词"),
        ("采购磋商文件.docx", True, "包含'磋商文件'关键词"),
        ("设备采购谈判文件.pdf", True, "包含'谈判文件'关键词"),
        ("招标文件-医疗设备采购", True, "包含'招标文件'关键词（开头）"),
        ("医疗设备采购招标文件", True, "包含'招标文件'关键词（结尾）"),
        ("采购公告.pdf", False, "不包含招标文件关键词"),
        ("中标公告.docx", False, "不包含招标文件关键词"),
        ("技术规格书.pdf", False, "不包含招标文件关键词"),
        ("", False, "空文件名"),
        (None, False, "None文件名"),
        ("招标 文件.pdf", False, "关键词被分割"),
        ("磋商 文件.docx", False, "关键词被分割"),
        ("谈判 文件.pdf", False, "关键词被分割"),
        ("招标文件磋商文件谈判文件", True, "包含多个关键词"),
        ("ZHAOBIAOWENJIAN", False, "拼音不匹配"),
    ]
    
    success_count = 0
    for i, (filename, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_keywords(filename, BID_DOC_KEYWORDS)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    文件名: '{filename}'")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"招标文件关键词测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_contract_keywords():
    """测试合同文件关键词检查"""
    log.info("测试合同文件关键词检查...")
    
    test_cases = [
        # (filename, expected_result, description)
        ("医疗设备采购合同.pdf", True, "包含'合同'关键词"),
        ("设备维护服务协议.docx", True, "包含'服务协议'关键词"),
        ("合同-医疗设备采购", True, "包含'合同'关键词（开头）"),
        ("医疗设备采购合同", True, "包含'合同'关键词（结尾）"),
        ("设备维护服务协议书", True, "包含'服务协议'关键词"),
        ("招标文件.pdf", False, "不包含合同关键词"),
        ("中标公告.docx", False, "不包含合同关键词"),
        ("技术规格书.pdf", False, "不包含合同关键词"),
        ("", False, "空文件名"),
        (None, False, "None文件名"),
        ("合 同.pdf", False, "关键词被分割"),
        ("服务 协议.docx", False, "关键词被分割"),
        ("合同服务协议", True, "包含多个关键词"),
        ("HETONG", False, "拼音不匹配"),
        ("contract.pdf", False, "英文不匹配"),
    ]
    
    success_count = 0
    for i, (filename, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_keywords(filename, CONTRACT_KEYWORDS)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    文件名: '{filename}'")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"合同文件关键词测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_custom_keywords():
    """测试自定义关键词"""
    log.info("测试自定义关键词...")
    
    custom_keywords = ["采购", "设备"]
    test_cases = [
        ("医疗设备采购", True, "包含自定义关键词'采购'"),
        ("医疗设备维护", True, "包含自定义关键词'设备'"),
        ("招标公告", False, "不包含自定义关键词"),
        ("合同文件", False, "不包含自定义关键词"),
    ]
    
    success_count = 0
    for i, (text, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_keywords(text, custom_keywords)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"自定义关键词测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_real_world_examples():
    """测试真实世界的例子"""
    log.info("测试真实世界的例子...")
    
    # 招标文件测试用例
    bid_test_cases = [
        ("绍兴市口腔医院医疗设备采购招标文件", True, "真实招标文件名"),
        ("某某医院设备采购磋商文件", True, "真实磋商文件名"),
        ("医疗器械采购谈判文件", True, "真实谈判文件名"),
        ("绍兴市口腔医院医疗设备采购公告", False, "采购公告（非招标文件）"),
        ("医疗设备中标公告", False, "中标公告（非招标文件）"),
        ("设备技术参数表", False, "技术文档（非招标文件）"),
    ]
    
    # 合同文件测试用例
    contract_test_cases = [
        ("绍兴市口腔医院医疗设备采购合同", True, "真实采购合同名"),
        ("医疗设备维护服务协议", True, "真实服务协议名"),
        ("设备保修服务协议书", True, "真实服务协议名"),
        ("绍兴市口腔医院医疗设备采购招标文件", False, "招标文件（非合同）"),
        ("医疗设备中标通知书", False, "中标通知（非合同）"),
        ("设备验收报告", False, "验收报告（非合同）"),
    ]
    
    success_count = 0
    total_cases = len(bid_test_cases) + len(contract_test_cases)
    
    # 测试招标文件
    for i, (filename, expected, description) in enumerate(bid_test_cases, 1):
        try:
            result = contains_keywords(filename, BID_DOC_KEYWORDS)
            
            if result == expected:
                log.info(f"  ✓ 招标测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 招标测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    文件名: '{filename}'")
        except Exception as e:
            log.error(f"  ✗ 招标测试 {i}: {description} -> 异常: {e}")
    
    # 测试合同文件
    for i, (filename, expected, description) in enumerate(contract_test_cases, 1):
        try:
            result = contains_keywords(filename, CONTRACT_KEYWORDS)
            
            if result == expected:
                log.info(f"  ✓ 合同测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 合同测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    文件名: '{filename}'")
        except Exception as e:
            log.error(f"  ✗ 合同测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"真实世界例子测试完成: {success_count}/{total_cases} 通过")
    return success_count == total_cases


def test_constants():
    """测试常量定义"""
    log.info("测试常量定义...")
    
    expected_bid_keywords = ["招标文件", "磋商文件", "谈判文件"]
    expected_contract_keywords = ["合同", "服务协议"]
    
    success_count = 0
    
    if BID_DOC_KEYWORDS == expected_bid_keywords:
        log.info(f"✓ BID_DOC_KEYWORDS 常量正确: {BID_DOC_KEYWORDS}")
        success_count += 1
    else:
        log.error(f"✗ BID_DOC_KEYWORDS 常量错误: 期望 {expected_bid_keywords}, 实际 {BID_DOC_KEYWORDS}")
    
    if CONTRACT_KEYWORDS == expected_contract_keywords:
        log.info(f"✓ CONTRACT_KEYWORDS 常量正确: {CONTRACT_KEYWORDS}")
        success_count += 1
    else:
        log.error(f"✗ CONTRACT_KEYWORDS 常量错误: 期望 {expected_contract_keywords}, 实际 {CONTRACT_KEYWORDS}")
    
    log.info(f"常量定义测试完成: {success_count}/2 通过")
    return success_count == 2


def main():
    """主函数"""
    try:
        log.info("开始测试文件字段清理功能...")
        log.info("=" * 60)
        
        all_tests_passed = True
        
        # 1. 测试常量定义
        if not test_constants():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 2. 测试招标文件关键词
        if not test_bid_doc_keywords():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 3. 测试合同文件关键词
        if not test_contract_keywords():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 4. 测试自定义关键词
        if not test_custom_keywords():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 5. 测试真实世界例子
        if not test_real_world_examples():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        if all_tests_passed:
            log.info("✓ 所有测试通过！文件字段清理功能正常工作。")
        else:
            log.error("✗ 部分测试失败，请检查代码实现。")
            sys.exit(1)
        
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
