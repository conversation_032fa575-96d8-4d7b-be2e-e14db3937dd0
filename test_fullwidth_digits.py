#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试全角数字修复功能
"""

import json
from analyse_noappendix import clean_json_data

def test_fullwidth_digits_fix():
    """
    测试全角数字修复功能
    """
    print("=" * 80)
    print("测试全角数字修复功能")
    print("=" * 80)
    
    # 包含全角数字的问题JSON
    problematic_json = '''[
    {
        "bid_name": "青岛大学附属医院医疗设备采购项目（五）-1第4包",
        "bid_number": "SDGP370000000202501003069",
        "bid_budget": null,
        "fiscal_delegation_number": null,
        "prj_addr": "",
        "prj_name": "青岛大学附属医院医疗设备采购项目(五)-1",
        "prj_number": "SDGP370000000202501003069",
        "prj_type": "货物",
        "release_time": "2025-06-30 14:00:00",
        "prj_approval_authority": null,
        "superintendent_office": null,
        "superintendent_office_code": "",
        "tenderee": "青岛大学附属医院",
        "bid_submission_deadline": null,
        "trade_platform": "中国政府采购网",
        "procurement_method": null,
        "prj_sub_type": "设备",
        "province": "山东省",
        "city": "青岛市",
        "county": "市南区",
        "announcement_type": "004",
        "object_name": "4-1放射性废物桶/4-2铅眼镜/4-3注射器钨合金防护套/●4-4辐射剂量检测仪/●4-5铅屏风",
        "object_brand": "",
        "object_model": "",
        "object_supplier": "济南夏日经贸有限公司",
        "object_produce_area": "",
        "object_conf": "4-1放射性废物桶/4-2铅眼镜/4-3注射器钨合金防护套/●4-4辐射剂量检测仪/●4-5铅屏风",
        "object_oem": "",
        "object_amount": null,
        "object_unit": "",
        "object_price": null,
        "object_total_price": 117600.0,
        "object_maintenance_period": "",
        "object_price_source": "",
               "object_quality": "",
        "bidder_price": 117600.0,
        "bidder_name": "济南夏日经贸有限公司",
        "bidder_contact_person": "",
        "bidder_contact_phone_number": "",
        "bidder_contract_config_param": "",
        "agent": "山东龙脉招标有限公司",
        "service_fee": 14１1.2,
        "bid_cancelled_flag": null,
        "bid_cancelled_reason": ""
    }
]'''
    
    print("原始JSON（包含全角数字）:")
    print("问题位置: service_fee: 14１1.2 (包含全角数字'１')")
    print()
    
    # 测试原始JSON是否能解析
    try:
        json.loads(problematic_json)
        print("❌ 意外：原始JSON竟然可以解析")
    except json.JSONDecodeError as e:
        print(f"✅ 预期：原始JSON解析失败: {e}")
    
    print("\n" + "-" * 60)
    print("开始修复...")
    
    # 使用clean_json_data修复
    try:
        cleaned_json = clean_json_data(problematic_json)
        print("✅ JSON清理成功")
        
        # 尝试解析清理后的JSON
        parsed_data = json.loads(cleaned_json)
        print("✅ 清理后的JSON解析成功")
        
        # 检查修复结果
        service_fee = parsed_data[0].get("service_fee")
        print(f"修复后的service_fee值: {service_fee} (类型: {type(service_fee)})")
        
        # 验证是否正确转换
        if service_fee == 1411.2:
            print("✅ 全角数字修复成功：14１1.2 → 1411.2")
        else:
            print(f"❌ 全角数字修复失败，期望1411.2，实际得到{service_fee}")
        
        print(f"\n修复后的完整数据:")
        print(f"  bid_name: {parsed_data[0].get('bid_name')}")
        print(f"  object_total_price: {parsed_data[0].get('object_total_price')}")
        print(f"  bidder_price: {parsed_data[0].get('bidder_price')}")
        print(f"  service_fee: {parsed_data[0].get('service_fee')}")
        
    except Exception as e:
        print(f"❌ JSON清理或解析失败: {e}")
        import traceback
        traceback.print_exc()

def test_various_fullwidth_digits():
    """
    测试各种全角数字的修复
    """
    print("\n" + "=" * 80)
    print("测试各种全角数字修复")
    print("=" * 80)
    
    test_cases = [
        ("０", "0"),
        ("１", "1"),
        ("２", "2"),
        ("３", "3"),
        ("４", "4"),
        ("５", "5"),
        ("６", "6"),
        ("７", "7"),
        ("８", "8"),
        ("９", "9"),
        ("１２３４５", "12345"),
        ("１０００.５", "1000.5"),
        ("２０２５年", "2025年"),
    ]
    
    print("全角数字转换测试:")
    for fullwidth, expected in test_cases:
        # 创建包含全角数字的JSON
        test_json = f'{{"test_value": "{fullwidth}"}}'
        
        try:
            cleaned = clean_json_data(test_json)
            parsed = json.loads(cleaned)
            actual = parsed["test_value"]
            
            status = "✅" if actual == expected else "❌"
            print(f"  {status} {fullwidth} → {actual} (期望: {expected})")
            
        except Exception as e:
            print(f"  ❌ {fullwidth} → 转换失败: {e}")

def test_mixed_content():
    """
    测试混合内容（中文标点+全角数字）
    """
    print("\n" + "=" * 80)
    print("测试混合内容修复")
    print("=" * 80)
    
    mixed_json = '''{
        "name": "测试项目"，
        "price": １２３４.５６，
        "date": "２０２５年０６月３０日"：
        "amount"： ９９９
    }'''
    
    print("原始JSON（包含中文标点和全角数字）:")
    print(mixed_json)
    print()
    
    try:
        cleaned = clean_json_data(mixed_json)
        parsed = json.loads(cleaned)
        
        print("✅ 混合内容修复成功")
        print("修复后的数据:")
        for key, value in parsed.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ 混合内容修复失败: {e}")

def main():
    """
    主函数
    """
    test_fullwidth_digits_fix()
    test_various_fullwidth_digits()
    test_mixed_content()
    
    print("\n" + "=" * 80)
    print("全角数字修复测试完成")
    print("=" * 80)

if __name__ == "__main__":
    main()
