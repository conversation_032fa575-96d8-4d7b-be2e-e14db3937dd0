# analyse_appendix.py 向 markersweb_attachment_analysis_alias 索引插入数据的字段分析

## 1. 插入操作代码位置

### 1.1 主要插入位置

**位置1：正常流程的融合结果插入**
- 文件：`analyse_appendix.py`
- 行号：2567-2575
- 代码：
```python
for i, final_result in enumerate(final_results):
    new_doc_id = f"{final_result['source_id']}_{i}"
    insert_document(
        self.es,
        self.es_index_analysis,  # markersweb_attachment_analysis_alias
        doc_id=new_doc_id,
        document=final_result,
    )
    log.info(f"成功插入融合文档 {new_doc_id}")
```

**位置2：异常情况下的主体解析结果插入**
- 文件：`analyse_appendix.py`
- 行号：2615-2621
- 代码：
```python
for i, result in enumerate(main_result_list):
    new_doc_id = f"{doc['_id']}_main_{i}"
    insert_document(
        self.es,
        self.es_index_analysis,  # markersweb_attachment_analysis_alias
        doc_id=new_doc_id,
        document=result,
    )
    log.info(f"成功插入主体解析结果 {new_doc_id}")
```

### 1.2 插入函数定义

**函数位置：**
- 文件：`es_deal.py`
- 行号：379-412
- 函数：`insert_document(es, index_name, doc_id, document)`

## 2. 完整字段列表

### 2.1 业务数据字段（从LLM解析获得）

| 字段名 | 字段类型 | 字段描述 |
|--------|----------|----------|
| `bid_name` | text | 标段名称,标段有时也叫包组或者包 |
| `bid_number` | keyword | 标段编号,标段有时也叫包组或者包 |
| `bid_budget` | double | 标段预算金额,标段有时也叫包组或者包 |
| `fiscal_delegation_number` | keyword | 财政委托编号 |
| `prj_addr` | text | 招标项目地址 |
| `prj_name` | text | 招标项目名称 |
| `prj_number` | keyword | 招标项目编号 |
| `prj_type` | keyword | 招标项目类型(工程,货物,服务) |
| `release_time` | date | 发布日期,格式:yyyy-MM-dd HH:mm:ss |
| `prj_approval_authority` | text | 项目审批单位 |
| `superintendent_office` | text | 监督部门 |
| `superintendent_office_code` | keyword | 监督部门编号 |
| `tenderee` | text | 招标人 |
| `bid_submission_deadline` | date | 投标截止时间,格式:yyyy-MM-dd HH:mm:ss |
| `trade_platform` | text | 交易平台 |
| `procurement_method` | text | 采购方式 |
| `prj_sub_type` | keyword | 项目细分类型(设备,维保,耗材,试剂,手术器械,其他) |
| `province` | keyword | 省份,必须是XX省 |
| `city` | keyword | 城市,必须是XX市 |
| `county` | keyword | 区县,必须是XX区或者XX县,但不能是市辖区 |
| `announcement_type` | keyword | 公告类型(001,002,003,004,010,999) |
| `object_name` | text | 标的物名称 |
| `object_brand` | keyword | 标的物品牌 |
| `object_model` | keyword | 标的物型号 |
| `object_supplier` | text | 标的物供应商 |
| `object_produce_area` | text | 标的物产地 |
| `object_conf` | text | 标的物配置参数,包含技术要求、技术规格等 |
| `object_oem` | text | 标的物OEM厂家 |
| `object_amount` | integer | 标的物数量 |
| `object_unit` | keyword | 标的物单位 |
| `object_price` | double | 标的物单价 |
| `object_total_price` | double | 标的物总价 |
| `object_maintenance_period` | keyword | 标的物维保期限 |
| `object_price_source` | keyword | 标的物价格来源 |
| `object_quality` | keyword | 标的物质量层次(1,2),1代表国产,2代表进口 |
| `bidder_price` | double | 中标金额 |
| `bidder_name` | text | 中标单位名称 |
| `bidder_contact_person` | text | 中标单位联系人 |
| `bidder_contact_phone_number` | keyword | 中标单位联系人电话 |
| `bidder_contract_config_param` | text | 中标合同配置参数,来自中标合同或服务协议里的报价表和技术应答 |
| `agent` | text | 代理机构 |
| `service_fee` | double | 代理服务收费金额 |
| `bid_cancelled_flag` | keyword | 标段是否废标标记,废标填1,否则填null |
| `bid_cancelled_reason` | text | 标段废标原因 |

### 2.2 源数据元数据字段（从原始文档获得）

| 字段名 | 字段类型 | 字段描述 |
|--------|----------|----------|
| `source_id` | keyword | 源文档ID（来自chn_ylcg索引的_id） |
| `source_title` | text | 源文档标题 |
| `source_create_time` | date | 源文档创建时间 |
| `source_category` | keyword | 源文档分类 |
| `source_url` | text | 源文档URL |
| `source_appendix` | text | 源文档附件信息 |

### 2.3 附件相关字段（从附件处理获得）

| 字段名 | 字段类型 | 字段描述 |
|--------|----------|----------|
| `bid_doc_name` | text | 招标文件名称 |
| `bid_doc_ext` | keyword | 招标文件扩展名 |
| `bid_doc_link_out` | text | 招标文件外部链接 |
| `bid_doc_link_key` | keyword | 招标文件上传ID |
| `contract_name` | text | 合同文件名称 |
| `contract_ext` | keyword | 合同文件扩展名 |
| `contract_link_out` | text | 合同文件外部链接 |
| `contract_link_key` | keyword | 合同文件上传ID |

### 2.4 系统字段（程序自动添加）

| 字段名 | 字段类型 | 字段描述 |
|--------|----------|----------|
| `insert_time` | text | 插入时间,格式:yyyy-MM-dd HH:mm:ss |

## 3. 字段分类和优先级

### 3.1 合同专属字段（CONTRACT_FIELDS）
这些字段只能从合同文件中获取，具有最高优先级：
- `bidder_price` - 中标金额
- `bidder_name` - 中标单位名称
- `bidder_contact_person` - 中标单位联系人
- `bidder_contact_phone_number` - 中标单位联系人电话
- `bidder_contract_config_param` - 中标合同配置参数
- `contract_name` - 合同文件名称
- `contract_ext` - 合同文件扩展名
- `contract_link_out` - 合同文件外部链接
- `contract_link_key` - 合同文件上传ID

### 3.2 附件专属字段（APPENDIX_FIELDS）
这些字段总是从附件解析结果中获取：
- `bid_doc_name` - 招标文件名称
- `bid_doc_ext` - 招标文件扩展名
- `bid_doc_link_out` - 招标文件外部链接
- `bid_doc_link_key` - 招标文件上传ID
- `contract_name` - 合同文件名称
- `contract_ext` - 合同文件扩展名
- `contract_link_out` - 合同文件外部链接
- `contract_link_key` - 合同文件上传ID

### 3.3 通用业务字段
其他所有业务字段，按优先级融合：
1. 公告主体解析结果（优先级最高）
2. 招标文件解析结果（补充）
3. 合同文件解析结果（仅限非CONTRACT_FIELDS字段）

## 4. 数据融合逻辑

### 4.1 融合优先级
1. **公告主体内容**：最高优先级，作为基础数据
2. **招标文件内容**：补充公告主体中缺失的字段（除CONTRACT_FIELDS外）
3. **合同文件内容**：只能补充CONTRACT_FIELDS中的字段

### 4.2 智能补充机制
当基础融合后仍有字段缺失时，程序会：
1. 识别缺失字段
2. 从对应的文档内容中使用LLM智能提取
3. 将提取结果补充到最终结果中

## 5. 文档ID生成规则

### 5.1 正常情况
- 格式：`{source_id}_{index}`
- 示例：`-1pfuZcBsUtJ06NfZI4t_0`

### 5.2 异常情况（仅主体解析）
- 格式：`{source_id}_main_{index}`
- 示例：`-1pfuZcBsUtJ06NfZI4t_main_0`

## 6. 总计字段数量

- **业务数据字段**：40个
- **源数据元数据字段**：6个
- **附件相关字段**：8个
- **系统字段**：1个
- **总计**：55个字段

## 7. 特殊说明

### 7.1 日期字段格式
- `release_time`：yyyy-MM-dd HH:mm:ss
- `bid_submission_deadline`：yyyy-MM-dd HH:mm:ss
- `source_create_time`：继承自源文档
- `insert_time`：yyyy-MM-dd HH:mm:ss

### 7.2 枚举字段值
- `announcement_type`：001(招标公告),002(候选人公示),003(更正/澄清公告),004(结果公告),010(中标通知书),999(其他)
- `prj_type`：工程,货物,服务
- `prj_sub_type`：设备,维保,耗材,试剂,手术器械,其他
- `object_quality`：1(国产),2(进口)
- `bid_cancelled_flag`：1(废标),null(正常)

### 7.3 金额字段
所有金额字段统一转换为人民币元：
- `bid_budget`
- `object_price`
- `object_total_price`
- `bidder_price`
- `service_fee`
