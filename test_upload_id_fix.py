#!/usr/bin/env python3
"""
测试bid_doc_link_key指向实际文件上传ID的修复
"""

def test_upload_id_priority():
    """测试上传ID优先级逻辑"""
    print("=== 测试上传ID优先级逻辑 ===")
    
    # 模拟压缩包内文件数据（包含upload_id）
    item_with_upload_id = {
        "filename": "广州医科大学附属第五医院部分后勤保障服务项目招标文件（2025070401）.docx",
        "file_ext": ".docx",
        "upload_id": "actual_file_upload_id_123"  # 实际文件的上传ID
    }
    
    # 模拟附件信息（压缩包的信息）
    actual_file_info = {
        "url": "https://example.com/compressed_file.zip",
        "text": "广州医科大学附属第五医院部分后勤保障服务项目招标文件",
        "file_link_key": "compressed_file_upload_id_456"  # 压缩包的上传ID
    }
    
    print("测试数据:")
    print(f"  实际文件名: {item_with_upload_id['filename']}")
    print(f"  实际文件上传ID: {item_with_upload_id['upload_id']}")
    print(f"  压缩包上传ID: {actual_file_info['file_link_key']}")
    
    # 模拟修复后的逻辑：优先使用压缩包内文件的upload_id
    file_link_key = item_with_upload_id.get("upload_id") or actual_file_info.get("file_link_key")
    
    # 模拟更新结果
    result = {
        "bid_doc_name": item_with_upload_id["filename"],
        "bid_doc_ext": item_with_upload_id.get("file_ext", ""),
        "bid_doc_link_out": actual_file_info["url"],
        "bid_doc_link_key": file_link_key
    }
    
    print(f"\n更新后的字段:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # 验证
    expected_link_key = "actual_file_upload_id_123"  # 应该使用实际文件的上传ID
    
    print(f"\n验证结果:")
    print(f"  bid_doc_link_key正确: {result['bid_doc_link_key'] == expected_link_key}")
    print(f"  期望值: {expected_link_key}")
    print(f"  实际值: {result['bid_doc_link_key']}")
    
    if result['bid_doc_link_key'] == expected_link_key:
        print("✅ 测试通过：正确使用了实际文件的上传ID")
    else:
        print("❌ 测试失败：没有使用实际文件的上传ID")


def test_fallback_to_compressed_id():
    """测试回退到压缩包ID的逻辑"""
    print("\n=== 测试回退到压缩包ID的逻辑 ===")
    
    # 模拟压缩包内文件数据（没有upload_id）
    item_without_upload_id = {
        "filename": "某个文件.docx",
        "file_ext": ".docx",
        # 没有upload_id字段
    }
    
    # 模拟附件信息（压缩包的信息）
    actual_file_info = {
        "url": "https://example.com/compressed_file.zip",
        "text": "压缩包文件",
        "file_link_key": "compressed_file_upload_id_789"
    }
    
    print("测试数据:")
    print(f"  实际文件名: {item_without_upload_id['filename']}")
    print(f"  实际文件上传ID: None")
    print(f"  压缩包上传ID: {actual_file_info['file_link_key']}")
    
    # 模拟修复后的逻辑：回退到压缩包的file_link_key
    file_link_key = item_without_upload_id.get("upload_id") or actual_file_info.get("file_link_key")
    
    # 模拟更新结果
    result = {
        "bid_doc_name": item_without_upload_id["filename"],
        "bid_doc_ext": item_without_upload_id.get("file_ext", ""),
        "bid_doc_link_out": actual_file_info["url"],
        "bid_doc_link_key": file_link_key
    }
    
    print(f"\n更新后的字段:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # 验证
    expected_link_key = "compressed_file_upload_id_789"  # 应该使用压缩包的上传ID
    
    print(f"\n验证结果:")
    print(f"  bid_doc_link_key正确: {result['bid_doc_link_key'] == expected_link_key}")
    print(f"  期望值: {expected_link_key}")
    print(f"  实际值: {result['bid_doc_link_key']}")
    
    if result['bid_doc_link_key'] == expected_link_key:
        print("✅ 测试通过：正确回退到压缩包的上传ID")
    else:
        print("❌ 测试失败：没有正确回退到压缩包的上传ID")


def test_handle_compressed_file_upload():
    """测试handle_compressed_file函数的上传逻辑"""
    print("\n=== 测试handle_compressed_file函数的上传逻辑 ===")
    
    # 模拟函数返回的数据结构
    extracted_files_content = [
        {
            "filename": "招标文件.docx",
            "content": "文件内容",
            "file_ext": ".docx",
            "file_bytes": b"binary_content",
            "upload_id": "docx_upload_id_111"  # 新增的upload_id字段
        },
        {
            "filename": "合同文件.pdf",
            "content": "文件内容",
            "file_ext": ".pdf", 
            "file_bytes": b"binary_content",
            "upload_id": "pdf_upload_id_222"  # 新增的upload_id字段
        }
    ]
    
    print("模拟handle_compressed_file返回的数据:")
    for i, item in enumerate(extracted_files_content, 1):
        print(f"  文件{i}:")
        print(f"    filename: {item['filename']}")
        print(f"    file_ext: {item['file_ext']}")
        print(f"    upload_id: {item['upload_id']}")
    
    print("\n✅ handle_compressed_file现在会为每个压缩包内文件返回upload_id")
    print("✅ 这样bid_doc_link_key就能指向实际文件的上传ID而不是压缩包的ID")


if __name__ == "__main__":
    test_upload_id_priority()
    test_fallback_to_compressed_id()
    test_handle_compressed_file_upload()
