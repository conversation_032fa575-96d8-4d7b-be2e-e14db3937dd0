#!/usr/bin/env python3
"""
测试 analyse_appendix.py 中新增的公告类型999附件内容过滤功能

该脚本用于验证：
1. contains_contract_keywords 函数是否正确工作
2. CONTRACT_KEYWORDS 常量是否正确定义
3. 过滤逻辑是否按预期工作
"""

import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import contains_contract_keywords, CONTRACT_KEYWORDS
from utils.log_cfg import log


def test_contract_keywords_constant():
    """测试合同关键词常量"""
    log.info("测试合同关键词常量...")
    
    expected_keywords = ["合同", "服务协议"]
    
    if CONTRACT_KEYWORDS == expected_keywords:
        log.info(f"✓ CONTRACT_KEYWORDS 常量正确: {CONTRACT_KEYWORDS}")
        return True
    else:
        log.error(f"✗ CONTRACT_KEYWORDS 常量错误: 期望 {expected_keywords}, 实际 {CONTRACT_KEYWORDS}")
        return False


def test_contains_contract_keywords_function():
    """测试合同关键词检查函数"""
    log.info("测试合同关键词检查函数...")
    
    test_cases = [
        # (content, expected_result, description)
        ("这是一份采购合同文件", True, "包含'合同'关键词"),
        ("本次采购服务协议的内容如下", True, "包含'服务协议'关键词"),
        ("合同编号：ABC123", True, "包含'合同'关键词（开头）"),
        ("签署服务协议", True, "包含'服务协议'关键词"),
        ("这是招标文件", False, "不包含合同关键词"),
        ("采购公告内容", False, "不包含合同关键词"),
        ("技术规格书", False, "不包含合同关键词"),
        ("", False, "空内容"),
        (None, False, "None内容"),
        ("HETONG合同FUWUXIEYI", True, "包含中文关键词（混合内容）"),
        ("这是一份购买合同的复印件", True, "包含'合同'关键词（中间位置）"),
        ("服务协议条款说明", True, "包含'服务协议'关键词（开头）"),
        ("合同", True, "仅包含'合同'关键词"),
        ("服务协议", True, "仅包含'服务协议'关键词"),
        ("合 同", False, "关键词被分割"),
        ("服务 协议", False, "关键词被分割"),
    ]
    
    success_count = 0
    for i, (content, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_keywords(content)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    内容: '{content}'")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"合同关键词检查函数测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_custom_keywords():
    """测试自定义关键词"""
    log.info("测试自定义关键词...")
    
    custom_keywords = ["采购", "招标"]
    test_cases = [
        ("这是采购文件", True, "包含自定义关键词'采购'"),
        ("招标公告", True, "包含自定义关键词'招标'"),
        ("合同文件", False, "不包含自定义关键词"),
        ("服务协议", False, "不包含自定义关键词"),
    ]
    
    success_count = 0
    for i, (content, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_keywords(content, custom_keywords)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"自定义关键词测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_case_sensitivity():
    """测试大小写敏感性"""
    log.info("测试大小写敏感性...")
    
    test_cases = [
        ("合同", True, "小写关键词"),
        ("HETONG", False, "大写拼音不匹配"),
        ("合同文件", True, "包含小写关键词"),
        ("这是一份合同", True, "句子中包含关键词"),
    ]
    
    success_count = 0
    for i, (content, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_keywords(content)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"大小写敏感性测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_real_world_examples():
    """测试真实世界的例子"""
    log.info("测试真实世界的例子...")
    
    test_cases = [
        ("""
        医疗设备采购合同
        
        甲方：某某医院
        乙方：某某公司
        
        根据《政府采购法》等相关法律法规，经双方协商一致，签订本合同。
        """, True, "真实合同文档"),
        
        ("""
        医疗设备维护服务协议
        
        为确保医疗设备的正常运行，特制定本服务协议。
        服务内容包括：设备维护、故障排除、定期检查等。
        """, True, "真实服务协议文档"),
        
        ("""
        医疗设备采购招标文件
        
        一、项目概况
        项目名称：医疗设备采购
        采购方式：公开招标
        
        二、投标人资格要求
        1. 具有独立法人资格
        2. 具有相关经营范围
        """, False, "招标文件（不包含合同关键词）"),
        
        ("""
        中标公告
        
        经评标委员会评审，现将中标结果公告如下：
        中标单位：某某公司
        中标金额：100万元
        """, False, "中标公告（不包含合同关键词）"),
    ]
    
    success_count = 0
    for i, (content, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_keywords(content)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    内容预览: {content[:100]}...")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"真实世界例子测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def main():
    """主函数"""
    try:
        log.info("开始测试公告类型999附件内容过滤功能...")
        log.info("=" * 60)
        
        all_tests_passed = True
        
        # 1. 测试常量定义
        if not test_contract_keywords_constant():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 2. 测试基本功能
        if not test_contains_contract_keywords_function():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 3. 测试自定义关键词
        if not test_custom_keywords():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 4. 测试大小写敏感性
        if not test_case_sensitivity():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        # 5. 测试真实世界例子
        if not test_real_world_examples():
            all_tests_passed = False
        
        log.info("=" * 60)
        
        if all_tests_passed:
            log.info("✓ 所有测试通过！公告类型999附件内容过滤功能正常工作。")
        else:
            log.error("✗ 部分测试失败，请检查代码实现。")
            sys.exit(1)
        
    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
