#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的智能融合功能
验证优先使用已有解析结果，避免不必要的LLM调用
"""

from analyse_appendix import intelligent_merge_analysis, identify_missing_fields
from utils.log_cfg import log
import json


def test_prioritize_existing_results():
    """测试优先使用已有解析结果的逻辑"""
    print("=" * 60)
    print("测试1: 优先使用已有解析结果")
    print("=" * 60)
    
    # 模拟主体解析结果（有一些空缺字段）
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "prj_number": "YY2025001",
            "tenderee": None,  # 空缺，应该从招标文件解析结果获取
            "object_name": "CT设备",
            "object_brand": None,  # 空缺，应该从招标文件解析结果获取
            "object_model": None,  # 空缺，应该从招标文件解析结果获取
            "bidder_name": None,  # 空缺，应该从合同文件解析结果获取
            "bidder_price": None,  # 空缺，应该从合同文件解析结果获取
            "agent": None,  # 空缺，应该从招标文件解析结果获取
        }
    ]
    
    # 模拟招标文件解析结果（包含一些主体结果中缺失的字段）
    tender_results = [
        {
            "tenderee": "某市人民医院",
            "object_brand": "西门子",
            "object_model": "SOMATOM CT",
            "agent": "某招标代理公司",
            "prj_approval_authority": "市卫健委",  # 额外字段
        }
    ]
    
    # 模拟合同文件解析结果（包含合同相关字段）
    contract_results = [
        {
            "bidder_name": "医疗设备有限公司",
            "bidder_price": 2500000.0,
            "bidder_contact_person": "张经理",
            "bidder_contact_phone_number": "13800138000",
        }
    ]
    
    print("主体解析结果（融合前）:")
    missing_before = identify_missing_fields(main_results[0])
    print(f"  空缺字段数量: {len(missing_before)}")
    print(f"  空缺字段示例: {missing_before[:10]}")
    
    print("\n招标文件解析结果:")
    print(f"  可用字段: {list(tender_results[0].keys())}")
    
    print("\n合同文件解析结果:")
    print(f"  可用字段: {list(contract_results[0].keys())}")
    
    # 执行智能融合（不提供文档内容，只使用解析结果）
    print("\n开始智能融合（仅使用已有解析结果）...")
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_list=tender_results,
        contract_list=contract_results
        # 注意：不提供tender_content、contract_content和model配置
        # 这样就只会使用已有的解析结果，不会调用LLM
    )
    
    print("\n智能融合后的结果:")
    merged_result = merged_results[0]
    missing_after = identify_missing_fields(merged_result)
    
    print(f"  融合后空缺字段数量: {len(missing_after)}")
    print(f"  减少空缺字段: {len(missing_before) - len(missing_after)}个")
    
    # 验证关键字段是否正确融合
    expected_fields = {
        "tenderee": "某市人民医院",
        "object_brand": "西门子", 
        "object_model": "SOMATOM CT",
        "agent": "某招标代理公司",
        "bidder_name": "医疗设备有限公司",
        "bidder_price": 2500000.0,
    }
    
    print("\n字段融合验证:")
    all_correct = True
    for field, expected_value in expected_fields.items():
        actual_value = merged_result.get(field)
        if actual_value == expected_value:
            print(f"  ✓ {field}: {actual_value}")
        else:
            print(f"  ✗ {field}: 期望 {expected_value}, 实际 {actual_value}")
            all_correct = False
    
    if all_correct:
        print("\n✓ 所有字段融合正确，优先使用已有解析结果成功！")
        return True
    else:
        print("\n✗ 部分字段融合失败")
        return False


def test_fallback_to_llm():
    """测试当解析结果不足时回退到LLM的逻辑"""
    print("\n" + "=" * 60)
    print("测试2: 回退到LLM提取的逻辑")
    print("=" * 60)
    
    # 模拟主体解析结果
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "object_name": "MRI设备",
            "tenderee": None,  # 空缺
            "object_brand": None,  # 空缺
            "bidder_name": None,  # 空缺
        }
    ]
    
    # 模拟招标文件解析结果（故意不包含所有需要的字段）
    tender_results = [
        {
            "tenderee": "某市人民医院",
            # 故意不包含object_brand，测试是否会尝试从文档内容提取
        }
    ]
    
    # 模拟合同文件解析结果（故意不包含所有需要的字段）
    contract_results = [
        {
            # 故意不包含bidder_name，测试是否会尝试从文档内容提取
            "bidder_price": 1500000.0,
        }
    ]
    
    # 模拟文档内容
    tender_content = """
    招标公告
    项目名称：医院设备采购项目
    招标人：某市人民医院
    设备要求：MRI设备，品牌：飞利浦
    """
    
    contract_content = """
    采购合同
    中标单位：医疗科技有限公司
    中标金额：1,500,000元
    """
    
    print("主体解析结果:")
    missing_before = identify_missing_fields(main_results[0])
    print(f"  空缺字段: {[f for f in missing_before if f in ['tenderee', 'object_brand', 'bidder_name']]}")
    
    print("\n招标文件解析结果:")
    print(f"  可用字段: {list(tender_results[0].keys())}")
    
    print("\n合同文件解析结果:")
    print(f"  可用字段: {list(contract_results[0].keys())}")
    
    print("\n执行智能融合（包含文档内容，但不提供模型配置）...")
    # 不提供模型配置，应该只使用已有解析结果，不会调用LLM
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_content=tender_content,
        contract_content=contract_content,
        tender_list=tender_results,
        contract_list=contract_results
        # 注意：不提供model_apikey等配置，所以不会调用LLM
    )
    
    merged_result = merged_results[0]
    
    print("\n融合结果:")
    print(f"  tenderee: {merged_result.get('tenderee')}")  # 应该从招标文件解析结果获取
    print(f"  object_brand: {merged_result.get('object_brand')}")  # 应该仍为None（没有LLM提取）
    print(f"  bidder_name: {merged_result.get('bidder_name')}")  # 应该仍为None（没有LLM提取）
    
    # 验证只使用了已有解析结果
    if (merged_result.get('tenderee') == "某市人民医院" and 
        merged_result.get('object_brand') is None and 
        merged_result.get('bidder_name') is None):
        print("\n✓ 正确：只使用了已有解析结果，没有调用LLM")
        return True
    else:
        print("\n✗ 错误：逻辑不符合预期")
        return False


def test_no_unnecessary_llm_calls():
    """测试不进行不必要的LLM调用"""
    print("\n" + "=" * 60)
    print("测试3: 避免不必要的LLM调用")
    print("=" * 60)
    
    # 模拟主体解析结果（所有字段都已填充）
    main_results = [
        {
            "prj_name": "医院设备采购项目",
            "prj_number": "YY2025001",
            "tenderee": "某市人民医院",
            "object_name": "CT设备",
            "object_brand": "西门子",
            "bidder_name": "医疗设备公司",
            "bidder_price": 2000000.0,
            "agent": "招标代理公司",
        }
    ]
    
    print("主体解析结果:")
    missing_fields = identify_missing_fields(main_results[0])
    print(f"  空缺字段数量: {len(missing_fields)}")
    
    if len(missing_fields) == 0:
        print("  所有字段都已填充，不需要智能融合")
    
    # 执行智能融合
    merged_results = intelligent_merge_analysis(
        main_list=main_results,
        tender_content="招标文件内容...",
        contract_content="合同文件内容...",
        model_apikey="test_key",  # 提供了模型配置
        model_name="test_model",
        model_url="test_url"
    )
    
    merged_result = merged_results[0]
    
    # 验证结果没有改变（因为没有空缺字段）
    if merged_result == main_results[0]:
        print("\n✓ 正确：没有空缺字段时不进行额外处理")
        return True
    else:
        print("\n✗ 错误：不应该修改已完整的结果")
        return False


def main():
    """运行所有优化测试"""
    print("开始测试优化后的智能融合功能...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("优先使用已有解析结果", test_prioritize_existing_results()))
    test_results.append(("回退到LLM提取逻辑", test_fallback_to_llm()))
    test_results.append(("避免不必要的LLM调用", test_no_unnecessary_llm_calls()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("优化测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有优化测试通过！智能融合逻辑已优化。")
        print("\n优化效果:")
        print("1. ✓ 优先使用已有的招标文件和合同文件解析结果")
        print("2. ✓ 避免不必要的LLM API调用")
        print("3. ✓ 提高处理速度和降低成本")
        print("4. ✓ 保持智能融合的完整性")
    else:
        print("⚠️  部分优化测试失败，请检查实现。")
    
    return passed == total


if __name__ == "__main__":
    main()
