#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多进程文档分析模块
基于 analyse_noappendix.py 的多进程版本，支持并发文档处理
"""

import os
import multiprocessing as mp
from multiprocessing import Pool, Manager
import signal
import time
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import json

from es_deal import init_es_client, search_documents, insert_document
from dotenv import load_dotenv
from openai import OpenAI
from utils.log_cfg import log
from markdownify import markdownify as md
from blacklist_manager import BlacklistManager, print_blacklist_stats

# 导入原始模块的函数和常量
from analyse_noappendix import (
    STANDARD_FIELDS,
    validate_and_normalize_fields,
    clean_json_data,
    process_json_data,
    clean_json_markdown,
    llm,
)


class DocumentAnalyzerMP:
    """多进程文档分析器"""

    def __init__(
        self,
        es_client,
        es_index_links: str,
        es_index_analysis: str,
        model_apikey: str,
        model_name: str,
        model_url: str,
        prompt_spec: str,
        timeout: int = 300,
        max_retries: int = 3,
        worker_count: int = None,
        batch_size: int = None,
    ):
        """
        初始化多进程文档分析器

        Args:
            es_client: Elasticsearch客户端实例
            es_index_links: 源数据索引名
            es_index_analysis: 分析结果索引名
            model_apikey: API Token
            model_name: 模型名称
            model_url: 模型URL
            prompt_spec: 提示词规范
            timeout: 超时时间
            max_retries: 最大重试次数
            worker_count: 工作进程数量（默认为CPU核心数）
            batch_size: 批处理大小（默认为工作进程数的2倍）
        """
        self.es = es_client
        self.es_index_links = es_index_links
        self.es_index_analysis = es_index_analysis
        self.model_apikey = model_apikey
        self.model_name = model_name
        self.model_url = model_url
        self.prompt_spec = prompt_spec
        self.timeout = timeout
        self.max_retries = max_retries

        # 多进程配置
        self.worker_count = worker_count or mp.cpu_count()
        self.batch_size = batch_size or (self.worker_count * 2)

        # 初始化黑名单管理器（启用文件锁定）
        self.blacklist_manager = BlacklistManager(enable_file_locking=True)

        # 打印配置信息
        log.info(f"多进程文档分析器初始化完成:")
        log.info(f"  工作进程数: {self.worker_count}")
        log.info(f"  批处理大小: {self.batch_size}")
        log.info(f"  文件锁定: 启用")

        # 打印黑名单统计信息
        stats = self.blacklist_manager.get_blacklist_stats()
        if stats["total_count"] > 0:
            log.info(f"黑名单中有 {stats['total_count']} 个文档将被跳过")
            if stats["today_count"] > 0:
                log.info(f"今日新增黑名单文档: {stats['today_count']} 个")

    def build_query(
        self,
        categories: List[str],
        exclude_ids: List[str] = None,
        batch_size: int = None,
    ) -> Dict:
        """
        构建ES查询（批量版本）

        Args:
            categories: 分类列表
            exclude_ids: 需要排除的ID列表
            batch_size: 批量大小

        Returns:
            Dict: ES查询语句
        """
        must_conditions = [
            {"terms": {"category": categories}},
            # 检查appendix字段为空
            {
                "bool": {
                    "must_not": [
                        {
                            "nested": {
                                "path": "appendix",
                                "query": {
                                    "bool": {
                                        "must": [
                                            {"exists": {"field": "appendix.text"}},
                                            {"exists": {"field": "appendix.url"}},
                                        ]
                                    }
                                },
                            }
                        }
                    ]
                }
            },
        ]

        if exclude_ids:
            must_conditions.append(
                {"bool": {"must_not": [{"ids": {"values": exclude_ids}}]}}
            )

        return {
            "query": {"bool": {"must": must_conditions}},
            "size": batch_size or self.batch_size,
            "sort": [{"_id": {"order": "asc"}}],
        }

    def get_processed_ids(self) -> List[str]:
        """
        获取已处理的文档ID列表

        Returns:
            List[str]: 已处理的文档ID列表
        """
        processed_ids = []

        # 初始化scroll查询，指定需要返回source_id字段
        query = {
            "query": {"match_all": {}},
            "_source": ["source_id"],  # 只返回source_id字段
            "size": 10000,
        }

        # 执行初始搜索
        result = search_documents(self.es, self.es_index_analysis, query=query)
        if not result:
            return processed_ids

        # 获取第一批结果
        hits = result.get("hits", {}).get("hits", [])
        # 从source中获取source_id
        processed_ids.extend([hit["_source"]["source_id"] for hit in hits])

        # 获取scroll_id
        scroll_id = result.get("_scroll_id")
        if not scroll_id:
            return processed_ids

        # 使用scroll API获取剩余结果
        while True:
            scroll_result = self.es.scroll(
                scroll_id=scroll_id, scroll="5m"  # 保持scroll上下文5分钟
            )

            hits = scroll_result.get("hits", {}).get("hits", [])
            if not hits:
                break

            # 从source中获取source_id
            processed_ids.extend([hit["_source"]["source_id"] for hit in hits])

        # 清理scroll上下文
        self.es.clear_scroll(scroll_id=scroll_id)

        # 去重
        processed_ids = list(set(processed_ids))

        return processed_ids

    def get_batch_documents(
        self,
        categories: List[str] = ["001", "004", "010"],
        batch_size: int = None,
    ) -> List[Dict]:
        """
        获取一批待处理的文档（原子操作）

        Args:
            categories: 分类列表
            batch_size: 批量大小

        Returns:
            List[Dict]: 文档列表
        """
        try:
            # 获取已处理的ID列表
            processed_ids = self.get_processed_ids()

            # 获取黑名单ID列表
            blacklisted_ids = self.blacklist_manager.get_blacklisted_ids()

            # 合并排除列表
            exclude_ids = list(set(processed_ids + blacklisted_ids))

            if blacklisted_ids:
                log.info(f"排除 {len(blacklisted_ids)} 个黑名单文档")

            # 构建查询，排除已处理的ID和黑名单ID
            query = self.build_query(
                categories, exclude_ids=exclude_ids, batch_size=batch_size
            )
            result = search_documents(self.es, self.es_index_links, query=query)

            if result and result.get("hits", {}).get("hits"):
                documents = result["hits"]["hits"]
                log.info(f"获取到 {len(documents)} 个待处理文档")
                return documents
            else:
                log.info("未找到待处理的文档")
                return []

        except Exception as e:
            log.error(f"获取批量文档失败: {e}")
            return []

    def process_document_batch(
        self,
        documents: List[Dict],
        categories: List[str] = ["001", "004", "010"],
    ) -> Tuple[int, int]:
        """
        处理一批文档（多进程）

        Args:
            documents: 文档列表
            categories: 分类列表

        Returns:
            Tuple[int, int]: (成功处理数量, 失败数量)
        """
        if not documents:
            return 0, 0

        log.info(
            f"开始多进程处理 {len(documents)} 个文档，使用 {self.worker_count} 个工作进程"
        )

        # 准备工作进程配置
        worker_config = {
            "model_apikey": self.model_apikey,
            "model_name": self.model_name,
            "model_url": self.model_url,
            "prompt_spec": self.prompt_spec,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "es_index_analysis": self.es_index_analysis,
        }

        # 准备工作任务
        tasks = []
        for doc in documents:
            tasks.append((doc, worker_config))

        success_count = 0
        failure_count = 0
        failed_documents = []

        try:
            # 创建进程池
            with Pool(processes=self.worker_count) as pool:
                # 提交任务并获取结果
                results = pool.map(process_document_worker, tasks)

                # 处理结果
                for i, result in enumerate(results):
                    if result["success"]:
                        success_count += 1
                        # 批量插入分析结果
                        if result["analyzed_docs"]:
                            self._batch_insert_results(result["analyzed_docs"])
                    else:
                        failure_count += 1
                        # 收集失败的文档信息
                        doc = documents[i]
                        failed_documents.append(
                            {
                                "document_id": doc["_id"],
                                "document_title": doc["_source"].get("title", "无标题"),
                                "document_url": doc["_source"].get("url", ""),
                                "failure_reason": result.get("error", "未知错误"),
                            }
                        )

                # 批量添加失败文档到黑名单
                if failed_documents:
                    blacklist_count = self.blacklist_manager.batch_add_to_blacklist(
                        failed_documents
                    )
                    log.info(f"已将 {blacklist_count} 个失败文档添加到黑名单")

                log.info(f"批处理完成: 成功 {success_count}, 失败 {failure_count}")
                return success_count, failure_count

        except Exception as e:
            log.error(f"多进程处理失败: {e}")
            return success_count, failure_count

    def _batch_insert_results(self, analyzed_docs: List[Dict]):
        """
        批量插入分析结果到ES

        Args:
            analyzed_docs: 分析结果列表
        """
        try:
            for i, analyzed_doc in enumerate(analyzed_docs):
                # 生成新的文档ID
                new_doc_id = f"{analyzed_doc['source_id']}_{i}"

                # 字段校验和标准化
                validated_document = validate_and_normalize_fields(analyzed_doc)

                # 插入文档
                insert_document(
                    self.es,
                    self.es_index_analysis,
                    doc_id=new_doc_id,
                    document=validated_document,
                )
                log.debug(f"成功插入文档 {new_doc_id}")

        except Exception as e:
            log.error(f"批量插入结果失败: {e}")

    def process_batch_records(
        self,
        categories: List[str] = ["001", "004", "010"],
        batch_size: int = None,
    ) -> Tuple[int, int]:
        """
        处理一批记录（主要入口函数）

        Args:
            categories: 分类列表
            batch_size: 批量大小

        Returns:
            Tuple[int, int]: (成功处理数量, 失败数量)
        """
        try:
            # 获取一批待处理文档
            documents = self.get_batch_documents(categories, batch_size)

            if not documents:
                log.info("没有找到待处理的文档")
                return 0, 0

            # 多进程处理文档
            return self.process_document_batch(documents, categories)

        except Exception as e:
            log.error(f"处理批量记录失败: {e}")
            return 0, 0


def process_document_worker(task_data: Tuple[Dict, Dict]) -> Dict:
    """
    工作进程函数：处理单个文档

    Args:
        task_data: (文档数据, 工作配置) 元组

    Returns:
        Dict: 处理结果
    """
    doc, config = task_data
    worker_id = mp.current_process().pid

    try:
        # 初始化ES客户端（每个工作进程独立）
        es = init_es_client()

        # 初始化黑名单管理器（启用文件锁定）
        blacklist_manager = BlacklistManager(enable_file_locking=True)

        log.info(f"Worker {worker_id}: 开始处理文档 {doc['_id']}")

        # 检查是否在黑名单中（双重检查）
        if blacklist_manager.is_blacklisted(doc["_id"]):
            log.warning(f"Worker {worker_id}: 文档 {doc['_id']} 在黑名单中，跳过处理")
            return {"success": False, "error": "文档在黑名单中"}

        # 分析文档
        analyzed_docs = analyze_document_worker(doc, config)

        log.info(
            f"Worker {worker_id}: 文档 {doc['_id']} 处理完成，生成 {len(analyzed_docs)} 个结果"
        )

        return {
            "success": True,
            "analyzed_docs": analyzed_docs,
            "worker_id": worker_id,
        }

    except Exception as e:
        error_msg = str(e)
        log.error(f"Worker {worker_id}: 处理文档 {doc['_id']} 失败: {error_msg}")

        return {
            "success": False,
            "error": error_msg,
            "worker_id": worker_id,
        }


def analyze_document_worker(doc: Dict, config: Dict) -> List[Dict]:
    """
    工作进程中的文档分析函数

    Args:
        doc: ES文档
        config: 工作配置

    Returns:
        List[Dict]: 分析结果列表
    """
    title = doc["_source"]["title"]
    html_content = doc["_source"]["response"]
    content = md(html_content)

    # 检查内容长度，避免过长内容导致超时
    max_content_length = 50000  # 最大50K字符
    if len(content) > max_content_length:
        log.warning(
            f"文档内容过长({len(content)}字符)，截取前{max_content_length}字符"
        )
        content = content[:max_content_length] + "\n...(内容已截取)"

    prompt = f"""
    标题：{title}
    内容：
    {content}
    {config['prompt_spec']}
    """

    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": "严格按照系统提示词输出结果，不要胡编乱造"},
    ]

    result = llm(
        messages,
        model_name=config['model_name'],
        model_apikey=config['model_apikey'],
        model_url=config['model_url'],
        timeout=config['timeout'],
        max_retries=config['max_retries'],
    )
    log.info(result)

    # 获取多个JSON字符串
    json_strings = clean_json_markdown(result)
    analyzed_results = []

    for json_str in json_strings:
        try:
            # 处理每个JSON字符串
            llm_result = process_json_data(json_str)
            meta = {
                "source_id": doc["_id"],
                "source_title": doc["_source"].get("title"),
                "source_create_time": doc["_source"].get("create_time"),
                "source_category": doc["_source"].get("category"),
                "source_url": doc["_source"].get("url"),
                "source_appendix": doc["_source"].get("appendix"),
                "insert_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
            if isinstance(llm_result, list):
                for item in llm_result:
                    if isinstance(item, dict):
                        item.update(meta)
                        analyzed_results.append(item)
            else:
                llm_result.update(meta)
                analyzed_results.append(llm_result)

        except json.JSONDecodeError as e:
            log.error(f"JSON解析失败: {e}")
            log.error(f"原始JSON字符串: {json_str}")
            continue

    return analyzed_results


def get_batch_records_mp(
    categories: List[str] = ["001", "004", "010"],
    worker_count: int = None,
    batch_size: int = None,
) -> Tuple[int, int]:
    """
    多进程批量处理记录的主入口函数

    Args:
        categories: 分类列表
        worker_count: 工作进程数量
        batch_size: 批处理大小

    Returns:
        Tuple[int, int]: (成功处理数量, 失败数量)
    """
    try:
        # 初始化ES客户端
        es = init_es_client()

        # 加载.env文件
        load_dotenv()

        # 从环境变量获取配置
        es_index_links = os.getenv("ES_INDEX_LINKS")
        es_index_analysis = os.getenv("ES_INDEX_ANALYSIS_ALIAS")
        model_apikey = os.getenv("MODEL_APIKEY")
        model_name = os.getenv("MODEL_NAME")
        model_url = os.getenv("MODEL_URL")
        prompt_spec = os.getenv("PROMPT_SPEC", "")

        # 创建多进程分析器实例
        analyzer = DocumentAnalyzerMP(
            es_client=es,
            es_index_links=es_index_links,
            es_index_analysis=es_index_analysis,
            model_apikey=model_apikey,
            model_name=model_name,
            model_url=model_url,
            prompt_spec=prompt_spec,
            worker_count=worker_count,
            batch_size=batch_size,
        )

        # 处理一批记录
        return analyzer.process_batch_records(categories, batch_size)

    except Exception as e:
        log.error(f"多进程批量处理失败: {e}")
        return 0, 0
