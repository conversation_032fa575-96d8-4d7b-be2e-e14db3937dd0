#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证release_time字段类型更新结果
"""

import os
from es_deal import init_es_client
from dotenv import load_dotenv
from utils.log_cfg import log


def verify_date_mapping():
    """
    验证日期映射更新结果
    """
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es_client = init_es_client()
        
        alias_name = "markersweb_attachment_analysis_alias"
        
        log.info("=" * 80)
        log.info("验证release_time字段类型更新结果")
        log.info("=" * 80)
        
        # 1. 检查别名指向的索引
        aliases = es_client.indices.get_alias(name=alias_name)
        current_index = list(aliases.keys())[0]
        log.info(f"当前别名指向的索引: {current_index}")
        
        # 2. 检查映射
        mapping = es_client.indices.get_mapping(index=current_index)
        properties = mapping[current_index]["mappings"]["properties"]
        
        if "release_time" in properties:
            release_time_mapping = properties["release_time"]
            log.info(f"release_time字段映射: {release_time_mapping}")
            
            field_type = release_time_mapping.get("type")
            if field_type == "date":
                log.info("✓ release_time字段类型已成功更新为date")
            else:
                log.error(f"✗ release_time字段类型仍为: {field_type}")
        else:
            log.error("✗ release_time字段不存在")
        
        # 3. 检查文档数量
        doc_count = es_client.count(index=alias_name)["count"]
        log.info(f"文档总数: {doc_count}")
        
        # 4. 测试日期查询功能
        log.info("\n测试日期查询功能:")
        
        # 测试范围查询
        range_query = {
            "query": {
                "range": {
                    "release_time": {
                        "gte": "2025-06-01",
                        "lte": "2025-06-30"
                    }
                }
            },
            "size": 0
        }
        
        range_result = es_client.search(index=alias_name, body=range_query)
        range_count = range_result["hits"]["total"]["value"]
        log.info(f"✓ 日期范围查询 (2025-06-01 到 2025-06-30): {range_count} 个文档")
        
        # 测试日期聚合
        agg_query = {
            "query": {"match_all": {}},
            "aggs": {
                "release_dates": {
                    "date_histogram": {
                        "field": "release_time",
                        "calendar_interval": "day"
                    }
                }
            },
            "size": 0
        }
        
        agg_result = es_client.search(index=alias_name, body=agg_query)
        buckets = agg_result["aggregations"]["release_dates"]["buckets"]
        log.info(f"✓ 日期聚合查询: 找到 {len(buckets)} 个不同的日期")
        
        # 显示前几个日期的统计
        if buckets:
            log.info("前5个日期的文档数量:")
            for i, bucket in enumerate(buckets[:5]):
                date_str = bucket["key_as_string"]
                doc_count = bucket["doc_count"]
                log.info(f"  {date_str}: {doc_count} 个文档")
        
        # 5. 抽样检查数据
        log.info("\n抽样检查数据:")
        sample_query = {
            "query": {"match_all": {}},
            "_source": ["release_time", "prj_name"],
            "size": 5
        }
        
        sample_result = es_client.search(index=alias_name, body=sample_query)
        hits = sample_result["hits"]["hits"]
        
        for i, hit in enumerate(hits, 1):
            source = hit["_source"]
            release_time = source.get("release_time", "N/A")
            prj_name = source.get("prj_name", "N/A")[:50]
            log.info(f"  样本{i}: {release_time} | {prj_name}...")
        
        log.info("=" * 80)
        log.info("验证完成！release_time字段已成功更新为date类型")
        log.info("现在可以使用日期范围查询、日期聚合等功能")
        log.info("=" * 80)
        
    except Exception as e:
        log.error(f"验证失败: {e}")
        raise


if __name__ == "__main__":
    verify_date_mapping()
